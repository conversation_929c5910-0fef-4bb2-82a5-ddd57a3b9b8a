syntax = "proto3";

package students;

// Import google protobuf types for Timestamp and BoolValue
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

service StudentService {
  // Student CRUD Operations
  rpc CreateStudent(CreateStudentRequest) returns (StudentResponse) {}
  rpc CreateStudentAfterRegistration(CreateStudentAfterRegistrationRequest)
      returns (StudentResponse) {}
  rpc GetStudent(GetStudentRequest) returns (StudentResponse) {}
   rpc GetStudentByEmail(GetStudentByEmailRequest) returns (StudentResponse) {}
  rpc UpdateStudent(UpdateStudentRequest) returns (StudentResponse) {}
  rpc ListStudents(ListStudentsRequest) returns (ListStudentsResponse) {}
  rpc CreateOrUpdateStudentAcademic(CreateOrUpdateStudentAcademicRequest)
      returns (StudentAcademicResponse) {}
  rpc GetStudentAcademic(GetStudentAcademicRequest) returns (StudentAcademicResponse) {}
   // New: Update student's application type
   rpc UpdateStudentApplicationType(UpdateStudentApplicationTypeRequest) returns (StudentResponse) {}
  
  // University-related operations
  rpc GetUniversityDetails(GetUniversityDetailsRequest) returns (UniversityDetailsResponse) {}
  rpc GetUniversitiesForStudent(GetUniversitiesForStudentRequest) returns (UniversitiesForStudentResponse) {}
  rpc GetCoursesForUniversity(GetCoursesForUniversityRequest) returns (CoursesForUniversityResponse) {}
  rpc GetIntakesForUniversity(GetIntakesForUniversityRequest) returns (IntakesForUniversityResponse) {}
  rpc GetProgramLevelsForUniversity(GetProgramLevelsForUniversityRequest) returns (ProgramLevelsForUniversityResponse) {}
  rpc GetFieldsOfStudyForUniversity(GetFieldsOfStudyForUniversityRequest) returns (FieldsOfStudyForUniversityResponse) {}
  rpc GetCampusesForUniversity(GetCampusesForUniversityRequest) returns (CampusesForUniversityResponse) {}
  rpc GetFeesForUniversity(GetFeesForUniversityRequest) returns (FeesForUniversityResponse) {}
  rpc EnrollInCourseWithValidation(EnrollInCourseWithValidationRequest) returns (EnrollmentResponse) {}
  
  // Application-related operations
  rpc CreateApplication(CreateApplicationRequest) returns (ApplicationResponse) {}
  rpc GetApplication(GetApplicationRequest) returns (ApplicationResponse) {}
  rpc GetApplicationsByStudent(GetApplicationsByStudentRequest) returns (ApplicationsByStudentResponse) {}
  rpc UpdateApplication(UpdateApplicationRequest) returns (ApplicationResponse) {}
  rpc DeleteApplication(DeleteApplicationRequest) returns (DeleteApplicationResponse) {}
  rpc ListApplications(ListApplicationsRequest) returns (ListApplicationsResponse) {}
  rpc GetApplicationProgress(GetApplicationRequest) returns (ApplicationProgressResponse) {}
  rpc GenerateCalDocument(GenerateCalDocumentRequest) returns (GenerateCalDocumentResponse) {}

  // Application Document Requirements
  rpc GetApplicationRequirements(GetApplicationRequirementsRequest) returns (GetApplicationRequirementsResponse) {}
  rpc UpdateApplicationRequirement(UpdateApplicationRequirementRequest) returns (UpdateApplicationRequirementResponse) {}
  rpc CreateApplicationRequirements(CreateApplicationRequirementsRequest) returns (CreateApplicationRequirementsResponse) {}

  // Application Stage Management
  rpc UpdateApplicationStage(UpdateApplicationStageRequest) returns (UpdateApplicationStageResponse) {}

  // Application Notes Management
  rpc CreateApplicationNote(CreateApplicationNoteRequest) returns (CreateApplicationNoteResponse) {}
  rpc GetApplicationNotes(GetApplicationNotesRequest) returns (GetApplicationNotesResponse) {}

  // Application Progress Records Management
  rpc GetApplicationProgressRecords(GetApplicationProgressRecordsRequest) returns (GetApplicationProgressRecordsResponse) {}
  rpc CreateApplicationProgressRecord(CreateApplicationProgressRecordRequest) returns (CreateApplicationProgressRecordResponse) {}

  // Data cleanup operations
  rpc CleanupDuplicateStudents(CleanupDuplicateStudentsRequest) returns (CleanupDuplicateStudentsResponse) {}
  
  // Admission package generation
  rpc GenerateAdmissionPackage(GenerateAdmissionPackageRequest) returns (GenerateAdmissionPackageResponse) {}
  
  // ADM document generation
  rpc GenerateAdmDocument(GenerateAdmDocumentRequest) returns (GenerateAdmDocumentResponse) {}
  
  // Admission Portfolio generation
  rpc GenerateAdmissionPortfolio(GenerateAdmissionPortfolioRequest) returns (GenerateAdmissionPortfolioResponse) {}
  
  // Dashboard data
  rpc GetDashboardDataByCampus(GetDashboardDataByCampusRequest) returns (GetDashboardDataByCampusResponse) {}
}

//----------------------------------------------------------------------------//
// Request & Response Messages
//----------------------------------------------------------------------------//
 
 // New: Update student's application type
 message UpdateStudentApplicationTypeRequest {
   string studentId = 1;
   string applicationType = 2;
 }
message CreateStudentRequest {
  int64 organizationId = 1;
  int64 agencyId = 2;  // nullable
  string firstName = 3;
  string lastName = 4;
  string nameInNative = 5;
  string email = 6;
  string phone = 7;
  string guardianPhone = 8;
  string dateOfBirth = 9;  // YYYY-MM-DD
  string gender = 10;
  string fatherName = 11;
  string motherName = 12;
  string nid = 13;
  string passport = 14;

  Address presentAddress = 15;
  Address permanentAddress = 16;
  MaritalStatus maritalStatus = 17;
  Sponsor sponsor = 18;
  EmergencyContact emergencyContact = 19;

  repeated string preferredSubject = 20;
  repeated string preferredCountry = 21;
  repeated SocialLink socialLinks = 22;

  string reference = 23;
  string note = 24;
  string studentId = 25;
  int64 userId = 26;  // Add userId field to link with auth service user
}

message UpdateStudentRequest {
  string id = 1;
  int64 organizationId = 2;
  int64 agencyId = 3;  // nullable
  string firstName = 4;
  string lastName = 5;
  string nameInNative = 6;
  string email = 7;
  string phone = 8;
  string guardianPhone = 9;
  string dateOfBirth = 10;  // YYYY-MM-DD
  string gender = 11;
  string fatherName = 12;
  string motherName = 13;
  string nid = 14;
  string passport = 15;

  Address presentAddress = 16;
  Address permanentAddress = 17;
  MaritalStatus maritalStatus = 18;
  Sponsor sponsor = 19;
  EmergencyContact emergencyContact = 20;

  repeated string preferredSubject = 21;
  repeated string preferredCountry = 22;
  repeated SocialLink socialLinks = 23;

  string reference = 24;
  string note = 25;
  string studentId = 26;
  int64 userId = 27;  // Add userId field to link with auth service user
}

message CreateStudentAfterRegistrationRequest {
  // The user’s internal ID (as a string)
  string user_id = 1;

  // Basic profile fields
  string name = 2;
  string email = 3;
  string phone = 4;
  string nationality = 5;

  // Context from auth flow
  string organizationId = 6;
  string roleName = 7;
  string departmentName = 8;
}

message CreateOrUpdateStudentAcademicRequest {
  string studentId = 1;
  repeated StudentAcademicRecord academic = 2;
  repeated StudentProficiencyRecord proficiency = 3;
  repeated StudentPublication publications = 4;
  repeated StudentSocialLink socialLinks = 5;
  repeated StudentOtherActivity otherActivities = 6;
}

message GetStudentAcademicRequest {
  string studentId = 1;
}

message StudentAcademicResponse {
  bool isActive = 1;
  int64 id = 2;
  string studentId = 3;
  repeated StudentAcademicRecord academicRecords = 4;
  repeated StudentProficiencyRecord proficiencyRecords = 5;
  repeated StudentPublication publicationRecords = 6;
  repeated StudentOtherActivity otherActivities = 7;
}

//----------------------------------------------------------------------------//
// Domain Messages
//----------------------------------------------------------------------------//
message Student {
  int64 userId = 1;
  string studentId = 2;
  string status = 3;
  int64 organizationId = 4;
  string agencyId = 5;
  string metadata = 6;
  StudentPersonalInfo personalInfo = 7;
  repeated StudentAcademicRecord academic_records = 8;
  repeated StudentProficiencyRecord proficiency_records = 9;
  repeated StudentPublication publications = 10;
  repeated StudentSocialLink socialLinks = 11;
  repeated StudentOtherActivity otherActivities = 12;
  repeated Enrollment enrollments = 13;
  repeated StudentAcademicBackground academicBackgrounds = 14;
  repeated Grade grades = 15;
  repeated EmergencyContact emergencyContacts = 16;
   // New: student's application type (enum enforced at DB level)
   string applicationType = 17;
}

message StudentPersonalInfo {
  int64 studentId = 1;
  string firstName = 2;
  string lastName = 3;
  string nameInNative = 4;
  string email = 5;
  string phone = 6;
  string guardianPhone = 7;
  string dateOfBirth = 8;
  string gender = 9;
  string fatherName = 10;
  string motherName = 11;
  string nid = 12;
  string passport = 13;

  Address presentAddress = 14;
  Address permanentAddress = 15;
  MaritalStatus maritalStatus = 16;
  Sponsor sponsor = 17;
  EmergencyContact emergencyContact = 18;

  repeated string preferredSubject = 19;
  repeated string preferredCountry = 20;
  repeated SocialLink socialLinks = 21;

  string reference = 22;
  string note = 23;
  bool isPersonalInfoEdited = 24;

  // metadata
  string id = 25;
  string createdAt = 26;
  string updatedAt = 27;
}

//----------------------------------------------------------------------------//
// Nested Types
//----------------------------------------------------------------------------//
message Address {
  string address = 1;
  string country = 2;
  string state = 3;
  string city = 4;
  string postalCode = 5;
}

message MaritalStatus {
  string status = 1;
  string spouseName = 2;
  string spousePhone = 3;
  string spousePassport = 4;
}

message Sponsor {
  string name = 1;
  string relation = 2;
  string phone = 3;
  string email = 4;
  string nationalId = 5;
}

message EmergencyContact {
  string lastName = 1;
  string middleName = 2;
  string firstName = 3;
  string phoneHome = 4;
  string phoneMobile = 5;
  string relation = 6;
}

message SocialLink {
  string platform = 1;
  string url = 2;
}

message GetStudentRequest {
  string id = 1;
}
 message GetStudentByEmailRequest {
   string email = 1;
 }

message StudentResponse {
  Student student = 1;
}

message ListStudentsRequest {
  int32 page = 1;
  int32 limit = 2;
  string filter = 3;
  string orderBy = 4;
}

message ListStudentsResponse {
  repeated Student students = 1;
  int32 total = 2;
}

//----------------------------------------------------------------------------//
// Missing Proto Messages
//----------------------------------------------------------------------------//

message StudentPublication {
  int64 studentId = 1;
  string subject = 2;
  string journal = 3;
  string publicationDate = 4;
  string link = 5;
  string title = 6;
  string abstract = 7;
  repeated string authors = 8;
  string publicationType = 9;
  string doi = 10;
  string isbn = 11;
  string issn = 12;
  string volume = 13;
  string issue = 14;
  string pages = 15;
  string publisher = 16;
  string conferenceLocation = 17;
  string conferenceDate = 18;
  repeated string keywords = 19;
  double impactFactor = 20;
  int32 citationCount = 21;
  string status = 22;
  bool isPeerReviewed = 23;
  bool isOpenAccess = 24;
  repeated string documents = 25;
  string notes = 26;
  string metadata = 27;

  // metadata
  string id = 28;
  string createdAt = 29;
  string updatedAt = 30;
}

message StudentSocialLink {
  int64 studentId = 1;
  string platform = 2;
  string url = 3;
  string username = 4;
  string displayName = 5;
  bool isPublic = 6;
  bool isActive = 7;
  bool isPrimary = 8;
  bool isProfessional = 9;
  string description = 10;
  int32 followerCount = 11;
  int32 followingCount = 12;
  string lastVerified = 13;
  bool isVerified = 14;
  string notes = 15;
  string metadata = 16;

  // metadata
  string id = 17;
  string createdAt = 18;
  string updatedAt = 19;
}

message StudentOtherActivity {
  int64 studentId = 1;
  string subject = 2;
  string certificationLink = 3;
  string startDate = 4;
  string endDate = 5;
  string title = 6;
  string description = 7;
  string activityType = 8;
  string organization = 9;
  string instructor = 10;
  string location = 11;
  int32 durationHours = 12;
  string certificateNumber = 13;
  string grade = 14;
  double score = 15;
  bool isCertified = 16;
  bool isOngoing = 17;
  string certificationExpiryDate = 18;
  repeated string skills = 19;
  repeated string technologies = 20;
  double cost = 21;
  string currency = 22;
  bool isSponsored = 23;
  string sponsor = 24;
  repeated string documents = 25;
  string notes = 26;
  string metadata = 27;

  // metadata
  string id = 28;
  string createdAt = 29;
  string updatedAt = 30;
}

message StudentAcademicRecord {
  int64 studentId = 1;
  bool foreignDegree = 2;
  string nameOfExam = 3;
  string institute = 4;
  string subject = 5;
  string board = 6;
  string grade = 7;
  string passingYear = 8;
  double cgpa = 9;
  double percentage = 10;
  string division = 11;
  string rollNumber = 12;
  string registrationNumber = 13;
  string examDate = 14;
  string certificateNumber = 15;
  string notes = 16;
  repeated string documents = 17;
  string metadata = 18;

  // metadata
  string id = 19;
  string createdAt = 20;
  string updatedAt = 21;
}

message StudentProficiencyRecord {
  int64 studentId = 1;
  string nameOfExam = 2;
  ProficiencyScore score = 3;
  string examDate = 4;
  string expiryDate = 5;
  string note = 6;
  string testCenter = 7;
  string candidateNumber = 8;
  string certificateNumber = 9;
  string testType = 10;
  string band = 11;
  string level = 12;
  repeated string documents = 13;
  string metadata = 14;

  // metadata
  string id = 15;
  string createdAt = 16;
  string updatedAt = 17;
}

message ProficiencyScore {
  double overall = 1;
  double R = 2;  // Reading
  double L = 3;  // Listening
  double W = 4;  // Writing
  double S = 5;  // Speaking
}

message Enrollment {
  int64 studentId = 1;
  string course_id = 2;
  string course_name = 3;
  string course_code = 4;
  int32 credits = 5;
  string semester = 6;
  int32 academic_year = 7;
  string status = 8;
  string enrollment_date = 9;
  string completion_date = 10;
  string drop_date = 11;
  string instructor_id = 12;
  string instructor_name = 13;
  EnrollmentSchedule schedule = 14;
  string metadata = 15;

  // metadata
  string id = 16;
  string createdAt = 17;
  string updatedAt = 18;
}

message EnrollmentSchedule {
  repeated string days = 1;
  string start_time = 2;
  string end_time = 3;
  string location = 4;
}

message StudentAcademicBackground {
  int64 studentId = 1;
  repeated AcademicRecord academic_records = 2;
  repeated ProficiencyRecord proficiencyRecords = 3;
  repeated PublicationRecord publicationRecords = 4;
  OtherActivity otherActivities = 5;

  // metadata
  string id = 6;
  string createdAt = 7;
  string updatedAt = 8;
}

message AcademicRecord {
  bool foreign_degree = 1;
  string name_of_exam = 2;
  string institute = 3;
  string subject = 4;
  string board = 5;
  string grade = 6;
  string passing_year = 7;
}

message ProficiencyRecord {
  string name_of_exam = 1;
  ProficiencyScore score = 2;
  string exam_date = 3;
  string expiry_date = 4;
  string note = 5;
}

message PublicationRecord {
  string subject = 1;
  string journal = 2;
  string publication_date = 3;
  string link = 4;
}

message OtherActivity {
  string subject = 1;
  string certification_link = 2;
  string start_date = 3;
  string end_date = 4;
}

message Grade {
  int64 studentId = 1;
  int64 enrollment_id = 2;
  string course_id = 3;
  string course_name = 4;
  string course_code = 5;
  string letter_grade = 6;
  double grade_points = 7;
  double percentage = 8;
  int32 credits = 9;
  string semester = 10;
  int32 academic_year = 11;
  string grade_type = 12;
  string grade_date = 13;
  string instructor_id = 14;
  string instructor_name = 15;
  string comments = 16;
  GradeBreakdown breakdown = 17;
  string metadata = 18;

  // metadata
  string id = 19;
  string createdAt = 20;
  string updatedAt = 21;
}

message GradeBreakdown {
  double assignments = 1;
  double quizzes = 2;
  double midterm = 3;
  double final = 4;
  double participation = 5;
  double projects = 6;
}

message ContactAddress {
  string street = 1;
  string city = 2;
  string state = 3;
  string postal_code = 4;
  string country = 5;
}

//----------------------------------------------------------------------------//
// University-related Request & Response Messages
//----------------------------------------------------------------------------//

message GetUniversityDetailsRequest {
  int64 universityId = 1;
}

message UniversityDetailsResponse {
  bool success = 1;
  string message = 2;
  UniversityDetails university = 3;
}

message UniversityDetails {
  int64 id = 1;
  string name = 2;
  string address = 3;
  string country = 4;
  string state = 5;
  string city = 6;
  string type = 7;
  bool isActive = 8;
}

message GetUniversitiesForStudentRequest {
  string studentId = 1;
}

message UniversitiesForStudentResponse {
  bool success = 1;
  string message = 2;
  repeated UniversityDetails universities = 3;
}

message GetCoursesForUniversityRequest {
  int64 universityId = 1;
}

message CoursesForUniversityResponse {
  bool success = 1;
  string message = 2;
  repeated CourseDetails courses = 3;
}

message CourseDetails {
  int64 id = 1;
  string name = 2;
  string code = 3;
  string description = 4;
  int32 credits = 5;
  string level = 6;
}

message GetIntakesForUniversityRequest {
  int64 universityId = 1;
}

message IntakesForUniversityResponse {
  bool success = 1;
  string message = 2;
  repeated IntakeDetails intakes = 3;
}

message IntakeDetails {
  int64 id = 1;
  string name = 2;
  string startDate = 3;
  string endDate = 4;
  string status = 5;
}

message GetProgramLevelsForUniversityRequest {
  int64 universityId = 1;
}

message ProgramLevelsForUniversityResponse {
  bool success = 1;
  string message = 2;
  repeated ProgramLevelDetails programLevels = 3;
}

message ProgramLevelDetails {
  int64 id = 1;
  string name = 2;
  string level = 3;
  string description = 4;
}

message GetFieldsOfStudyForUniversityRequest {
  int64 universityId = 1;
}

message FieldsOfStudyForUniversityResponse {
  bool success = 1;
  string message = 2;
  repeated FieldOfStudyDetails fieldsOfStudy = 3;
}

message FieldOfStudyDetails {
  int64 id = 1;
  string name = 2;
  string description = 3;
}

message GetCampusesForUniversityRequest {
  int64 universityId = 1;
}

message CampusesForUniversityResponse {
  bool success = 1;
  string message = 2;
  repeated CampusDetails campuses = 3;
}

message CampusDetails {
  int64 id = 1;
  string name = 2;
  string address = 3;
  string city = 4;
  string state = 5;
  string country = 6;
}

message GetFeesForUniversityRequest {
  int64 universityId = 1;
}

message FeesForUniversityResponse {
  bool success = 1;
  string message = 2;
  repeated FeeDetails fees = 3;
}

message FeeDetails {
  int64 id = 1;
  string name = 2;
  double amount = 3;
  string currency = 4;
  string type = 5;
  string description = 6;
}

message EnrollInCourseWithValidationRequest {
  string studentId = 1;
  string courseId = 2;
  string semester = 3;
  int64 universityId = 4;
}

message EnrollmentResponse {
  bool success = 1;
  string message = 2;
  EnrollmentDetails enrollment = 3;
}

message EnrollmentDetails {
  int64 id = 1;
  string studentId = 2;
  string courseId = 3;
  string courseName = 4;
  string courseCode = 5;
  int32 credits = 6;
  string semester = 7;
  string academicYear = 8;
  string status = 9;
  string enrollmentDate = 10;
  string completionDate = 11;
  string dropDate = 12;
  string instructorId = 13;
  string instructorName = 14;
  string schedule = 15;
  string metadata = 16;
}

// Application-related messages

// Request message for bulk create
message CreateApplicationRequest {
  repeated CreateApplication applications = 1;
}
message CreateApplication {
  string studentId = 1;
  int64 universityId = 2;
  int64 universityCountryId = 3;
  int64 universityCountryCampus = 4;
  int64 programId = 5;
  int64 intakeId = 6;
  int64 courseId = 7;
  string note = 8;
  string status = 9;
  string paymentStatus = 10;
  string applicationType = 11;
  // Enhanced fields
  string applicationId = 12;
  string currentStage = 13;
  int32 overallProgress = 14;
  double totalAmount = 15;
  double paidAmount = 16;
  double refundAmount = 17;
  string deliveryMethod = 18;
  string submissionDate = 19;
  string deadlineDate = 20;
  // Payment-related fields
  string paymentInvoiceUrl = 21;
  string paymentMethod = 22;
  // ApplyGoal acceptance fields
  bool applicationAcceptedByApplyGoal = 23;
  string applicationAcceptedByApplyGoalAt = 24;
  int64 applicationAcceptedByApplyGoalUserId = 25;
}

message GetApplicationRequest {
  int64 id = 1;
}

message ApplicationResponse {
  int32 status = 1;
  string message = 2;
  repeated ApplicationData data = 3;
  string error = 4;
}

message ApplicationData {
  int64 id = 1;
  string studentId = 2;
  int64 universityId = 3;
  int64 universityCountryId = 4;
  int64 universityCountryCampus = 5;
  int64 programId = 6;
  int64 intakeId = 7;
  int64 courseId = 8;
  string note = 9;
  string status = 10;
  string paymentStatus = 11;
  string applicationType = 12;
  // Enhanced fields
  string applicationId = 13;
  string currentStage = 14;
  int32 overallProgress = 15;
  double totalAmount = 16;
  double paidAmount = 17;
  double refundAmount = 18;
  string deliveryMethod = 19;
  string submissionDate = 20;
  string deadlineDate = 21;
  // Payment-related fields
  string paymentInvoiceUrl = 22;
  string paymentMethod = 23;
  string appliedAt = 24;
  string updatedAt = 25;
  string createdAt = 26;
  // ApplyGoal acceptance fields
  bool applicationAcceptedByApplyGoal = 27;
  string applicationAcceptedByApplyGoalAt = 28;
  int64 applicationAcceptedByApplyGoalUserId = 29;
  // Immigration document fields
  string savisId = 30;
  string i20Url = 31;
  string i94Url = 32;
  string i797cUrl = 33;
  string calUrl = 34;
  string admUrl = 35;
  string apUrl = 36;
}

message GetApplicationsByStudentRequest {
  string studentId = 1;
}

message ApplicationsByStudentResponse {
  int32 status = 1;
  string message = 2;
  ApplicationsData data = 3;
  string error = 4;
}

message ApplicationsData {
  repeated ApplicationData applications = 1;
}

message UpdateApplicationRequest {
  int64 id = 1;
  string studentId = 2;
  int64 universityId = 3;
  int64 universityCountryId = 4;
  int64 universityCountryCampus = 5;
  int64 programId = 6;
  int64 intakeId = 7;
  int64 courseId = 8;
  string note = 9;
  string status = 10;
  string paymentStatus = 11;
  string applicationType = 12;
  // Enhanced fields
  string applicationId = 13;
  string currentStage = 14;
  int32 overallProgress = 15;
  double totalAmount = 16;
  double paidAmount = 17;
  double refundAmount = 18;
  string deliveryMethod = 19;
  string submissionDate = 20;
  string deadlineDate = 21;
  // Payment-related fields
  string paymentInvoiceUrl = 22;
  string paymentMethod = 23;
  // ApplyGoal acceptance fields
  bool applicationAcceptedByApplyGoal = 24;
  string applicationAcceptedByApplyGoalAt = 25;
  int64 applicationAcceptedByApplyGoalUserId = 26;
  // Immigration document fields
  string savisId = 27;
  string i20Url = 28;
  string i94Url = 29;
  string i797cUrl = 30;
  string calUrl = 31;
  string admUrl = 32;
  string apUrl = 33;
}

message DeleteApplicationRequest {
  int64 id = 1;
}

message DeleteApplicationResponse {
  int32 status = 1;
  string message = 2;
  string error = 3;
}

message ListApplicationsRequest {
  int32 page = 1;
  int32 limit = 2;
  string studentId = 3;
  string status = 4;
  string paymentStatus = 5;
  string applicationType = 6;
  string sortBy = 7;
  string sortOrder = 8;
  int64 universityId = 9;
  int64 programId = 10;
  int64 intakeId = 11;
  int64 courseId = 12;
  int64 campusId = 13; // maps to universityCountryCampus
}

message ListApplicationsResponse {
  int32 status = 1;
  string message = 2;
  ListApplicationsData data = 3;
  string error = 4;
}

message ListApplicationsData {
  repeated ApplicationData applications = 1;
  int32 total = 2;
  int32 page = 3;
  int32 limit = 4;
}

message ApplicationProgressResponse {
  int32 status = 1;
  string message = 2;
  ApplicationProgressData data = 3;
  string error = 4;
}

message ApplicationProgressData {
  ApplicationData application = 1;
  ApplicationProgress progress = 2;
}

// Application Document Requirements messages
message ApplicationDocumentRequirementItem {
  int64 id = 1;
  int64 applicationId = 2;
  string documentTitle = 3;
  string documentName = 4;
  string url = 5;
  string documentDescription = 6;
  bool isRequired = 7;
  repeated string allowedFormats = 8;
  string documentStatus = 9;
  string requiredByDate = 10;
  string uploadedAt = 11;
  string verifiedAt = 12;
  int64 verifiedBy = 13;
  google.protobuf.BoolValue acceptedByApplyGoal = 14;
  int64 acceptedByApplyGoalUserId = 15;
  bool acceptedByUniversity = 16;
  int64 acceptedByUniversityUserId = 17;
  string rejectionReason = 18;
  string createdAt = 19;
  string updatedAt = 20;
}

message GetApplicationRequirementsRequest { int64 applicationId = 1; }

message ApplicationRequirementsData {
  repeated ApplicationDocumentRequirementItem applicationRequirements = 1;
  repeated ApplicationStageData applicationStages = 2;
}

message GetApplicationRequirementsResponse {
  int32 status = 1;
  string message = 2;
  ApplicationRequirementsData data = 3;
  string error = 4;
}

message CreateApplicationRequirementItem {
  string documentTitle = 1;
  string documentName = 2;
  string url = 3;
  string documentDescription = 4;
  bool isRequired = 5;
  repeated string allowedFormats = 6;
  string requiredByDate = 7; // ISO date
}

message CreateApplicationRequirementsRequest {
  int64 applicationId = 1;
  repeated CreateApplicationRequirementItem items = 2;
  string studentId = 3;
}

message CreateApplicationRequirementsResponse {
  int32 status = 1;
  string message = 2;
  repeated ApplicationDocumentRequirementItem data = 3;
  string error = 4;
}

message UpdateApplicationRequirementRequest {
  int64 applicationId = 1;
  int64 requirementId = 2;
  google.protobuf.BoolValue acceptedByApplyGoal = 3;
  bool acceptedByUniversity = 4;
  int64 acceptedByApplyGoalUserId = 5;
  int64 acceptedByUniversityUserId = 6;
  string rejectionReason = 7;
  string status = 8; // optional, e.g., requested, uploaded, approved, rejected
  string url = 9;
  
}

message UpdateApplicationRequirementResponse {
  int32 status = 1;
  string message = 2;
  ApplicationDocumentRequirementItem data = 3;
  string error = 4;
}

// Application Stage Update messages
message UpdateApplicationStageRequest {
  int64 applicationId = 1;
  int64 stageId = 2;
  string status = 3; // pending, in_progress, completed, failed
}

message UpdateApplicationStageResponse {
  int32 status = 1;
  string message = 2;
  ApplicationStageData data = 3;
  string error = 4;
}

// Application Notes messages
message CreateApplicationNoteRequest {
  int64 applicationId = 1;
  string noteType = 2; // internal, student, university
  string title = 3;
  string content = 4;
  int64 createdBy = 5;
  bool isPrivate = 6;
  string userInfo = 7; // JSON string containing user information
  optional int64 replyId = 8; // Parent note ID for replies
}

message CreateApplicationNoteResponse {
  int32 status = 1;
  string message = 2;
  ApplicationNoteData data = 3;
  string error = 4;
}

message GetApplicationNotesRequest {
  int64 applicationId = 1;
  string noteType = 2; // optional filter by noteType
}

message GetApplicationNotesResponse {
  int32 status = 1;
  string message = 2;
  ApplicationNotesListData data = 3;
  string error = 4;
}

// Parent note data for replies (simplified to avoid circular reference)
message ParentNoteData {
  int64 id = 1;
  string title = 2;
  string content = 3;
  int64 createdBy = 4;
  string createdAt = 5;
}

message ApplicationNoteData {
  int64 id = 1;
  int64 applicationId = 2;
  string noteType = 3;
  string title = 4;
  string content = 5;
  int64 createdBy = 6;
  bool isPrivate = 7;
  string userInfo = 8; // JSON string
  string createdAt = 9;
  string updatedAt = 10;
  optional int64 replyId = 11; // Parent note ID for replies
  optional ParentNoteData parentNote = 12; // Parent note information
}

message ApplicationNotesListData {
  repeated ApplicationNoteData notes = 1;
  int32 total = 2;
}

// Application Progress Record messages
message GetApplicationProgressRecordsRequest {
  int64 applicationId = 1;
  string recordType = 2; // optional filter by recordType
}

message GetApplicationProgressRecordsResponse {
  int32 status = 1;
  string message = 2;
  ApplicationProgressRecordsListData data = 3;
  string error = 4;
}

message CreateApplicationProgressRecordRequest {
  int64 applicationId = 1;
  string recordType = 2; // document_upload, status_change, payment, offer_letter, etc.
  string title = 3;
  string description = 4;
  string status = 5; // completed, in_progress, failed
  string recordDate = 6; // ISO date string
  double amount = 7; // optional for payment records
  string currency = 8; // optional, default USD
  string proofLinks = 9; // JSON string array of URLs
  string attachments = 10; // JSON string array of file paths
  int64 createdBy = 11;
  string applicationStage = 12; // current application stage title
  string applicationStep = 13; // current application step title
}

message CreateApplicationProgressRecordResponse {
  int32 status = 1;
  string message = 2;
  ApplicationProgressRecordData data = 3;
  string error = 4;
}

message ApplicationProgressRecordData {
  int64 id = 1;
  int64 applicationId = 2;
  string recordType = 3;
  string title = 4;
  string description = 5;
  string status = 6;
  string recordDate = 7;
  double amount = 8;
  string currency = 9;
  string proofLinks = 10; // JSON string array
  string attachments = 11; // JSON string array
  int64 createdBy = 12;
  string createdAt = 13;
  string updatedAt = 14;
}

message ApplicationProgressRecordsListData {
  repeated ApplicationProgressRecordData records = 1;
  int32 total = 2;
}

message ApplicationProgress {
  int32 totalStages = 1;
  int32 completedStages = 2;
  int32 overallProgress = 3;
  string currentStage = 4;
  repeated ApplicationStageData stages = 5;
}

message ApplicationStageData {
  int64 id = 1;
  string stageName = 2;
  int32 stageOrder = 3;
  string status = 4;
  int32 progressPercentage = 5;
  string startDate = 6;
  string completedDate = 7;
  int32 requirementsMet = 8;
  int32 totalRequirements = 9;
}

// CAL Document Generation
message GenerateCalDocumentRequest {
  int64 applicationId = 1;
  string outputFormat = 2; // "pdf" or "html"
  bool includeAllPages = 3; // true to include all pages, false for specific page
  repeated int32 pageNumbers = 4; // specific page numbers to include (optional)
}

message GenerateCalDocumentResponse {
  int32 status = 1;
  string message = 2;
  CalDocumentData data = 3;
  string error = 4;
}

message CalDocumentData {
  bytes documentContent = 1; // PDF or HTML content
  string filename = 2;
  string contentType = 3; // "application/pdf" or "text/html"
  int64 fileSize = 4;
  string generatedAt = 5;
  string minioUrl = 6; // MinIO URL for the uploaded document
  string objectKey = 7; // MinIO object key
}

// Data cleanup messages
message CleanupDuplicateStudentsRequest {
  // Empty request - no parameters needed
}

message CleanupDuplicateStudentsResponse {
  bool success = 1;
  string message = 2;
  CleanupDuplicateStudentsData data = 3;
  string error = 4;
}

message CleanupDuplicateStudentsData {
  int32 merged = 1;
  int32 deleted = 2;
}

// Admission package generation messages
message GenerateAdmissionPackageRequest {
  string studentId = 1;
  int64 applicationId = 2;
  string templateType = 3;  // 'pdf' or 'html'
  string outputFormat = 4;
  bool includeAllPages = 5;
  repeated int32 pageNumbers = 6;
}

message GenerateAdmissionPackageResponse {
  int32 status = 1;
  string message = 2;
  GenerateAdmissionPackageData data = 3;
  string error = 4;
}

message GenerateAdmissionPackageData {
  bytes documentContent = 1;
  string filename = 2;
  string contentType = 3;
  int32 fileSize = 4;
  string generatedAt = 5;
  string minioUrl = 6;
  string objectKey = 7;
  string templateType = 8;
}

// ADM document generation messages
message GenerateAdmDocumentRequest {
  string studentId = 1;
  int64 applicationId = 2;
  string templateType = 3;  // 'pdf' or 'html'
  string outputFormat = 4;
  bool includeAllPages = 5;
  repeated int32 pageNumbers = 6;
}

message GenerateAdmDocumentResponse {
  int32 status = 1;
  string message = 2;
  GenerateAdmDocumentData data = 3;
  string error = 4;
}

message GenerateAdmDocumentData {
  bytes documentContent = 1;
  string filename = 2;
  string contentType = 3;
  int32 fileSize = 4;
  string generatedAt = 5;
  string minioUrl = 6;
  string objectKey = 7;
  string templateType = 8;
}

// Admission Portfolio generation messages
message GenerateAdmissionPortfolioRequest {
  string studentId = 1;
  int64 applicationId = 2;
  string templateType = 3;  // 'pdf' or 'html'
  string outputFormat = 4;
  bool includeAllPages = 5;
  repeated int32 pageNumbers = 6;
}

message GenerateAdmissionPortfolioResponse {
  int32 status = 1;
  string message = 2;
  GenerateAdmissionPortfolioData data = 3;
  string error = 4;
}

message GenerateAdmissionPortfolioData {
  bytes documentContent = 1;
  string filename = 2;
  string contentType = 3;
  int32 fileSize = 4;
  string generatedAt = 5;
  string minioUrl = 6;
  string objectKey = 7;
  string templateType = 8;
}

// Dashboard data messages
message GetDashboardDataByCampusRequest {
  int64 campusId = 1;
}

message GetDashboardDataByCampusResponse {
  int32 status = 1;
  string message = 2;
  DashboardData data = 3;
  string error = 4;
}

message DashboardData {
  int32 totalApplications = 1;
  int32 pendingApplications = 2;
  int32 calGenerated = 3;
  int32 admissionPortfolioGenerated = 4;
  int64 campusId = 5;
}