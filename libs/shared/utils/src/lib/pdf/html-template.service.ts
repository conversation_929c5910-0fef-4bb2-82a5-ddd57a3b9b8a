import { Injectable, Logger } from '@nestjs/common';
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';
import { PDFDocument } from 'pdf-lib';

export interface HtmlCalDocumentData {
  applicationId: number;
  name: string;
  studentName: string;
  idNumber: string;
  passport: string;
  dateOfBirth: string;
  courseName: string;
  intake: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
  issueDate: string;
}

export interface HtmlAdmissionPackageData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
}

export interface HtmlAdmDocumentData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  admNumber: string;
  admDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
}

export interface HtmlAdmissionPortfolioData {
  studentId: string;
  studentName: string;
  applicationType: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  passport: string;
  nid: string;
  presentAddress: string;
  permanentAddress: string;
  preferredSubject: string[];
  preferredCountry: string[];
  issueDate: string;
  fatherName: string;
  motherName: string;
  gender: string;
  maritalStatus: string;
  sponsor: string;
  emergencyContact: string;
  portfolioNumber: string;
  portfolioDate: string;
  universityName: string;
  courseName: string;
  intakeName: string;
  campusName: string;
  tuitionFee: string;
  courseStartDate: string;
  courseEndDate: string;
}

export interface HtmlTemplateOptions {
  includeAllPages?: boolean;
  pageNumbers?: number[];
}

export interface HtmlGenerationResult {
  success: boolean;
  documentContent?: Buffer;
  contentType?: string;
  error?: string;
}

@Injectable()
export class HtmlTemplateService {
  private readonly logger = new Logger(HtmlTemplateService.name);

  /**
   * Get standardized A4 CSS styles for perfect PDF formatting
   */
  private getA4Styles(): string {
    return `
      /* Perfect A4 page setup */
      html, body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
        width: 210mm;
        height: 297mm;
        max-width: 210mm;
        max-height: 297mm;
        overflow: hidden;
        box-sizing: border-box;
      }

      @media print {
        html, body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          width: 210mm;
          height: 297mm;
          margin: 0;
          padding: 0;
        }
        @page {
          size: A4;
          margin: 0;
        }
      }

      /* Global A4 optimizations */
      * {
        box-sizing: border-box !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
      }

      /* Fix image sizing for A4 */
      img {
        max-width: 100%;
        height: auto;
        display: block;
        object-fit: contain;
      }

      /* Ensure all content fits within A4 bounds */
      body > * {
        max-width: 210mm !important;
        max-height: 297mm !important;
      }

      /* Auto-scale content if needed */
      .auto-scale {
        transform-origin: top left;
        width: 100%;
        height: 100%;
      }
    `;
  }

  async generateCalDocument(
    templateData: HtmlCalDocumentData,
    options: HtmlTemplateOptions = {}
  ): Promise<HtmlGenerationResult> {
    try {
      this.logger.log(
        `Generating CAL document for application ${templateData.applicationId}`
      );

      // Get available HTML templates
      const templates = await this.getAvailableHtmlTemplates();
      if (templates.length === 0) {
        throw new Error('No HTML templates found');
      }

      // Determine which pages to include
      const pagesToInclude = this.determinePagesToInclude(templates, options);
      this.logger.debug(
        `Including pages: ${pagesToInclude.map((p) => p.name).join(', ')}`
      );

      // Launch Puppeteer
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      try {
        const page = await browser.newPage();

        // Set viewport for A4 rendering (210mm x 297mm at 96 DPI)
        await page.setViewport({ width: 794, height: 1123, deviceScaleFactor: 1 });

        // Generate PDF from HTML templates
        const pdfBuffer = await this.generatePdfFromHtmlTemplates(
          page,
          pagesToInclude,
          templateData
        );

        this.logger.log(
          `CAL document generated successfully, size: ${pdfBuffer.length} bytes`
        );

        return {
          success: true,
          documentContent: pdfBuffer,
          contentType: 'application/pdf'
        };
      } finally {
        await browser.close();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating CAL document: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined
      );
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async generateAdmissionPackage(
    templateData: HtmlAdmissionPackageData,
    options: HtmlTemplateOptions = {}
  ): Promise<HtmlGenerationResult> {
    try {
      this.logger.log(
        `Generating admission package for student ${templateData.studentId}`
      );

      // Get available HTML templates for admission package
      const templates = await this.getAvailableAdmissionPackageTemplates(
        templateData.applicationType
      );
      if (templates.length === 0) {
        throw new Error(
          `No HTML templates found for application type: ${templateData.applicationType}`
        );
      }

      // Launch Puppeteer
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      try {
        const page = await browser.newPage();

        // Set viewport for A4 rendering (210mm x 297mm at 96 DPI)
        await page.setViewport({ width: 794, height: 1123, deviceScaleFactor: 1 });

        // Generate PDF from HTML templates
        const pdfBuffer =
          await this.generateAdmissionPackagePdfFromHtmlTemplates(
            page,
            templates,
            templateData
          );

        this.logger.log(
          `Admission package generated successfully, size: ${pdfBuffer.length} bytes`
        );

        return {
          success: true,
          documentContent: pdfBuffer,
          contentType: 'application/pdf'
        };
      } finally {
        await browser.close();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating admission package: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined
      );
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async generateAdmDocument(
    templateData: HtmlAdmDocumentData,
    options: HtmlTemplateOptions = {}
  ): Promise<HtmlGenerationResult> {
    try {
      this.logger.log(
        `Generating ADM document for student ${templateData.studentId}`
      );

      // Get available HTML templates for ADM document
      const templates = await this.getAvailableAdmDocumentTemplates(
        templateData.applicationType
      );
      if (templates.length === 0) {
        throw new Error(
          `No HTML templates found for application type: ${templateData.applicationType}`
        );
      }

      // Launch Puppeteer
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      try {
        const page = await browser.newPage();

        // Set viewport for A4 rendering (210mm x 297mm at 96 DPI)
        await page.setViewport({ width: 794, height: 1123, deviceScaleFactor: 1 });

        // Generate PDF from HTML templates
        const pdfBuffer = await this.generateAdmDocumentPdfFromHtmlTemplates(
          page,
          templates,
          templateData
        );

        this.logger.log(
          `ADM document generated successfully, size: ${pdfBuffer.length} bytes`
        );

        return {
          success: true,
          documentContent: pdfBuffer,
          contentType: 'application/pdf'
        };
      } finally {
        await browser.close();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating ADM document: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined
      );
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async generateAdmissionPortfolio(
    templateData: HtmlAdmissionPortfolioData,
    options: HtmlTemplateOptions = {}
  ): Promise<HtmlGenerationResult> {
    try {
      this.logger.log(
        `Generating admission portfolio for student ${templateData.studentId}`
      );

      // Get available HTML templates for admission portfolio
      const templates = await this.getAvailableAdmissionPortfolioTemplates(
        templateData.applicationType
      );
      if (templates.length === 0) {
        throw new Error(
          `No HTML templates found for application type: ${templateData.applicationType}`
        );
      }

      // Launch Puppeteer
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      try {
        const page = await browser.newPage();

        // Set viewport for A4 rendering (210mm x 297mm at 96 DPI)
        await page.setViewport({ width: 794, height: 1123, deviceScaleFactor: 1 });

        // Generate PDF from HTML templates
        const pdfBuffer =
          await this.generateAdmissionPortfolioPdfFromHtmlTemplates(
            page,
            templates,
            templateData
          );

        this.logger.log(
          `Admission portfolio generated successfully, size: ${pdfBuffer.length} bytes`
        );

        return {
          success: true,
          documentContent: pdfBuffer,
          contentType: 'application/pdf'
        };
      } finally {
        await browser.close();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error generating admission portfolio: ${errorMessage}`,
        error instanceof Error ? error.stack : undefined
      );
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  private async getAvailableHtmlTemplates(): Promise<
    Array<{
      name: string;
      path: string;
      pageNumber: number;
    }>
  > {
    try {
      const templatesPath = path.join(
        process.cwd(),
        'apps/services/students-service/src/assets/html-templates/CAL'
      );

      // Check if directory exists
      const dirExists = await fs.promises
        .access(templatesPath)
        .then(() => true)
        .catch(() => false);
      if (!dirExists) {
        this.logger.error(
          `HTML templates directory not found: ${templatesPath}`
        );
        throw new Error(`HTML templates directory not found: ${templatesPath}`);
      }

      const files = await fs.promises.readdir(templatesPath);
      const templateDirs = files.filter((file) => {
        const fullPath = path.join(templatesPath, file);
        return (
          fs.statSync(fullPath).isDirectory() &&
          file.toLowerCase().includes('cal page')
        );
      });

      this.logger.debug(
        `Found HTML template directories: ${templateDirs.join(', ')}`
      );

      const templates = templateDirs.map((dir) => {
        let pageNumber = 1;
        // Extract page number from directory name
        // const pageMatch = dir.match(/page\s*(\d+)/i);
        const pageMatch = dir.match(/page[_\s-]?(\d+)/i);

        if (pageMatch) {
          pageNumber = parseInt(pageMatch[1], 10);
        }

        this.logger.debug(
          `Template ${dir} assigned page number: ${pageNumber}`
        );
        return {
          name: dir,
          path: path.join(templatesPath, dir),
          pageNumber
        };
      });

      templates.sort((a, b) => a.pageNumber - b.pageNumber);
      this.logger.debug(
        `Found ${templates.length} HTML templates: ${templates
          .map((t) => `${t.name} (page ${t.pageNumber})`)
          .join(', ')}`
      );
      return templates;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error reading HTML templates: ${errorMessage}`);
      throw error;
    }
  }

  private determinePagesToInclude(
    templates: Array<{ name: string; path: string; pageNumber: number }>,
    options: HtmlTemplateOptions
  ) {
    if (options.includeAllPages) {
      return templates;
    }

    if (options.pageNumbers && options.pageNumbers.length > 0) {
      return templates.filter((template) =>
        options.pageNumbers!.includes(template.pageNumber)
      );
    }

    // Default: include all pages
    return templates;
  }

  private async generatePdfFromHtmlTemplates(
    page: puppeteer.Page,
    templates: Array<{ name: string; path: string; pageNumber: number }>,
    templateData: HtmlCalDocumentData
  ): Promise<Buffer> {
    const pdfPages: Buffer[] = [];

    for (const template of templates) {
      this.logger.debug(`Processing template: ${template.name}`);
      this.logger.debug(`Template path: ${template.path}`);
      this.logger.debug(`Page number: ${template.pageNumber}`);

      try {
        const htmlContent = await this.processHtmlTemplate(
          template.path,
          templateData
        );
        const pdfBuffer = await this.convertHtmlToPdf(
          page,
          htmlContent,
          template.path
        );
        pdfPages.push(pdfBuffer);

        this.logger.debug(`Successfully processed template: ${template.name}`);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        this.logger.error(
          `Error processing template ${template.name}: ${errorMessage}`
        );
        throw error;
      }
    }
    this.logger.log(
      `Generated  pdf  length:============================>${pdfPages.length} PDF pages`
    );

    // Combine all PDF pages into a single document
    return this.combinePdfPages(pdfPages);
  }

  private async processHtmlTemplate(
    templatePath: string,
    templateData: HtmlCalDocumentData
  ): Promise<string> {
    try {
      const htmlFilePath = path.join(templatePath, 'index.html');
      const cssFilePath = path.join(templatePath, 'style.css');
      const varsFilePath = path.join(templatePath, 'vars.css');

      // Read HTML content
      let htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');

      // Read CSS content
      let cssContent = '';
      if (
        await fs.promises
          .access(cssFilePath)
          .then(() => true)
          .catch(() => false)
      ) {
        cssContent = await fs.promises.readFile(cssFilePath, 'utf-8');
      }

      let varsContent = '';
      if (
        await fs.promises
          .access(varsFilePath)
          .then(() => true)
          .catch(() => false)
      ) {
        varsContent = await fs.promises.readFile(varsFilePath, 'utf-8');
      }

      // Convert relative image paths to base64 data URLs
      htmlContent = await this.convertImagePathsToBase64(
        htmlContent,
        templatePath
      );

      // Compile Handlebars template
      const template = Handlebars.compile(htmlContent);

      // Prepare data for template
      const data = {
        name: templateData.studentName,
        studentName: templateData.studentName,
        idNumber: templateData.idNumber,
        passport: templateData.passport,
        dateOfBirth: templateData.dateOfBirth,
        courseName: templateData.courseName,
        intake: templateData.intake,
        campusName: templateData.campusName,
        tuitionFee: templateData.tuitionFee,
        courseStartDate: templateData.courseStartDate,
        courseEndDate: templateData.courseEndDate,
        issueDate: templateData.issueDate,
        applicationId: templateData.applicationId
      };

      // Render template with data
      const renderedHtml = template(data);

      // Create complete HTML document with embedded CSS
      const completeHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            ${varsContent}
            ${cssContent}
            /* Perfect A4 page setup */
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              width: 210mm;
              height: 297mm;
              max-width: 210mm;
              max-height: 297mm;
              overflow: hidden;
              box-sizing: border-box;
            }
            @media print {
              html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
              }
              @page {
                size: A4;
                margin: 0;
              }
            }
            /* Ensure each page takes full A4 dimensions */
            .cover-page {
              width: 210mm !important;
              height: 297mm !important;
              margin: 0 !important;
              padding: 0 !important;
              overflow: hidden !important;
              position: relative !important;
              background: #ffffff !important;
              box-sizing: border-box !important;
            }
            /* Fix image sizing and positioning for A4 */
            img {
              max-width: 100%;
              height: auto;
              display: block;
              object-fit: contain;
            }
            .layer-1 {
              width: 100% !important;
              height: auto !important;
              max-width: 278px !important;
              max-height: 63px !important;
              object-fit: contain !important;
            }
            .university-name-vertical {
              width: auto !important;
              height: auto !important;
              max-width: 100% !important;
              max-height: 100% !important;
              object-fit: contain !important;
            }
            .icon {
              width: 400px !important;
              height: 400px !important;
              object-fit: contain !important;
              max-width: 400px !important;
              max-height: 400px !important;
            }
            /* Ensure proper positioning within A4 bounds */
            .iau-3 {
              width: 278px !important;
              height: 63px !important;
              max-width: 278px !important;
              max-height: 63px !important;
              object-fit: contain !important;
            }
            .university-name {
              width: 24px !important;
              height: 297mm !important;
              max-width: 24px !important;
              max-height: 297mm !important;
              object-fit: contain !important;
            }
            /* Global text and element scaling for A4 */
            * {
              box-sizing: border-box !important;
              word-wrap: break-word !important;
              overflow-wrap: break-word !important;
            }
            /* Ensure all content fits within A4 bounds */
            body > * {
              max-width: 210mm !important;
              max-height: 297mm !important;
            }
          </style>
          <title>CAL Document</title>
        </head>
        <body>
          ${renderedHtml}
        </body>
        </html>
      `;

      return completeHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error processing HTML template: ${errorMessage}`);
      throw error;
    }
  }

  private async convertImagePathsToBase64(
    htmlContent: string,
    templatePath: string
  ): Promise<string> {
    try {
      // Find all img tags with src attributes
      const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
      let match;
      let processedHtml = htmlContent;

      while ((match = imgRegex.exec(htmlContent)) !== null) {
        const fullMatch = match[0];
        const imagePath = match[1];

        // Skip if already a data URL or absolute URL
        if (
          imagePath.startsWith('data:') ||
          imagePath.startsWith('http://') ||
          imagePath.startsWith('https://')
        ) {
          continue;
        }

        try {
          // Resolve the image path relative to the template directory
          const imageFilePath = path.resolve(templatePath, imagePath);

          // Check if file exists
          if (
            await fs.promises
              .access(imageFilePath)
              .then(() => true)
              .catch(() => false)
          ) {
            // Read the image file
            const imageBuffer = await fs.promises.readFile(imageFilePath);

            // Determine MIME type based on file extension
            const ext = path.extname(imagePath).toLowerCase();
            let mimeType = 'image/png'; // default

            if (ext === '.jpg' || ext === '.jpeg') {
              mimeType = 'image/jpeg';
            } else if (ext === '.svg') {
              mimeType = 'image/svg+xml';
            } else if (ext === '.gif') {
              mimeType = 'image/gif';
            } else if (ext === '.webp') {
              mimeType = 'image/webp';
            }

            // Convert to base64
            const base64Data = imageBuffer.toString('base64');
            const dataUrl = `data:${mimeType};base64,${base64Data}`;

            // Replace the src attribute
            const newImgTag = fullMatch.replace(
              /src=["']([^"']+)["']/i,
              `src="${dataUrl}"`
            );

            processedHtml = processedHtml.replace(fullMatch, newImgTag);

            this.logger.debug(
              `Successfully converted image: ${imagePath} -> base64 data URL (${imageBuffer.length} bytes)`
            );
          } else {
            this.logger.warn(`Image file not found: ${imageFilePath}`);
          }
        } catch (error) {
          this.logger.error(
            `Error converting image ${imagePath}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }

      return processedHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error converting image paths to base64: ${errorMessage}`
      );
      return htmlContent; // Return original content if conversion fails
    }
  }

  // private async convertHtmlToPdf(
  //   page: puppeteer.Page,
  //   htmlContent: string,
  //   templatePath: string
  // ): Promise<Buffer> {
  //   try {
  //     // Set viewport to exact content dimensions
  //     await page.setViewport({
  //       width: 595,
  //       height: 842,
  //       deviceScaleFactor: 2 // Higher DPI for better image quality
  //     });

  //     // Set content to page with better options for image handling
  //     await page.setContent(htmlContent, {
  //       waitUntil: ['networkidle0', 'domcontentloaded'],
  //       timeout: 30000
  //     });

  //     // Wait for images to load with more robust handling
  //     await page.waitForTimeout(5000);

  //     // Ensure all images are loaded and rendered
  //     await page.evaluate(() => {
  //       return Promise.all(
  //         Array.from(document.images).map(
  //           (img) =>
  //             new Promise((resolve) => {
  //               if (img.complete) {
  //                 resolve(true);
  //               } else {
  //                 img.onload = () => resolve(true);
  //                 img.onerror = () => resolve(false);
  //               }
  //             })
  //         )
  //       );
  //     });

  //     // Additional wait to ensure all content is rendered
  //     await page.waitForTimeout(2000);

  //     // Generate PDF with exact content dimensions
  //     const pdfBuffer = await page.pdf({
  //       format: undefined, // Don't use A4 format, use exact dimensions
  //       printBackground: true,
  //       margin: {
  //         top: '0',
  //         right: '0',
  //         bottom: '0',
  //         left: '0'
  //       },
  //       preferCSSPageSize: false, // Disable to use exact dimensions
  //       displayHeaderFooter: false,
  //       // Use exact content dimensions
  //       width: '595px',
  //       height: '842px',
  //       // Better image quality settings
  //       scale: 1,
  //       // Ensure images are rendered
  //       omitBackground: false
  //     });

  //     return pdfBuffer;
  //   } catch (error) {
  //     const errorMessage =
  //       error instanceof Error ? error.message : 'Unknown error occurred';
  //     this.logger.error(`Error converting HTML to PDF: ${errorMessage}`);
  //     throw error;
  //   }
  // }

  private async convertHtmlToPdf(
    page: puppeteer.Page,
    htmlContent: string,
    templatePath: string
  ): Promise<Buffer> {
    try {
      // Extract page number from template path for better logging
      const pageMatch = templatePath.match(/CAL Page-(\d+)/);
      const pageNumber = pageMatch ? pageMatch[1] : 'Unknown';

      this.logger.debug(`�� Converting HTML to PDF for CAL Page-${pageNumber}`);
      this.logger.debug(`📁 Template path: ${templatePath}`);
      this.logger.debug(
        `📏 HTML content length: ${htmlContent.length} characters`
      );

      // Set viewport to A4 screen-equivalent size (96 DPI)
      // A4 = 210mm x 297mm = 794px x 1123px at 96 DPI
      await page.setViewport({
        width: 794,
        height: 1123,
        deviceScaleFactor: 1
      });

      this.logger.debug(`�� Set viewport to A4 dimensions: 794x1123 pixels`);

      // Load HTML
      await page.setContent(htmlContent, {
        waitUntil: ['networkidle0', 'domcontentloaded'],
        timeout: 30000
      });

      this.logger.debug(
        `📄 HTML content set to page, waiting for images to load...`
      );

      // Wait for images to load
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Ensure images are loaded
      await page.evaluate(() =>
        Promise.all(
          Array.from(document.images).map(
            (img) =>
              new Promise<boolean>((resolve) => {
                if (img.complete) {
                  resolve(true);
                } else {
                  img.onload = () => resolve(true);
                  img.onerror = () => resolve(false);
                }
              })
          )
        )
      );

      const contentDimensions = await page.evaluate(() => {
        const body = document.body;
        const html = document.documentElement;

        return {
          bodyScrollWidth: body.scrollWidth,
          bodyScrollHeight: body.scrollHeight,
          bodyClientWidth: body.clientWidth,
          bodyClientHeight: body.clientHeight,
          htmlScrollWidth: html.scrollWidth,
          htmlScrollHeight: html.scrollHeight,
          htmlClientWidth: html.clientWidth,
          htmlClientHeight: html.clientHeight,
          allElements: document.querySelectorAll('*').length,
          absoluteElements: document.querySelectorAll(
            '[style*="position: absolute"]'
          ).length,
          fixedElements: document.querySelectorAll('[style*="position: fixed"]')
            .length
        };
      });
      await page.evaluate(() => {
        // Align page CSS to true A4; hide overflow to avoid spillover
        const body = document.body;
        const html = document.documentElement;

        body.style.width = '210mm';
        body.style.height = '297mm';
        body.style.maxWidth = '210mm';
        body.style.maxHeight = '297mm';
        html.style.width = '210mm';
        html.style.height = '297mm';
        html.style.maxWidth = '210mm';
        html.style.maxHeight = '297mm';

        body.style.overflow = 'hidden';
        html.style.overflow = 'hidden';
        body.style.margin = '0';
        body.style.padding = '0';
        html.style.margin = '0';
        html.style.padding = '0';

        const style = document.createElement('style');
        style.textContent = `
          @page { 
            size: A4; 
            margin: 0; 
          }
          
          * { 
            box-sizing: border-box !important;
            page-break-inside: avoid !important; 
            page-break-before: avoid !important; 
            page-break-after: avoid !important; 
          }
          
          html, body { 
            width: 210mm !important; 
            height: 297mm !important; 
            max-width: 210mm !important; 
            max-height: 297mm !important;
            min-height: 297mm !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 12px !important;
            line-height: 1.3 !important;
            transform: scale(1) !important;
            transform-origin: top left !important;
          }
          
          .container, .content, .page, div, section, article { 
            max-height: 297mm !important; 
            overflow: hidden !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            font-size: 11px !important;
          }
          
          /* Ensure tables fit properly */
          table {
            width: 100% !important;
            table-layout: fixed !important;
            font-size: 10px !important;
            border-collapse: collapse !important;
          }
          
          /* Adjust text elements for better fitting */
          p, div, span, td, th, li {
            font-size: 10px !important;
            line-height: 1.2 !important;
            margin: 1px 0 !important;
            padding: 1px !important;
            word-wrap: break-word !important;
          }
          
          /* Headers */
          h1, h2, h3, h4, h5, h6 {
            font-size: 12px !important;
            line-height: 1.2 !important;
            margin: 2px 0 !important;
            padding: 1px !important;
          }
          
          /* Ensure images scale properly */
          img {
            max-width: 100% !important;
            height: auto !important;
            object-fit: contain !important;
          }
          
          /* Prevent text overflow and ensure wrapping */
          * {
            text-overflow: ellipsis !important;
            white-space: normal !important;
            overflow-wrap: break-word !important;
            hyphens: auto !important;
          }
          
          /* Scale down if content is too large */
          body > * {
            transform: scale(0.95) !important;
            transform-origin: top left !important;
          }
        `;
        document.head.appendChild(style);

        // Auto-scale content if it exceeds A4 dimensions
        setTimeout(() => {
          const bodyHeight = document.body.scrollHeight;
          const bodyWidth = document.body.scrollWidth;
          const a4HeightPx = 297 * 3.78; // 297mm to pixels (approximate)
          const a4WidthPx = 210 * 3.78;  // 210mm to pixels (approximate)
          
          let scaleX = 1;
          let scaleY = 1;
          
          if (bodyWidth > a4WidthPx) {
            scaleX = a4WidthPx / bodyWidth * 0.95; // 95% to leave margin
          }
          
          if (bodyHeight > a4HeightPx) {
            scaleY = a4HeightPx / bodyHeight * 0.95; // 95% to leave margin
          }
          
          // Use the smaller scale to ensure content fits both dimensions
          const finalScale = Math.min(scaleX, scaleY, 1);
          
          if (finalScale < 1) {
            console.log(`Auto-scaling content by ${(finalScale * 100).toFixed(1)}% to fit A4`);
            document.body.style.transform = `scale(${finalScale})`;
            document.body.style.transformOrigin = 'top left';
            document.body.style.width = `${210/finalScale}mm`;
            document.body.style.height = `${297/finalScale}mm`;
          }
        }, 100);
      });

      // 🔍 CHECK DIMENSIONS AFTER FIXING
      const dimensionsAfterFix = await page.evaluate(() => {
        const body = document.body;
        const html = document.documentElement;

        return {
          bodyClientHeight: body.clientHeight,
          bodyScrollHeight: body.scrollHeight,
          bodyOffsetHeight: body.offsetHeight,
          htmlClientHeight: html.clientHeight,
          htmlScrollHeight: html.scrollHeight,
          htmlOffsetHeight: html.offsetHeight
        };
      });

      // this.logger.debug(
      //   `🔧 CAL Page-${pageNumber} - Dimensions after fixing:`,
      //   dimensionsAfterFix
      // );

      // // 🚨 FINAL OVERFLOW CHECK
      // const finalOverflow = [];
      // if (dimensionsAfterFix.bodyScrollHeight > 842) {
      //   finalOverflow.push(
      //     `Body scroll height still too large: ${dimensionsAfterFix.bodyScrollHeight}px`
      //   );
      // }
      // if (dimensionsAfterFix.htmlScrollHeight > 842) {
      //   finalOverflow.push(
      //     `HTML scroll height still too large: ${dimensionsAfterFix.htmlScrollHeight}px`
      //   );
      // }

      // if (finalOverflow.length > 0) {
      //   this.logger.error(
      //     `🚨 CAL Page-${pageNumber} - OVERFLOW PERSISTS after fixing: ${finalOverflow.join(
      //       ', '
      //     )}`
      //   );
      //   this.logger.error(`🚨 This page will likely generate multiple pages!`);
      // } else {
      //   this.logger.debug(
      //     `✅ CAL Page-${pageNumber} - No overflow detected after fixing`
      //   );
      // }

      // Wait for auto-scaling to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // this.logger.debug(`�� Generating PDF for CAL Page-${pageNumber}...`);

      // Generate PDF with perfect A4 formatting
      const pdfBuffer = await page.pdf({
        format: 'A4', // Standard A4 format (210mm x 297mm)
        printBackground: true,
        margin: {
          top: '0mm',
          right: '0mm',
          bottom: '0mm',
          left: '0mm'
        },
        // Use CSS page size for consistent rendering
        preferCSSPageSize: true,
        displayHeaderFooter: false,
        // Ensure high quality output
        scale: 1,
        omitBackground: false,
        // Force single page output
        pageRanges: '1'
      });

      // this.logger.debug(
      //   `✅ CAL Page-${pageNumber} - PDF generated successfully. Buffer size: ${pdfBuffer.length} bytes`
      // );

      // // 📊 FINAL SUMMARY FOR THIS PAGE
      // this.logger.log(`📊 CAL Page-${pageNumber} Summary:`);
      // this.logger.log(
      //   `   �� Content dimensions: ${contentDimensions.bodyScrollWidth}x${contentDimensions.bodyScrollHeight}`
      // );
      // this.logger.log(`   📄 PDF size: ${pdfBuffer.length} bytes`);
      // this.logger.log(`   ⚠️ CSS issues: ${cssIssues.length}`);
      // this.logger.log(`   🚨 Overflow issues: ${finalOverflow.length}`);

      // if (finalOverflow.length > 0) {
      //   this.logger.error(
      //     `🚨 CAL Page-${pageNumber} - HIGH RISK of generating multiple pages!`
      //   );
      // } else {
      //   this.logger.debug(
      //     `✅ CAL Page-${pageNumber} - LOW RISK of generating multiple pages`
      //   );
      // }

      return pdfBuffer;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`❌ Error converting HTML to PDF: ${errorMessage}`);
      this.logger.error(`📁 Template path: ${templatePath}`);
      throw error;
    }
  }

  private async combinePdfPages(pdfPages: Buffer[]): Promise<Buffer> {
    try {
      if (pdfPages.length === 0) {
        throw new Error('No PDF pages to combine');
      }

      if (pdfPages.length === 1) {
        return pdfPages[0];
      }

      this.logger.log(
        `Combining ${pdfPages.length} PDF pages into a single document`
      );

      // Create a new PDF document
      const mergedPdf = await PDFDocument.create();

      // Add each page to the merged document
      for (let i = 0; i < pdfPages.length; i++) {
        try {
          const pdfDoc = await PDFDocument.load(new Uint8Array(pdfPages[i]));
          const pageCount = pdfDoc.getPageCount();
          this.logger.debug(`Page ${i + 1} has ${pageCount} pages`);

          if (pageCount > 1) {
            this.logger.warn(
              `Template ${i + 1} generated ${pageCount} pages instead of 1`
            );
            // ADD THIS: Check page dimensions
            const pages = pdfDoc.getPages();
            pages.forEach((page, pageIndex) => {
              const { width, height } = page.getSize();
              this.logger.debug(
                `  Sub-page ${pageIndex + 1} dimensions: ${width}x${height}`
              );
            });
          }

          // If a template produced more than one page (e.g., an extra blank page),
          // only take the first page to keep CAL at exactly one page per template.
          const indicesToCopy = pageCount > 1 ? [0] : pdfDoc.getPageIndices();
          const pages = await mergedPdf.copyPages(
            pdfDoc,
            indicesToCopy
          );
          pages.forEach((page) => mergedPdf.addPage(page));
          this.logger.debug(
            `Successfully added ${indicesToCopy.length} page(s) from template ${i + 1}`
          );
        } catch (error) {
          this.logger.error(
            `Error processing page ${i + 1}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
          throw error;
        }
      }

      // Save the merged PDF
      const mergedPdfBytes = await mergedPdf.save();
      const mergedPdfBuffer = Buffer.from(mergedPdfBytes);

      this.logger.log(
        `Successfully created merged PDF with ${mergedPdf.getPageCount()} pages, size: ${
          mergedPdfBuffer.length
        } bytes`
      );

      return mergedPdfBuffer;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(`Error combining PDF pages: ${errorMessage}`);
      throw error;
    }
  }

  private async getAvailableAdmissionPackageTemplates(
    applicationType: string
  ): Promise<
    Array<{
      name: string;
      path: string;
      content: string;
    }>
  > {
    try {
      // Map application type to folder name (handle actual database values)
      const folderMapping: { [key: string]: string } = {
        'F-1 Initial': 'F-1 Initial',
        'F-1 Intial': 'F-1 Initial', // Handle typo in database
        Transfer: 'F-1 Transfer (Admission Package ADM)', // Handle "Transfer" from database
        'F-1 Transfer': 'F-1 Transfer (Admission Package ADM)', // Also handle full name
        'F-1 Reinstatement': 'F-1 Reinstatement',
        'New Program': 'New Program',
        'Change of Status': 'Change of status',
        'Non-F-1': 'Non F1'
      };

      const folderName = folderMapping[applicationType] || 'F-1 Initial';
      const templatesDir = path.join(
        process.cwd(),
        'apps',
        'services',
        'students-service',
        'src',
        'assets',
        'html-templates',
        'Admission Package',
        folderName
      );

      if (!fs.existsSync(templatesDir)) {
        this.logger.warn(
          `Admission package templates directory not found: ${templatesDir}`
        );
        return [];
      }

      // Look for subdirectories that contain HTML files
      const subdirs = fs
        .readdirSync(templatesDir)
        .filter((item) => {
          const itemPath = path.join(templatesDir, item);
          return fs.statSync(itemPath).isDirectory();
        })
        .sort();

      this.logger.log(
        `Found ${subdirs.length} subdirectories for admission package (${applicationType})`
      );

      const templates = [];
      for (const subdir of subdirs) {
        const subdirPath = path.join(templatesDir, subdir);
        const htmlFiles = fs
          .readdirSync(subdirPath)
          .filter((file) => file.endsWith('.html'))
          .sort();

        for (const htmlFile of htmlFiles) {
          const templatePath = path.join(subdirPath, htmlFile);
          const content = fs.readFileSync(templatePath, 'utf-8');

          templates.push({
            name: `${subdir}-${htmlFile.replace('.html', '')}`,
            path: templatePath,
            content: content
          });
        }
      }

      this.logger.log(
        `Found ${templates.length} HTML templates for admission package (${applicationType})`
      );
      return templates;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error getting available admission package templates: ${errorMessage}`
      );
      return [];
    }
  }

  private async getAvailableAdmissionPortfolioTemplates(
    applicationType: string
  ): Promise<
    Array<{
      name: string;
      path: string;
      content: string;
    }>
  > {
    try {
      // Map application type to folder name (handle actual database values)
      const folderMapping: { [key: string]: string } = {
        'F-1 Initial': 'Initial Student Admission Portfolio',
        'F-1 Intial': 'Initial Student Admission Portfolio', // Handle typo in database
        Transfer: 'Transfer Student Admission Portfolio', // Handle "Transfer" from database
        'F-1 Transfer': 'Transfer Student Admission Portfolio', // Also handle full name
        'F-1 Reinstatement': 'F-1 Reinstatement Admission Portfolio',
        'New Program': 'New Program Admission Portfolio',
        'Change of Status': 'Change of Status Admission Portfolio',
        'Non-F-1': 'Non F1 Student Admission Portfolio'
      };

      const folderName =
        folderMapping[applicationType] || 'Initial Student Admission Portfolio';
      const templatesDir = path.join(
        process.cwd(),
        'apps',
        'services',
        'students-service',
        'src',
        'assets',
        'html-templates',
        'Admission Portfolio',
        folderName
      );

      if (!fs.existsSync(templatesDir)) {
        this.logger.warn(
          `Admission portfolio templates directory not found: ${templatesDir}`
        );
        return [];
      }

      // Look for subdirectories that contain HTML files
      const subdirs = fs
        .readdirSync(templatesDir)
        .filter((item) => {
          const itemPath = path.join(templatesDir, item);
          return fs.statSync(itemPath).isDirectory();
        })
        .sort();

      this.logger.log(
        `Found ${subdirs.length} subdirectories for admission portfolio (${applicationType})`
      );

      const templates = [];
      for (const subdir of subdirs) {
        const subdirPath = path.join(templatesDir, subdir);
        const htmlFiles = fs
          .readdirSync(subdirPath)
          .filter((file) => file.endsWith('.html'))
          .sort();

        for (const htmlFile of htmlFiles) {
          const templatePath = path.join(subdirPath, htmlFile);
          const content = fs.readFileSync(templatePath, 'utf-8');

          templates.push({
            name: `${subdir}-${htmlFile.replace('.html', '')}`,
            path: templatePath,
            content: content
          });
        }
      }

      this.logger.log(
        `Found ${templates.length} HTML templates for admission portfolio (${applicationType})`
      );
      return templates;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error getting available admission portfolio templates: ${errorMessage}`
      );
      return [];
    }
  }

  private async generateAdmissionPackagePdfFromHtmlTemplates(
    page: puppeteer.Page,
    templates: Array<{ name: string; path: string; content: string }>,
    templateData: HtmlAdmissionPackageData
  ): Promise<Buffer> {
    const pdfPages: Buffer[] = [];

    for (const template of templates) {
      this.logger.debug(
        `Processing admission package template: ${template.name}`
      );

      try {
        const htmlContent = await this.processAdmissionPackageHtmlTemplate(
          template.content,
          templateData
        );
        const pdfBuffer = await this.convertHtmlToPdf(
          page,
          htmlContent,
          template.path
        );
        pdfPages.push(pdfBuffer);

        this.logger.debug(
          `Successfully processed admission package template: ${template.name}`
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        this.logger.error(
          `Error processing admission package template ${template.name}: ${errorMessage}`
        );
        throw error;
      }
    }

    // Combine all PDF pages into a single document
    return await this.combinePdfPages(pdfPages);
  }

  private async generateAdmissionPortfolioPdfFromHtmlTemplates(
    page: puppeteer.Page,
    templates: Array<{ name: string; path: string; content: string }>,
    templateData: HtmlAdmissionPortfolioData
  ): Promise<Buffer> {
    const pdfPages: Buffer[] = [];

    for (const template of templates) {
      this.logger.debug(
        `Processing admission portfolio template: ${template.name}`
      );

      try {
        const htmlContent = await this.processAdmissionPortfolioHtmlTemplate(
          template.content,
          templateData
        );
        const pdfBuffer = await this.convertHtmlToPdf(
          page,
          htmlContent,
          template.path
        );
        pdfPages.push(pdfBuffer);

        this.logger.debug(
          `Successfully processed admission portfolio template: ${template.name}`
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        this.logger.error(
          `Error processing admission portfolio template ${template.name}: ${errorMessage}`
        );
        throw error;
      }
    }

    // Combine all PDF pages into a single document
    return await this.combinePdfPages(pdfPages);
  }

  private async processAdmissionPortfolioHtmlTemplate(
    templateContent: string,
    templateData: HtmlAdmissionPortfolioData
  ): Promise<string> {
    try {
      // Compile the Handlebars template
      const template = Handlebars.compile(templateContent);

      // Register custom helpers for admission portfolio
      Handlebars.registerHelper('formatDate', function (date: string) {
        if (!date || date === 'N/A') return 'N/A';
        try {
          const d = new Date(date);
          return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        } catch {
          return date;
        }
      });

      Handlebars.registerHelper(
        'joinArray',
        function (array: string[], separator = ', ') {
          if (!array || !Array.isArray(array)) return 'N/A';
          return array.join(separator);
        }
      );

      // Generate the HTML content with the template data
      const renderedHtml = template(templateData);

      // Create complete HTML document with A4 styling
      const completeHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            /* Perfect A4 page setup */
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              width: 210mm;
              height: 297mm;
              max-width: 210mm;
              max-height: 297mm;
              overflow: hidden;
              box-sizing: border-box;
            }
            @media print {
              html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
              }
              @page {
                size: A4;
                margin: 0;
              }
            }
            /* Global A4 optimizations */
            * {
              box-sizing: border-box !important;
              word-wrap: break-word !important;
              overflow-wrap: break-word !important;
            }
            /* Fix image sizing for A4 */
            img {
              max-width: 100%;
              height: auto;
              display: block;
              object-fit: contain;
            }
            /* Ensure all content fits within A4 bounds */
            body > * {
              max-width: 210mm !important;
              max-height: 297mm !important;
            }
          </style>
          <title>Admission Portfolio</title>
        </head>
        <body>
          ${renderedHtml}
        </body>
        </html>
      `;

      this.logger.debug(
        `Admission portfolio HTML template processed successfully with A4 styling`
      );
      return completeHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error processing admission portfolio HTML template: ${errorMessage}`
      );
      throw error;
    }
  }

  private async processAdmissionPackageHtmlTemplate(
    templateContent: string,
    templateData: HtmlAdmissionPackageData
  ): Promise<string> {
    try {
      // Compile the Handlebars template
      const template = Handlebars.compile(templateContent);

      // Register custom helpers for admission package
      Handlebars.registerHelper('formatDate', function (date: string) {
        if (!date || date === 'N/A') return 'N/A';
        try {
          const d = new Date(date);
          return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        } catch {
          return date;
        }
      });

      Handlebars.registerHelper(
        'joinArray',
        function (array: string[], separator = ', ') {
          if (!array || !Array.isArray(array)) return 'N/A';
          return array.join(separator);
        }
      );

      // Generate the HTML content with the template data
      const renderedHtml = template(templateData);

      // Create complete HTML document with A4 styling
      const completeHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            /* Perfect A4 page setup */
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              width: 210mm;
              height: 297mm;
              max-width: 210mm;
              max-height: 297mm;
              overflow: hidden;
              box-sizing: border-box;
            }
            @media print {
              html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
              }
              @page {
                size: A4;
                margin: 0;
              }
            }
            /* Global A4 optimizations */
            * {
              box-sizing: border-box !important;
              word-wrap: break-word !important;
              overflow-wrap: break-word !important;
            }
            /* Fix image sizing for A4 */
            img {
              max-width: 100%;
              height: auto;
              display: block;
              object-fit: contain;
            }
            /* Ensure all content fits within A4 bounds */
            body > * {
              max-width: 210mm !important;
              max-height: 297mm !important;
            }
          </style>
          <title>Admission Package</title>
        </head>
        <body>
          ${renderedHtml}
        </body>
        </html>
      `;

      this.logger.debug(
        `Admission package HTML template processed successfully with A4 styling`
      );
      return completeHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error processing admission package HTML template: ${errorMessage}`
      );
      throw error;
    }
  }

  private async getAvailableAdmDocumentTemplates(
    applicationType: string
  ): Promise<
    Array<{
      name: string;
      path: string;
      content: string;
    }>
  > {
    try {
      // Map application type to folder name (handle actual database values)
      const folderMapping: { [key: string]: string } = {
        'F-1 Initial': 'F-1 Initial',
        'F-1 Intial': 'F-1 Initial', // Handle typo in database
        Transfer: 'F-1 Transfer (Admission Package ADM)', // Handle "Transfer" from database
        'F-1 Transfer': 'F-1 Transfer (Admission Package ADM)', // Also handle full name
        'F-1 Reinstatement': 'F-1 Reinstatement',
        'New Program': 'New Program',
        'Change of Status': 'Change of status',
        'Non-F-1': 'Non F1'
      };

      const folderName = folderMapping[applicationType] || 'F-1 Initial';
      this.logger.log(
        `ADM document: Application type "${applicationType}" mapped to folder "${folderName}"`
      );

      const templatesDir = path.join(
        process.cwd(),
        'apps',
        'services',
        'students-service',
        'src',
        'assets',
        'html-templates',
        'Admission Package',
        folderName
      );
      this.logger.log(
        `ADM document: Looking for templates in directory: ${templatesDir}`
      );

      if (!fs.existsSync(templatesDir)) {
        this.logger.warn(
          `ADM document templates directory not found: ${templatesDir}`
        );
        return [];
      }

      // Recursively search for HTML files in all subdirectories
      const findHtmlFilesRecursively = (
        dirPath: string,
        depth = 0
      ): Array<{ path: string; content: string; relativePath: string }> => {
        const results: Array<{
          path: string;
          content: string;
          relativePath: string;
        }> = [];

        try {
          const items = fs.readdirSync(dirPath);

          for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory()) {
              // Recursively search subdirectories
              const subResults = findHtmlFilesRecursively(itemPath, depth + 1);
              results.push(...subResults);
            } else if (item.endsWith('.html')) {
              // Found an HTML file
              const content = fs.readFileSync(itemPath, 'utf-8');
              const relativePath = path.relative(templatesDir, itemPath);
              results.push({
                path: itemPath,
                content: content,
                relativePath: relativePath
              });
            }
          }
        } catch (error) {
          this.logger.warn(`Error reading directory ${dirPath}: ${error}`);
        }

        return results;
      };

      this.logger.log(
        `Recursively searching for HTML templates in: ${templatesDir}`
      );
      const htmlFiles = findHtmlFilesRecursively(templatesDir);

      this.logger.log(
        `Found ${htmlFiles.length} HTML files recursively in ADM document templates (${applicationType})`
      );

      const templates = [];
      for (const htmlFile of htmlFiles) {
        templates.push({
          name: htmlFile.relativePath
            .replace(/[\\/]/g, '-')
            .replace('.html', ''),
          path: htmlFile.path,
          content: htmlFile.content
        });
      }

      this.logger.log(
        `Found ${templates.length} HTML templates for ADM document (${applicationType})`
      );
      return templates;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error getting available ADM document templates: ${errorMessage}`
      );
      return [];
    }
  }

  private async generateAdmDocumentPdfFromHtmlTemplates(
    page: puppeteer.Page,
    templates: Array<{ name: string; path: string; content: string }>,
    templateData: HtmlAdmDocumentData
  ): Promise<Buffer> {
    const pdfPages: Buffer[] = [];

    for (const template of templates) {
      this.logger.debug(`Processing ADM document template: ${template.name}`);

      try {
        const htmlContent = await this.processAdmDocumentHtmlTemplate(
          template.content,
          template.path,
          templateData
        );
        const pdfBuffer = await this.convertHtmlToPdf(
          page,
          htmlContent,
          template.path
        );
        pdfPages.push(pdfBuffer);

        this.logger.debug(
          `Successfully processed ADM document template: ${template.name}`
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        this.logger.error(
          `Error processing ADM document template ${template.name}: ${errorMessage}`
        );
        throw error;
      }
    }

    // Combine all PDF pages into a single document
    return await this.combinePdfPages(pdfPages);
  }

  private async processAdmDocumentHtmlTemplate(
    templateContent: string,
    templatePath: string,
    templateData: HtmlAdmDocumentData
  ): Promise<string> {
    try {
      const templateDir = path.dirname(templatePath);
      const cssFilePath = path.join(templateDir, 'style.css');
      const varsFilePath = path.join(templateDir, 'vars.css');

      // Read CSS content
      let cssContent = '';
      if (
        await fs.promises
          .access(cssFilePath)
          .then(() => true)
          .catch(() => false)
      ) {
        cssContent = await fs.promises.readFile(cssFilePath, 'utf-8');
        this.logger.debug(`Loaded CSS file: ${cssFilePath}`);
      } else {
        this.logger.warn(`CSS file not found: ${cssFilePath}`);
      }

      let varsContent = '';
      if (
        await fs.promises
          .access(varsFilePath)
          .then(() => true)
          .catch(() => false)
      ) {
        varsContent = await fs.promises.readFile(varsFilePath, 'utf-8');
        this.logger.debug(`Loaded vars file: ${varsFilePath}`);
      } else {
        this.logger.warn(`Vars file not found: ${varsFilePath}`);
      }

      // Convert relative image paths to base64 data URLs
      const htmlContent = await this.convertImagePathsToBase64(
        templateContent,
        templateDir
      );

      // Compile the Handlebars template
      const template = Handlebars.compile(htmlContent);

      // Register custom helpers for ADM document
      Handlebars.registerHelper('formatDate', function (date: string) {
        if (!date || date === 'N/A') return 'N/A';
        try {
          const d = new Date(date);
          return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        } catch {
          return date;
        }
      });

      Handlebars.registerHelper(
        'joinArray',
        function (array: string[], separator = ', ') {
          if (!array || !Array.isArray(array)) return 'N/A';
          return array.join(separator);
        }
      );

      // Generate the HTML content with the template data
      const renderedHtml = template(templateData);

      // Create complete HTML document with embedded CSS for A4 styling
      const completeHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            ${varsContent}
            ${cssContent}
            /* Perfect A4 page setup */
            html, body {
              margin: 0;
              padding: 0;
              width: 210mm;
              height: 297mm;
              max-width: 210mm;
              max-height: 297mm;
              overflow: hidden;
              box-sizing: border-box;
            }
            @media print {
              html, body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
              }
              @page {
                size: A4;
                margin: 0;
              }
            }
            /* Ensure the main container takes full A4 dimensions */
            .f-1-initial-1,
            .f-1-transfer-1,
            .f-1-reinstatement-1,
            .new-program-1,
            .change-of-status-1,
            .non-f1-1 {
              width: 210mm !important;
              height: 297mm !important;
              margin: 0 !important;
              padding: 0 !important;
              overflow: hidden !important;
              position: relative !important;
              background: #ffffff !important;
              box-sizing: border-box !important;
            }
            /* Additional A4 formatting for better text scaling */
            * {
              box-sizing: border-box !important;
              word-wrap: break-word !important;
              overflow-wrap: break-word !important;
            }
            /* Ensure all content fits within A4 bounds */
            body > * {
              max-width: 210mm !important;
              max-height: 297mm !important;
            }
            /* Fix image sizing and positioning for A4 */
            img {
              max-width: 100%;
              height: auto;
              display: block;
              object-fit: contain;
            }
            /* Responsive text sizing for A4 - improve readability */
            .f-1-initial-1 *[style*="font-size: 8px"],
            .f-1-transfer-1 *[style*="font-size: 8px"],
            .f-1-reinstatement-1 *[style*="font-size: 8px"],
            .new-program-1 *[style*="font-size: 8px"],
            .change-of-status-1 *[style*="font-size: 8px"],
            .non-f1-1 *[style*="font-size: 8px"] {
              font-size: 9px !important;
            }
            .f-1-initial-1 *[style*="font-size: 9px"],
            .f-1-transfer-1 *[style*="font-size: 9px"],
            .f-1-reinstatement-1 *[style*="font-size: 9px"],
            .new-program-1 *[style*="font-size: 9px"],
            .change-of-status-1 *[style*="font-size: 9px"],
            .non-f1-1 *[style*="font-size: 9px"] {
              font-size: 10px !important;
            }
            .f-1-initial-1 *[style*="font-size: 10px"],
            .f-1-transfer-1 *[style*="font-size: 10px"],
            .f-1-reinstatement-1 *[style*="font-size: 10px"],
            .new-program-1 *[style*="font-size: 10px"],
            .change-of-status-1 *[style*="font-size: 10px"],
            .non-f1-1 *[style*="font-size: 10px"] {
              font-size: 11px !important;
            }
            /* Ensure background images scale properly */
            .f-1-initial-1 [style*="background-image"],
            .f-1-transfer-1 [style*="background-image"],
            .f-1-reinstatement-1 [style*="background-image"],
            .new-program-1 [style*="background-image"],
            .change-of-status-1 [style*="background-image"],
            .non-f1-1 [style*="background-image"] {
              background-size: contain !important;
              background-repeat: no-repeat !important;
              background-position: center !important;
            }
            /* Force all elements to use border-box for proper sizing */
            .f-1-initial-1 *,
            .f-1-transfer-1 *,
            .f-1-reinstatement-1 *,
            .new-program-1 *,
            .change-of-status-1 *,
            .non-f1-1 * {
              box-sizing: border-box !important;
            }

          </style>
          <title>ADM Document</title>
        </head>
        <body>
          ${renderedHtml}
        </body>
        </html>
      `;

      this.logger.debug(
        `ADM document HTML template processed successfully with A4 styling and CSS files`
      );
      return completeHtml;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      this.logger.error(
        `Error processing ADM document HTML template: ${errorMessage}`
      );
      throw error;
    }
  }
}
