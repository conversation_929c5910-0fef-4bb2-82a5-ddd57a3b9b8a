# Development Environment Configuration
# Copy this file to .env.development and fill in your actual values

# Database Configuration (Local PostgreSQL)
DB_HOST=host.docker.internal
DB_PORT=5432
DB_NAME=applyGoalPortal
DB_USER=postgres
DB_PASSWORD=postgres123

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# RabbitMQ Configuration
RABBITMQ_USER=rabbitmq_user
RABBITMQ_PASS=rabbitmq_pass

# S3/MinIO Configuration (Local MinIO)
STORAGE_ENABLED=true
S3_ENDPOINT=http://minio:9000
S3_PUBLIC_ENDPOINT=http://localhost:9000
S3_BUCKET=applygoal-files
S3_ACCESS_KEY=admin
S3_SECRET_KEY=admin123
S3_REGION=us-east-1
S3_FORCE_PATH_STYLE=true

# JWT Configuration
JWT_SECRET=applygoal-super-secret-jwt-key-2024

# Google OAuth Configuration (for Gmail API)
GMAIL_CLIENT_ID=your-gmail-client-id-here
GMAIL_CLIENT_SECRET=your-gmail-client-secret-here
GMAIL_REFRESH_TOKEN=your-gmail-refresh-token-here
GMAIL_SENDER=<EMAIL>

# Google OAuth Configuration (for authentication)
GOOGLE_CLIENT_ID=your-google-oauth-client-id-here

# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123

# NEXTAUTH Configuration
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://crm.localhost

# Microservices Database Configuration
AUDIT_DB_HOST=host.docker.internal
AUDIT_DB_PORT=5432
AUDIT_DB_NAME=audit_db
AUDIT_DB_USER=audit_user
AUDIT_DB_PASSWORD=audit_pass

AUTH_DB_HOST=host.docker.internal
AUTH_DB_PORT=5432
AUTH_DB_NAME=auth_db
AUTH_DB_USER=auth_user
AUTH_DB_PASSWORD=auth_pass

STUDENTS_DB_HOST=host.docker.internal
STUDENTS_DB_PORT=5432
STUDENTS_DB_NAME=students_db
STUDENTS_DB_USER=students_user
STUDENTS_DB_PASSWORD=students_pass

UNIVERSITY_DB_HOST=host.docker.internal
UNIVERSITY_DB_PORT=5432
UNIVERSITY_DB_NAME=university_db
UNIVERSITY_DB_USER=university_user
UNIVERSITY_DB_PASSWORD=university_pass