-- Rollback Migration: Remove campusId field from organizations table
-- Date: 2024-01-XX
-- Description: Remove campusId field and related index

-- Drop the index first
DROP INDEX IF EXISTS idx_organizations_campus_id;

-- Remove the column
ALTER TABLE organizations
DROP COLUMN IF EXISTS "campusId";

-- Verify the rollback
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'organizations' AND column_name = 'campusId'; 