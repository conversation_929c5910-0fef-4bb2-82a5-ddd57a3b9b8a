-- Migration: Add 'campus' to organizations.type enum
-- Date: 2024-01-XX
-- Description: Add 'campus' as a valid organization type to support campus organizations

-- First, create a new enum type with all values including 'campus'
CREATE TYPE organizations_type_new AS ENUM ('university', 'agency', 'company', 'campus');

-- Update the existing column to use the new enum type
ALTER TABLE organizations 
  ALTER COLUMN "type" TYPE organizations_type_new 
  USING "type"::text::organizations_type_new;

-- Drop the old enum type
DROP TYPE organizations_type;

-- Rename the new enum type to the original name
ALTER TYPE organizations_type_new RENAME TO organizations_type;

-- Add comment for documentation
COMMENT ON TYPE organizations_type IS 'Organization types including university, agency, company, and campus';

-- Verify the change
SELECT unnest(enum_range(NULL::organizations_type)) as valid_organization_types; 