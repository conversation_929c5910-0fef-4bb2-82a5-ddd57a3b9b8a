-- Migration: Add index on campusId field for better performance
-- Date: 2025-08-11
-- Description: Add index on organizations.campusId for faster campus organization lookups

-- Add index for better performance when looking up organizations by campusId
CREATE INDEX IF NOT EXISTS idx_organizations_campus_id_performance ON organizations("campusId");

-- Add comment for documentation
COMMENT ON INDEX idx_organizations_campus_id_performance IS 'Performance index for campus organization lookups';

-- Verify the index was created
SELECT 
    indexname, 
    tablename, 
    indexdef 
FROM pg_indexes 
WHERE indexname = 'idx_organizations_campus_id_performance'; 