-- Rollback Migration: Remove 'campus' from organizations.type enum
-- Date: 2024-01-XX
-- Description: Rollback script to remove 'campus' organization type if needed

-- First, check if there are any organizations with type 'campus'
-- If there are, you'll need to either delete them or change their type first
SELECT COUNT(*) as campus_organizations_count 
FROM organizations 
WHERE "type" = 'campus';

-- If the count is 0, proceed with the rollback
-- Create a new enum type without 'campus'
CREATE TYPE organizations_type_old AS ENUM ('university', 'agency', 'company');

-- Update the existing column to use the old enum type
ALTER TABLE organizations 
  ALTER COLUMN "type" TYPE organizations_type_old 
  USING "type"::text::organizations_type_old;

-- Drop the new enum type
DROP TYPE organizations_type;

-- Rename the old enum type to the original name
ALTER TYPE organizations_type_old RENAME TO organizations_type;

-- Add comment for documentation
COMMENT ON TYPE organizations_type IS 'Organization types including university, agency, and company';

-- Verify the change
SELECT unnest(enum_range(NULL::organizations_type)) as valid_organization_types; 