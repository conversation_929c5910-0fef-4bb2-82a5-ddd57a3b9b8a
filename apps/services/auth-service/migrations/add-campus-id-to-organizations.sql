-- Migration: Add campusId field to organizations table
-- Date: 2024-01-XX
-- Description: Add campusId field to support linking campus organizations to campus entities

-- Add new column to organizations table
ALTER TABLE organizations
ADD COLUMN "campusId" BIGINT;

-- Add comments for documentation
COMMENT ON COLUMN organizations."campusId" IS 'Reference to the campus entity in university service (nullable)';

-- Create index for better performance
CREATE INDEX idx_organizations_campus_id ON organizations("campusId");

-- Verify the change
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'organizations' AND column_name = 'campusId'; 