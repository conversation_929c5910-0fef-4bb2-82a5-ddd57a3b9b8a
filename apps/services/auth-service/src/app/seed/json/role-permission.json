[{"role": "Super Admin", "department": [{"name": "ApplyGoal Admin"}], "modules": [{"name": "Dashboard Access", "features": [{"name": "Overview of key metrics", "permissions": true, "subFeatures": []}, {"name": "Quick links to important actions", "permissions": true, "subFeatures": []}, {"name": "Notifications and reminders", "permissions": true, "subFeatures": []}]}, {"name": "Users Management", "features": [{"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": true, "subFeatures": []}, {"name": "Deactivate", "permissions": true, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "View Activity", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Assign role and permissions", "permissions": true}, {"name": "Assign a Team", "permissions": true}]}]}, {"name": "Leads Management", "features": [{"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Follow-up Tracking", "permissions": true, "subFeatures": []}, {"name": "Assign leades to user", "permissions": true, "subFeatures": []}, {"name": "lead Conversion to application", "permissions": true, "subFeatures": []}]}, {"name": "Application Management", "features": [{"name": "Track application status", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": true, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Documents Upload", "permissions": true}, {"name": "Documents Download", "permissions": true}, {"name": "Documents Delete", "permissions": true}, {"name": "Documents View", "permissions": true}, {"name": "Documents Edit", "permissions": true}, {"name": "ESL Start Date (Change)", "permissions": true}, {"name": "Intake (Change)", "permissions": true}, {"name": "Documents Main Copy (Check/Uncheck)", "permissions": true}, {"name": "Docuements Review (Approve/Reject)", "permissions": true}, {"name": "Documents Resubmission", "permissions": true}, {"name": "Notes Against Applications", "permissions": true}]}]}, {"name": "Financial Transaction", "features": [{"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": true, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Track payments", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Manage refunds and discounts", "permissions": true}, {"name": "Financial reporting", "permissions": true}, {"name": "Send Invoices", "permissions": true}]}]}, {"name": "HR Module Access", "features": [{"name": "Employee records", "permissions": true, "subFeatures": []}, {"name": "Attendance tracking", "permissions": true, "subFeatures": []}, {"name": "Leave management", "permissions": true, "subFeatures": []}, {"name": "Role assignments", "permissions": true, "subFeatures": []}]}, {"name": "System Settings", "features": [{"name": "CRM configuration", "permissions": true, "subFeatures": []}, {"name": "Role and permission setup", "permissions": true, "subFeatures": []}, {"name": "Notification preferences", "permissions": true, "subFeatures": []}, {"name": "Backup and security settings", "permissions": true, "subFeatures": []}]}, {"name": "Reports & Analytics", "features": [{"name": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": true, "subFeatures": []}, {"name": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"name": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"name": "Agency Management", "features": [{"name": "Onboard new agency partners", "permissions": true, "subFeatures": []}, {"name": "Track agency performance", "permissions": true, "subFeatures": []}, {"name": "View applications submitted by agencies", "permissions": true, "subFeatures": []}, {"name": "Set commission rates", "permissions": true, "subFeatures": []}]}, {"name": "University Management", "features": [{"name": "Add/update university profiles", "permissions": true, "subFeatures": []}, {"name": "Manage university requirements", "permissions": true, "subFeatures": []}, {"name": "Application intake calendars", "permissions": true, "subFeatures": []}, {"name": "Course list and program info", "permissions": true, "subFeatures": []}, {"name": "Request University for Onboarding", "permissions": true, "subFeatures": []}]}, {"name": "Student Profile Management", "features": [{"name": "Communication logs", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": true, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Store student's personal info", "permissions": true}, {"name": "Education history", "permissions": true}]}]}, {"name": "Support & Maintenance/IT/System Admins", "features": [{"name": "Submit support tickets", "permissions": true, "subFeatures": []}, {"name": "IT activity log", "permissions": true, "subFeatures": []}, {"name": "System status checks", "permissions": true, "subFeatures": []}, {"name": "Maintenance and Setup", "permissions": true, "subFeatures": []}]}, {"name": "Task Management", "features": [{"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": true, "subFeatures": []}, {"name": "Track task status and progress", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Assign task", "permissions": true}, {"name": "Set deadlines and priorities", "permissions": true}, {"name": "Link tasks to students, leads, or applications", "permissions": true}]}]}]}, {"role": "Application Officer", "department": [{"name": "ApplyGoal Admission Team"}], "modules": [{"name": "Dashboard Access", "features": [{"name": "Overview of key metrics", "permissions": false, "subFeatures": []}, {"name": "Quick links to important actions", "permissions": false, "subFeatures": []}, {"name": "Notifications and reminders", "permissions": false, "subFeatures": []}]}, {"name": "Users Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Deactivate", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "View Activity", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign role and permissions", "permissions": false}, {"name": "Assign a Team", "permissions": false}]}]}, {"name": "Leads Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"name": "Assign leades to user", "permissions": false, "subFeatures": []}, {"name": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"name": "Application Management", "features": [{"name": "Track application status", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Documents Upload", "permissions": true}, {"name": "Documents Download", "permissions": true}, {"name": "Documents Delete", "permissions": false}, {"name": "Documents View", "permissions": true}, {"name": "Documents Edit", "permissions": true}, {"name": "ESL Start Date (Change)", "permissions": true}, {"name": "Intake (Change)", "permissions": true}, {"name": "Documents Main Copy (Check/Uncheck)", "permissions": true}, {"name": "Docuements Review (Approve/Reject)", "permissions": false}, {"name": "Documents Resubmission", "permissions": false}, {"name": "Notes Against Applications", "permissions": false}]}]}, {"name": "Financial Transaction", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "Track payments", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Manage refunds and discounts", "permissions": false}, {"name": "Financial reporting", "permissions": false}, {"name": "Send Invoices", "permissions": false}]}]}, {"name": "HR Module Access", "features": [{"name": "Employee records", "permissions": false, "subFeatures": []}, {"name": "Attendance tracking", "permissions": false, "subFeatures": []}, {"name": "Leave management", "permissions": false, "subFeatures": []}, {"name": "Role assignments", "permissions": false, "subFeatures": []}]}, {"name": "System Settings", "features": [{"name": "CRM configuration", "permissions": false, "subFeatures": []}, {"name": "Role and permission setup", "permissions": false, "subFeatures": []}, {"name": "Notification preferences", "permissions": false, "subFeatures": []}, {"name": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"name": "Reports & Analytics", "features": [{"name": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": false, "subFeatures": []}, {"name": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"name": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"name": "Agency Management", "features": [{"name": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"name": "Track agency performance", "permissions": false, "subFeatures": []}, {"name": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"name": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"name": "University Management", "features": [{"name": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"name": "Manage university requirements", "permissions": false, "subFeatures": []}, {"name": "Application intake calendars", "permissions": false, "subFeatures": []}, {"name": "Course list and program info", "permissions": true, "subFeatures": []}, {"name": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"name": "Student Profile Management", "features": [{"name": "Communication logs", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Store student's personal info", "permissions": true}, {"name": "Education history", "permissions": true}]}]}, {"name": "Support & Maintenance/IT/System Admins", "features": [{"name": "Submit support tickets", "permissions": false, "subFeatures": []}, {"name": "IT activity log", "permissions": false, "subFeatures": []}, {"name": "System status checks", "permissions": false, "subFeatures": []}, {"name": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"name": "Task Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Track task status and progress", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign task", "permissions": false}, {"name": "Set deadlines and priorities", "permissions": false}, {"name": "Link tasks to students, leads, or applications", "permissions": false}]}]}]}, {"role": "Student", "department": "Student", "modules": [{"name": "Dashboard Access", "features": [{"name": "Overview of key metrics", "permissions": true, "subFeatures": []}, {"name": "Quick links to important actions", "permissions": true, "subFeatures": []}, {"name": "Notifications and reminders", "permissions": true, "subFeatures": []}]}, {"name": "Users Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Deactivate", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "View Activity", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign role and permissions", "permissions": false}, {"name": "Assign a Team", "permissions": false}]}]}, {"name": "Leads Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"name": "Assign leades to user", "permissions": false, "subFeatures": []}, {"name": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"name": "Application Management", "features": [{"name": "Track application status", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Documents Upload", "permissions": true}, {"name": "Documents Download", "permissions": true}, {"name": "Documents Delete", "permissions": true}, {"name": "Documents View", "permissions": true}, {"name": "Documents Edit", "permissions": true}, {"name": "ESL Start Date (Change)", "permissions": true}, {"name": "Intake (Change)", "permissions": true}, {"name": "Documents Main Copy (Check/Uncheck)", "permissions": true}, {"name": "Docuements Review (Approve/Reject)", "permissions": true}, {"name": "Documents Resubmission", "permissions": true}, {"name": "Notes Against Applications", "permissions": true}]}]}, {"name": "Financial Transaction", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "Track payments", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Manage refunds and discounts", "permissions": false}, {"name": "Financial reporting", "permissions": false}, {"name": "Send Invoices", "permissions": false}]}]}, {"name": "HR Module Access", "features": [{"name": "Employee records", "permissions": false, "subFeatures": []}, {"name": "Attendance tracking", "permissions": false, "subFeatures": []}, {"name": "Leave management", "permissions": false, "subFeatures": []}, {"name": "Role assignments", "permissions": false, "subFeatures": []}]}, {"name": "System Settings", "features": [{"name": "CRM configuration", "permissions": false, "subFeatures": []}, {"name": "Role and permission setup", "permissions": false, "subFeatures": []}, {"name": "Notification preferences", "permissions": false, "subFeatures": []}, {"name": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"name": "Reports & Analytics", "features": [{"name": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": false, "subFeatures": []}, {"name": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"name": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"name": "Agency Management", "features": [{"name": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"name": "Track agency performance", "permissions": false, "subFeatures": []}, {"name": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"name": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"name": "University Management", "features": [{"name": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"name": "Manage university requirements", "permissions": false, "subFeatures": []}, {"name": "Application intake calendars", "permissions": false, "subFeatures": []}, {"name": "Course list and program info", "permissions": true, "subFeatures": []}, {"name": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"name": "Student Profile Management", "features": [{"name": "Communication logs", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Store student's personal info", "permissions": true}, {"name": "Education history", "permissions": true}]}]}, {"name": "Support & Maintenance/IT/System Admins", "features": [{"name": "Submit support tickets", "permissions": false, "subFeatures": []}, {"name": "IT activity log", "permissions": false, "subFeatures": []}, {"name": "System status checks", "permissions": false, "subFeatures": []}, {"name": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"name": "Task Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Track task status and progress", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign task", "permissions": false}, {"name": "Set deadlines and priorities", "permissions": false}, {"name": "Link tasks to students, leads, or applications", "permissions": false}]}]}]}, {"role": "University Admin", "department": "University", "modules": [{"name": "Dashboard Access", "features": [{"name": "Overview of key metrics", "permissions": true, "subFeatures": []}, {"name": "Quick links to important actions", "permissions": true, "subFeatures": []}, {"name": "Notifications and reminders", "permissions": true, "subFeatures": []}]}, {"name": "Users Management", "features": [{"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Deactivate", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "View Activity", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign role and permissions", "permissions": false}, {"name": "Assign a Team", "permissions": false}]}]}, {"name": "Leads Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"name": "Assign leades to user", "permissions": false, "subFeatures": []}, {"name": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"name": "Application Management", "features": [{"name": "Track application status", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Documents Upload", "permissions": true}, {"name": "Documents Download", "permissions": true}, {"name": "Documents Delete", "permissions": false}, {"name": "Documents View", "permissions": true}, {"name": "Documents Edit", "permissions": false}, {"name": "ESL Start Date (Change)", "permissions": false}, {"name": "Intake (Change)", "permissions": false}, {"name": "Documents Main Copy (Check/Uncheck)", "permissions": false}, {"name": "Docuements Review (Approve/Reject)", "permissions": false}, {"name": "Documents Resubmission", "permissions": true}, {"name": "Notes Against Applications", "permissions": true}]}]}, {"name": "Financial Transaction", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "Track payments", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Manage refunds and discounts", "permissions": false}, {"name": "Financial reporting", "permissions": false}, {"name": "Send Invoices", "permissions": false}]}]}, {"name": "HR Module Access", "features": [{"name": "Employee records", "permissions": false, "subFeatures": []}, {"name": "Attendance tracking", "permissions": false, "subFeatures": []}, {"name": "Leave management", "permissions": false, "subFeatures": []}, {"name": "Role assignments", "permissions": false, "subFeatures": []}]}, {"name": "System Settings", "features": [{"name": "CRM configuration", "permissions": false, "subFeatures": []}, {"name": "Role and permission setup", "permissions": false, "subFeatures": []}, {"name": "Notification preferences", "permissions": false, "subFeatures": []}, {"name": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"name": "Reports & Analytics", "features": [{"name": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": true, "subFeatures": []}, {"name": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"name": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"name": "Agency Management", "features": [{"name": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"name": "Track agency performance", "permissions": false, "subFeatures": []}, {"name": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"name": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"name": "University Management", "features": [{"name": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"name": "Manage university requirements", "permissions": false, "subFeatures": []}, {"name": "Application intake calendars", "permissions": false, "subFeatures": []}, {"name": "Course list and program info", "permissions": false, "subFeatures": []}, {"name": "Request University for Onboarding", "permissions": true, "subFeatures": []}]}, {"name": "Student Profile Management", "features": [{"name": "Communication logs", "permissions": false, "subFeatures": []}, {"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Store student's personal info", "permissions": false}, {"name": "Education history", "permissions": false}]}]}, {"name": "Support & Maintenance/IT/System Admins", "features": [{"name": "Submit support tickets", "permissions": false, "subFeatures": []}, {"name": "IT activity log", "permissions": false, "subFeatures": []}, {"name": "System status checks", "permissions": false, "subFeatures": []}, {"name": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"name": "Task Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Track task status and progress", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign task", "permissions": false}, {"name": "Set deadlines and priorities", "permissions": false}, {"name": "Link tasks to students, leads, or applications", "permissions": false}]}]}]}, {"role": "Designated School Official (DSO)", "department": [{"name": "University Admission Team"}], "modules": [{"name": "Dashboard Access", "features": [{"name": "Overview of key metrics", "permissions": true, "subFeatures": []}, {"name": "Quick links to important actions", "permissions": true, "subFeatures": []}, {"name": "Notifications and reminders", "permissions": false, "subFeatures": []}]}, {"name": "Users Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Deactivate", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "View Activity", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign role and permissions", "permissions": false}, {"name": "Assign a Team", "permissions": false}]}]}, {"name": "Leads Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"name": "Assign leades to user", "permissions": false, "subFeatures": []}, {"name": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"name": "Application Management", "features": [{"name": "Track application status", "permissions": false, "subFeatures": []}, {"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Documents Upload", "permissions": true}, {"name": "Documents Download", "permissions": true}, {"name": "Documents Delete", "permissions": true}, {"name": "Documents View", "permissions": true}, {"name": "Documents Edit", "permissions": false}, {"name": "ESL Start Date (Change)", "permissions": false}, {"name": "Intake (Change)", "permissions": false}, {"name": "Documents Main Copy (Check/Uncheck)", "permissions": false}, {"name": "Docuements Review (Approve/Reject)", "permissions": false}, {"name": "Documents Resubmission", "permissions": false}, {"name": "Notes Against Applications", "permissions": false}]}]}, {"name": "Financial Transaction", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "Track payments", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Manage refunds and discounts", "permissions": false}, {"name": "Financial reporting", "permissions": false}, {"name": "Send Invoices", "permissions": false}]}]}, {"name": "HR Module Access", "features": [{"name": "Employee records", "permissions": false, "subFeatures": []}, {"name": "Attendance tracking", "permissions": false, "subFeatures": []}, {"name": "Leave management", "permissions": false, "subFeatures": []}, {"name": "Role assignments", "permissions": false, "subFeatures": []}]}, {"name": "System Settings", "features": [{"name": "CRM configuration", "permissions": false, "subFeatures": []}, {"name": "Role and permission setup", "permissions": false, "subFeatures": []}, {"name": "Notification preferences", "permissions": false, "subFeatures": []}, {"name": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"name": "Reports & Analytics", "features": [{"name": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": false, "subFeatures": []}, {"name": "Export to PDF/Excel", "permissions": false, "subFeatures": []}, {"name": "Dashboard widgets with filters", "permissions": false, "subFeatures": []}]}, {"name": "Agency Management", "features": [{"name": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"name": "Track agency performance", "permissions": false, "subFeatures": []}, {"name": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"name": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"name": "University Management", "features": [{"name": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"name": "Manage university requirements", "permissions": false, "subFeatures": []}, {"name": "Application intake calendars", "permissions": false, "subFeatures": []}, {"name": "Course list and program info", "permissions": false, "subFeatures": []}, {"name": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"name": "Student Profile Management", "features": [{"name": "Communication logs", "permissions": false, "subFeatures": []}, {"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Store student's personal info", "permissions": false}, {"name": "Education history", "permissions": false}]}]}, {"name": "Support & Maintenance/IT/System Admins", "features": [{"name": "Submit support tickets", "permissions": false, "subFeatures": []}, {"name": "IT activity log", "permissions": false, "subFeatures": []}, {"name": "System status checks", "permissions": false, "subFeatures": []}, {"name": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"name": "Task Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Track task status and progress", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign task", "permissions": false}, {"name": "Set deadlines and priorities", "permissions": false}, {"name": "Link tasks to students, leads, or applications", "permissions": false}]}]}]}, {"role": "Admission Team", "department": [{"name": "University Admission Team"}], "modules": [{"name": "Dashboard Access", "features": [{"name": "Overview of key metrics", "permissions": true, "subFeatures": []}, {"name": "Quick links to important actions", "permissions": true, "subFeatures": []}, {"name": "Notifications and reminders", "permissions": true, "subFeatures": []}]}, {"name": "Users Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Deactivate", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "View Activity", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign role and permissions", "permissions": false}, {"name": "Assign a Team", "permissions": false}]}]}, {"name": "Leads Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Follow-up Tracking", "permissions": false, "subFeatures": []}, {"name": "Assign leades to user", "permissions": false, "subFeatures": []}, {"name": "lead Conversion to application", "permissions": false, "subFeatures": []}]}, {"name": "Application Management", "features": [{"name": "Track application status", "permissions": true, "subFeatures": []}, {"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": true, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Documents Upload", "permissions": true}, {"name": "Documents Download", "permissions": true}, {"name": "Documents Delete", "permissions": true}, {"name": "Documents View", "permissions": true}, {"name": "Documents Edit", "permissions": true}, {"name": "ESL Start Date (Change)", "permissions": true}, {"name": "Intake (Change)", "permissions": true}, {"name": "Documents Main Copy (Check/Uncheck)", "permissions": false}, {"name": "Docuements Review (Approve/Reject)", "permissions": true}, {"name": "Documents Resubmission", "permissions": true}, {"name": "Notes Against Applications", "permissions": true}]}]}, {"name": "Financial Transaction", "features": [{"name": "Create", "permissions": true, "subFeatures": []}, {"name": "Delete", "permissions": true, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Track payments", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": true, "subFeatures": [{"name": "Manage refunds and discounts", "permissions": true}, {"name": "Financial reporting", "permissions": true}, {"name": "Send Invoices", "permissions": true}]}]}, {"name": "HR Module Access", "features": [{"name": "Employee records", "permissions": false, "subFeatures": []}, {"name": "Attendance tracking", "permissions": false, "subFeatures": []}, {"name": "Leave management", "permissions": false, "subFeatures": []}, {"name": "Role assignments", "permissions": false, "subFeatures": []}]}, {"name": "System Settings", "features": [{"name": "CRM configuration", "permissions": false, "subFeatures": []}, {"name": "Role and permission setup", "permissions": false, "subFeatures": []}, {"name": "Notification preferences", "permissions": false, "subFeatures": []}, {"name": "Backup and security settings", "permissions": false, "subFeatures": []}]}, {"name": "Reports & Analytics", "features": [{"name": "Generate reports by feature (Leads, Applications, Finance, etc.)", "permissions": true, "subFeatures": []}, {"name": "Export to PDF/Excel", "permissions": true, "subFeatures": []}, {"name": "Dashboard widgets with filters", "permissions": true, "subFeatures": []}]}, {"name": "Agency Management", "features": [{"name": "Onboard new agency partners", "permissions": false, "subFeatures": []}, {"name": "Track agency performance", "permissions": false, "subFeatures": []}, {"name": "View applications submitted by agencies", "permissions": false, "subFeatures": []}, {"name": "Set commission rates", "permissions": false, "subFeatures": []}]}, {"name": "University Management", "features": [{"name": "Add/update university profiles", "permissions": false, "subFeatures": []}, {"name": "Manage university requirements", "permissions": false, "subFeatures": []}, {"name": "Application intake calendars", "permissions": true, "subFeatures": []}, {"name": "Course list and program info", "permissions": true, "subFeatures": []}, {"name": "Request University for Onboarding", "permissions": false, "subFeatures": []}]}, {"name": "Student Profile Management", "features": [{"name": "Communication logs", "permissions": false, "subFeatures": []}, {"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "View", "permissions": true, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Store student's personal info", "permissions": false}, {"name": "Education history", "permissions": false}]}]}, {"name": "Support & Maintenance/IT/System Admins", "features": [{"name": "Submit support tickets", "permissions": false, "subFeatures": []}, {"name": "IT activity log", "permissions": false, "subFeatures": []}, {"name": "System status checks", "permissions": false, "subFeatures": []}, {"name": "Maintenance and Setup", "permissions": false, "subFeatures": []}]}, {"name": "Task Management", "features": [{"name": "Create", "permissions": false, "subFeatures": []}, {"name": "Delete", "permissions": false, "subFeatures": []}, {"name": "Track task status and progress", "permissions": false, "subFeatures": []}, {"name": "Edit", "permissions": false, "subFeatures": [{"name": "Assign task", "permissions": false}, {"name": "Set deadlines and priorities", "permissions": false}, {"name": "Link tasks to students, leads, or applications", "permissions": false}]}]}]}]