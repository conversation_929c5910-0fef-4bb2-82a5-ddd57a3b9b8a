{"modules": [{"name": "Dashboard Access", "features": [{"name": "Overview of key metrics", "subFeatures": []}, {"name": "Quick links to important actions", "subFeatures": []}, {"name": "Notifications and reminders", "subFeatures": []}]}, {"name": "Users Management", "features": [{"name": "Create", "subFeatures": []}, {"name": "Delete", "subFeatures": []}, {"name": "Deactivate", "subFeatures": []}, {"name": "View", "subFeatures": []}, {"name": "View Activity", "subFeatures": []}, {"name": "Edit", "subFeatures": [{"name": "Assign role and permissions"}, {"name": "Assign a Team"}]}]}, {"name": "Leads Management", "features": [{"name": "Create", "subFeatures": []}, {"name": "Follow-up Tracking", "subFeatures": []}, {"name": "Assign leades to user", "subFeatures": []}, {"name": "lead Conversion to application", "subFeatures": []}]}, {"name": "Application Management", "features": [{"name": "Track application status", "subFeatures": []}, {"name": "Create", "subFeatures": []}, {"name": "Delete", "subFeatures": []}, {"name": "View", "subFeatures": []}, {"name": "Edit", "subFeatures": [{"name": "Documents Upload"}, {"name": "Documents Download"}, {"name": "Documents Delete"}, {"name": "Documents View"}, {"name": "Documents Edit"}, {"name": "ESL Start Date (Change)"}, {"name": "Intake (Change)"}, {"name": "Documents Main Copy (Check/Uncheck)"}, {"name": "Docuements Review (Approve/Reject)"}, {"name": "Documents Resubmission"}, {"name": "Notes Against Applications"}]}]}, {"name": "Financial Transaction", "features": [{"name": "Create", "subFeatures": []}, {"name": "Delete", "subFeatures": []}, {"name": "View", "subFeatures": []}, {"name": "Track payments", "subFeatures": []}, {"name": "Edit", "subFeatures": [{"name": "Manage refunds and discounts"}, {"name": "Financial reporting"}, {"name": "Send Invoices"}]}]}, {"name": "HR Module Access", "features": [{"name": "Employee records", "subFeatures": []}, {"name": "Attendance tracking", "subFeatures": []}, {"name": "Leave management", "subFeatures": []}, {"name": "Role assignments", "subFeatures": []}]}, {"name": "System Settings", "features": [{"name": "CRM configuration", "subFeatures": []}, {"name": "Role and permission setup", "subFeatures": []}, {"name": "Notification preferences", "subFeatures": []}, {"name": "Backup and security settings", "subFeatures": []}]}, {"name": "Reports & Analytics", "features": [{"name": "Generate reports by feature (Leads, Applications, Finance, etc.)", "subFeatures": []}, {"name": "Export to PDF/Excel", "subFeatures": []}, {"name": "Dashboard widgets with filters", "subFeatures": []}]}, {"name": "Agency Management", "features": [{"name": "Onboard new agency partners", "subFeatures": []}, {"name": "Track agency performance", "subFeatures": []}, {"name": "View applications submitted by agencies", "subFeatures": []}, {"name": "Set commission rates", "subFeatures": []}]}, {"name": "University Management", "features": [{"name": "Add/update university profiles", "subFeatures": []}, {"name": "Manage university requirements", "subFeatures": []}, {"name": "Application intake calendars", "subFeatures": []}, {"name": "Course list and program info", "subFeatures": []}, {"name": "Request University for Onboarding", "subFeatures": []}]}, {"name": "Student Profile Management", "features": [{"name": "Communication logs", "subFeatures": []}, {"name": "Create", "subFeatures": []}, {"name": "Delete", "subFeatures": []}, {"name": "View", "subFeatures": []}, {"name": "Edit", "subFeatures": [{"name": "Store student's personal info"}, {"name": "Education history"}]}]}, {"name": "Support & Maintenance/IT/System Admins", "features": [{"name": "Submit support tickets", "subFeatures": []}, {"name": "IT activity log", "subFeatures": []}, {"name": "System status checks", "subFeatures": []}, {"name": "Maintenance and Setup", "subFeatures": []}]}, {"name": "Task Management", "features": [{"name": "Create", "subFeatures": []}, {"name": "Delete", "subFeatures": []}, {"name": "Track task status and progress", "subFeatures": []}, {"name": "Edit", "subFeatures": [{"name": "Assign task"}, {"name": "Set deadlines and priorities"}, {"name": "Link tasks to students, leads, or applications"}]}]}]}