export interface RegisterRequest {
  name: string;
  email: string;
  nationality: string;
  organizationName: string;
  password: string;
  phone: string;
  departmentName: string;
  roleName: string;
  ipAddress?: string;
  userAgent?: string;
  activateImmediately?: boolean;
  campusId?: bigint;  // Optional campus ID for campus organization lookup
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  userId?: number; // Add userId field to return the created user's ID
}

export interface LoginRequest {
  email: string;
  password: string;
  ipAddress: string;
  userAgent?: string;
  campusId?: number;  // Optional campus ID for validation
}

export interface LoginResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: any;
  studentId?: string;
  employeeId?: string;        // Employee ID if user is an employee
  campusId?: number;          // Campus ID if user belongs to a campus organization
}

export interface LogoutRequest {
  accessToken: string;
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface ValidateTokenRequest {
  token: string;
}

export interface ValidateTokenResponse {
  valid: boolean;
  message: string;
  user?: any;
}

export interface RefreshTokenRequest {
  refreshToken?: string;
}

export interface RefreshTokenResponse {
  success?: boolean;
  message?: string;
  accessToken?: string;
  refreshToken?: string;
}

export interface GetUserByEmailRequest {
  email: string;
}

export interface GetUserByEmailResponse {
  success: boolean;
  message: string;
  user?: any;
}

export interface CreateAuditLogRequest {
  userId: number;
  orgId?: bigint;
  userRole: string;
  actions: string;
  serviceName: string;
  resourceType: string;
  resourceId: number;
  description: string;
  metadata?: { [key: string]: string };
  ipAddress?: string;
  userAgent?: string;
  source?: string;
}

export interface GenerateOtpRequest {
  email: string;
  type: string; // 'registration', 'login', 'password-reset', etc.
  ipAddress?: string;
  userAgent?: string;
}

export interface GenerateOtpResponse {
  success: boolean;
  message: string;
  expiresAt?: Date;
}

export interface VerifyOtpRequest {
  email: string;
  otp: string;
  type: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface VerifyOtpResponse {
  success: boolean;
  message: string;
}

export interface SsoAuthRequest {
  provider: 'google'; // for now
  token: string;
  email: string;
  name: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface SsoAuthResponse {
  success: boolean;
  message: string;
  accessToken?: string;
  refreshToken?: string;
  user?: any;
}

export interface ForgotPasswordRequest {
  email: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface ForgotPasswordResponse {
  success: boolean;
  message: string;
  resetToken?: string; // Only for development/testing
}

export interface ResetPasswordRequest {
  email: string;
  resetToken: string;
  newPassword: string;
  confirmPassword: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface ResetPasswordResponse {
  success: boolean;
  message: string;
}

export interface VerifyResetTokenRequest {
  email: string;
  resetToken: string;
}

export interface VerifyResetTokenResponse {
  valid: boolean;
  message: string;
  expiresAt?: Date;
}
