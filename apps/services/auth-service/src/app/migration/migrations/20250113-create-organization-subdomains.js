'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('organization_subdomains', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.BIGINT
      },
      subdomain: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
        comment: 'Subdomain (e.g., "university1", "campus1")'
      },
      organizationId: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: 'Organization ID from organizations table',
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      universityId: {
        type: Sequelize.BIGINT,
        allowNull: true,
        comment: 'University ID (nullable for non-university organizations)'
      },
      campusId: {
        type: Sequelize.BIGINT,
        allowNull: true,
        comment: 'Campus ID (nullable for non-campus organizations)'
      },
      description: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'Additional metadata or notes'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether this subdomain mapping is active'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      deletedAt: {
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('organization_subdomains', ['organizationId']);
    await queryInterface.addIndex('organization_subdomains', ['universityId']);
    await queryInterface.addIndex('organization_subdomains', ['campusId']);
    await queryInterface.addIndex('organization_subdomains', ['isActive']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('organization_subdomains');
  }
}; 