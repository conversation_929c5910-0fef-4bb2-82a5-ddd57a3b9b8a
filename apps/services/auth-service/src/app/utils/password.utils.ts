import { Logger } from '@nestjs/common';
import { Op } from 'sequelize';

// ─── Helper Methods ─────────────────────────────────────────────────
export function generateSecureToken(): string {
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
}

export function isValidPassword(password: string): boolean {
  // Password must be at least 8 characters long and contain:
  // - At least one uppercase letter
  // - At least one lowercase letter
  // - At least one number
  // - At least one special character
  const passwordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

export async function sendPasswordResetEmail(
  email: string,
  name: string,
  resetToken: string
): Promise<void> {
  try {
    const resetUrl = `${
      process.env.FRONTEND_URL || 'http://localhost:3000'
    }/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;

    const emailPayload = {
      to: email,
      subject: 'Password Reset Request - ApplyGoal',
      template: 'password-reset',
      data: {
        name,
        resetUrl,
        expiryTime: '1 hour',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>'
      }
    };

    await this.rmqPublisher.sendEmail(emailPayload);
    Logger.log(`Password reset email sent to: ${email}`);
  } catch (error) {
    Logger.error(
      `Failed to send password reset email to ${email}:`,
      error
    );
    // Don't throw error here as the reset token is still valid
  }
}

export async function sendPasswordChangeConfirmationEmail(
  email: string,
  name: string
): Promise<void> {
  try {
    const emailPayload = {
      to: email,
      subject: 'Password Changed Successfully - ApplyGoal',
      template: 'password-change-confirmation',
      data: {
        name,
        changeTime: new Date().toLocaleString(),
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>'
      }
    };

    await this.rmqPublisher.sendEmail(emailPayload);
    Logger.log(`Password change confirmation email sent to: ${email}`);
  } catch (error) {
    Logger.error(
      `Failed to send password change confirmation email to ${email}:`,
      error
    );
    // Don't throw error here as the password was already changed successfully
  }
}

// ─── Cleanup Methods ─────────────────────────────────────────────────
export async function cleanupExpiredResetTokens(): Promise<void> {
  try {
    const result = await this.passwordResetTokenModel.destroy({
      where: {
        expiresAt: {
          [Op.lt]: new Date()
        }
      }
    });
    Logger.log(`Cleaned up ${result} expired password reset tokens`);
  } catch (error) {
    Logger.error('Failed to cleanup expired reset tokens:', error);
  }
}
