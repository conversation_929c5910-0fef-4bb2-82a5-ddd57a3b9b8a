import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Role } from '../user/model/role.model';
import { Organization } from '../organization/organization.model';
import { Feature } from '../user/model/feature.model';
import { RoleFeaturePermission } from '../user/model/role-feature-permission.model';

export interface CreateRoleRequest {
  name: string;
  description?: string;
  organizationId?: number;
  isSystemRole?: boolean;
  permissions?: {
    featureId: number;
    hasPermission: boolean;
  }[];
}

export interface UpdateRoleRequest {
  id: number;
  name?: string;
  roleName?: string; // Added for backward compatibility
  description?: string;
  permissions?: {
    featureId: number;
    hasPermission: boolean;
  }[];
}

export interface RoleResponse {
  success: boolean;
  message: string;
  role?: Role;
}

export interface ListRolesRequest {
  organizationId?: number;
  includeSystemRoles?: boolean;
  page?: number;
  limit?: number;
}

export interface ListRolesResponse {
  success: boolean;
  message: string;
  roles: Role[];
  total: number;
  page: number;
  limit: number;
}

@Injectable()
export class RoleService {
  private readonly logger = new Logger(RoleService.name);

  constructor(
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    @InjectModel(Feature)
    private readonly featureModel: typeof Feature,
    @InjectModel(RoleFeaturePermission)
    private readonly roleFeaturePermissionModel: typeof RoleFeaturePermission
  ) {}

  async createRole(request: CreateRoleRequest): Promise<RoleResponse> {
    try {
      this.logger.log(
        `Creating role: ${request.name} for organization: ${request.organizationId}`
      );

      // Validate organization if provided
      if (request.organizationId) {
        const organization = await this.organizationModel.findByPk(
          request.organizationId
        );
        if (!organization) {
          throw new BadRequestException(
            `Organization with ID ${request.organizationId} not found`
          );
        }
      }

      // Check if role already exists for this organization
      const existingRole = await this.roleModel.findOne({
        where: {
          name: request.name,
          organizationId: request.organizationId || null,
        },
      });

      if (existingRole) {
        const scope = request.organizationId ? 'organization' : 'system';
        throw new BadRequestException(
          `Role '${request.name}' already exists for this ${scope}`
        );
      }

      // Create role
      const role = await this.roleModel.create({
        name: request.name,
        description: request.description,
        organizationId: request.organizationId,
        isSystemRole: request.isSystemRole || false,
      });

      // Assign permissions if provided
      if (request.permissions?.length) {
        await this.assignPermissions(Number(role.id), request.permissions);
      }

      return {
        success: true,
        message: 'Role created successfully',
        role,
      };
    } catch (error) {
      this.logger.error(`Error creating role: ${error.message}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error('Failed to create role');
    }
  }

  async updateRole(request: UpdateRoleRequest): Promise<RoleResponse> {
    try {
      this.logger.log(`Updating role: ${request.id}`);

      const role = await this.roleModel.findByPk(request.id);
      if (!role) {
        throw new NotFoundException(`Role with ID ${request.id} not found`);
      }

      // Update role fields - handle both name and roleName for backward compatibility
      if (request.name) role.name = request.name;
      if (request.roleName) role.name = request.roleName; // Add support for roleName field
      if (request.description !== undefined)
        role.description = request.description;

      await role.save();

      // Update permissions if provided
      if (request.permissions?.length) {
        await this.assignPermissions(Number(role.id), request.permissions);
      }

      return {
        success: true,
        message: 'Role updated successfully',
        role,
      };
    } catch (error) {
      this.logger.error(`Error updating role: ${error.message}`, error.stack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new Error('Failed to update role');
    }
  }

  async listRoles(request: ListRolesRequest): Promise<ListRolesResponse> {
    try {
      const page = request.page || 1;
      const limit = request.limit || 10;
      const offset = (page - 1) * limit;

      const whereClause: any = {};

      if (request.organizationId) {
        whereClause.organizationId = request.organizationId;
      }

      if (!request.includeSystemRoles) {
        whereClause.isSystemRole = false;
      }

      const { rows: roles, count: total } =
        await this.roleModel.findAndCountAll({
          where: whereClause,
          include: [
            {
              model: Organization,
              as: 'organization',
              attributes: ['id', 'name', 'type'],
            },
          ],
          limit,
          offset,
          order: [['name', 'ASC']],
        });

      return {
        success: true,
        message: 'Roles retrieved successfully',
        roles,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error listing roles: ${error.message}`, error.stack);
      throw new Error('Failed to list roles');
    }
  }

  async getRoleById(id: number): Promise<Role> {
    const role = await this.roleModel.findByPk(id, {
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name', 'type'],
        },
        {
          model: Feature,
          as: 'features',
          through: {
            where: { isAllowed: true },
          },
        },
      ],
    });

    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    return role;
  }

  async deleteRole(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const role = await this.roleModel.findByPk(id);
      if (!role) {
        throw new NotFoundException(`Role with ID ${id} not found`);
      }

      // Check if role is a system role
      if (role.isSystemRole) {
        throw new BadRequestException('Cannot delete system roles');
      }

      // Delete role permissions first
      await this.roleFeaturePermissionModel.destroy({
        where: { roleId: id },
      });

      // Delete role
      await role.destroy();

      return {
        success: true,
        message: 'Role deleted successfully',
      };
    } catch (error) {
      this.logger.error(`Error deleting role: ${error.message}`, error.stack);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new Error('Failed to delete role');
    }
  }

  async getRolesForOrganization(organizationId: number): Promise<Role[]> {
    return this.roleModel.findAll({
      where: {
        [require('sequelize').Op.or]: [
          { organizationId },
          { isSystemRole: true, organizationId: null },
        ],
      },
      include: [
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name', 'type'],
        },
      ],
      order: [['name', 'ASC']],
    });
  }

  private async assignPermissions(
    roleId: number,
    permissions: { featureId: number; hasPermission: boolean }[]
  ): Promise<void> {
    // Remove existing permissions
    await this.roleFeaturePermissionModel.destroy({
      where: { roleId },
    });

    // Add new permissions
    const permissionData = permissions
      .filter((p) => p.hasPermission)
      .map((p) => ({
        roleId,
        featureId: p.featureId,
        isAllowed: true,
      }));

    if (permissionData.length > 0) {
      await this.roleFeaturePermissionModel.bulkCreate(permissionData);
    }
  }
}
