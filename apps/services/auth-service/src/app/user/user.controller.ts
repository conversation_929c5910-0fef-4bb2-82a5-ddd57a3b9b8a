import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { UserService } from './user.service';
import { status } from '@grpc/grpc-js';
import { GetRolesWithDetailsResponse } from './user.interface';
import { RpcException } from '@nestjs/microservices';

@Controller('user')
export class UserController {
  private readonly logger = new Logger(UserController.name);

  constructor(private readonly userService: UserService) {}

  //  -------------- Users -----------------------
  // #region

  // #endregion

  // ---------------- modules ------------------
  // #region
  @GrpcMethod('AuthService', 'BulkCreateModules')
  async bulkCreateModules(request: any) {
    try {
      this.logger.log(`gRPC CreateModule request: ${request.name}`);
      return await this.userService.bulkCreateModule(request);
    } catch (error) {
      this.logger.error(`CreateModule error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to create feature',
          details: error.stack || '',
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'CreateModule')
  async createModule(request: any) {
    try {
      this.logger.log(`gRPC CreateModule request: ${request.name}`);
      return await this.userService.createModule(request);
    } catch (error) {
      this.logger.error(`CreateModule error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to create feature',
          details: error.stack || '',
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'ListModules')
  async listModules(request: any) {
    try {
      this.logger.log(`gRPC ListModules request`);
      return await this.userService.listModules(
        request.userId,
        request.roleName,
        request.ipAddress,
        request.userAgent
      );
    } catch (error) {
      this.logger.error(`ListModules error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to list modules',
          details: error.stack || '',
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'GetRolesWithDetails')
  async getRolesWithDetails(request: any) {
    try {
      // this.logger.log(`gRPC getRolesWithDetails request: ${request}`);
      const svc: GetRolesWithDetailsResponse =
        await this.userService.getRolesWithDetails(request);

      return {
        success: svc.success,
        message: svc.message,
        roles: svc.roles.map((r) => ({
          id: r.id,
          role: r.role,
          department: r.department.map((d) => ({
            id: d.id,
            name: d.name,
            users: d.users.map((u) => ({
              id: u.id,
              name: u.name,
              email: u.email,
            })),
          })),
          modules: r.modules.map((m) => ({
            id: m.id,
            module: m.module,
            features: m.features.map((f) => ({
              id: f.id,
              feature: f.feature,
              permissions: f.permissions,
              subFeatures: f.subFeatures.map((sf) => ({
                id: sf.id,
                subFeature: sf.subFeature,
                permissions: sf.permissions,
              })),
            })),
          })),
        })),
      };
    } catch (error) {
      this.logger.error(
        `getRolesWithDetails error: ${error.message}`,
        error.stack
      );
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to get roles and permissions',
          details: error.stack || '',
        },
      };
    }
  }

  // #endregion

  // ----------- departments -----------------
  // #region
  // Removed duplicate CreateDepartment method - handled by DepartmentController

  @GrpcMethod('AuthService', 'AssignDepartment')
  async assignDepartment(request: any) {
    try {
      this.logger.log(
        `gRPC AssignDepartment: userId=${request.userId}, departmentId=${request.departmentId}`
      );
      return await this.userService.assignDepartment(request);
    } catch (error) {
      this.logger.error(
        `AssignDepartment error: ${error.message}`,
        error.stack
      );
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to assign department',
          details: error.stack || '',
        },
      };
    }
  }
  // Removed duplicate ListDepartments method - handled by DepartmentController

  @GrpcMethod('AuthService', 'ListDepartmentsWithUsers')
  async listDepartmentsWithUsers(request: any) {
    try {
      this.logger.log(`gRPC ListDepartmentsWithUsers request`);
      return await this.userService.listDepartmentsWithUsers(request);
    } catch (error) {
      this.logger.error(
        `ListDepartmentsWithUsers error: ${error.message}`,
        error.stack
      );
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to list departments',
          details: error.stack || '',
        },
      };
    }
  }
  // #endregion

  //  --------- Roles -------------------------
  // #region
  @GrpcMethod('AuthService', 'GetRoleDetails')
  async getRoleDetails(request: any) {
    try {
      this.logger.log(`gRPC GetRoleDetails request: ${request.name}`);
      return await this.userService.getRoleDetails(request);
    } catch (error) {
      this.logger.error(`GetRoleDetails error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to get role details',
          details: error.stack || '',
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'CreateRoleWithDetails')
  async createRoleWithDetails(request: any) {
    try {
      this.logger.debug(
        `gRPC CreateRoleWithDetails request: ${request.name} & ${request.id}`
      );
      return await this.userService.createOrUpdateRoleWithDetails(request);
    } catch (error) {
      this.logger.error(
        `CreateRoleWithDetails error: ${error.message}`,
        error.stack
      );
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to create role',
          details: error.stack || '',
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'UpdateRole')
  async updateRole(request: any) {
    try {
      this.logger.log(`gRPC UpdateRole request: ${request.id}`);
      return await this.userService.updateRole(request);
    } catch (error) {
      this.logger.error(`UpdateRole error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to update role',
          details: error.stack || '',
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'DeleteRole')
  async deleteRole(request: any) {
    try {
      this.logger.log(`gRPC DeleteRole request: ${request.id}`);
      return await this.userService.deleteRole(request);
    } catch (error) {
      this.logger.error(`DeleteRole error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to delete role',
          details: error.stack || '',
        },
      };
    }
  }

  // #endregion

  // ------------ University ----------------
  // #region
  @GrpcMethod('AuthService', 'CreateUniversity')
  async createUniversity(request: any) {
    try {
      this.logger.log(`gRPC CreateUniversity request: ${request.name}`);
      return await this.userService.createUniversity(request);
    } catch (error) {
      this.logger.error(
        `CreateUniversity error: ${error.message}`,
        error.stack
      );
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to create university',
          details: error.stack || '',
        },
      };
    }
  }

  @GrpcMethod('AuthService', 'CreateCampusAdminUser')
  async createCampusAdminUser(request: any) {
    try {
      this.logger.log(`gRPC CreateCampusAdminUser request: ${request.name}`);
      return await this.userService.createCampusAdminUser(request);
    } catch (error) {
      this.logger.error(`CreateCampusAdminUser error: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message,
      });
    }
  }

  @GrpcMethod('AuthService', 'CreateCampusUser')
  async createCampusUser(request: any) {
    try {
      this.logger.log(`gRPC CreateCampusUser request: ${request.name} with role: ${request.roleName}`);
      return await this.userService.createCampusUser(request);
    } catch (error) {
      this.logger.error(`CreateCampusUser error: ${error.message}`);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message,
      });
    }
  }
  // #endregion

  // ------------- Agency ----------------
  // #region
  @GrpcMethod('AuthService', 'CreateAgency')
  async createAgency(request: any) {
    try {
      this.logger.log(`gRPC CreateAgency request: ${request.name}`);
      return await this.userService.createAgency(request);
    } catch (error) {
      this.logger.error(`CreateAgency error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to create agency',
          details: error.stack || '',
        },
      };
    }
  }
  // #endregion

  // ------------- Student ----------------
  // #region
  @GrpcMethod('AuthService', 'CreateStudent')
  async createStudent(request: any) {
    try {
      this.logger.log(`gRPC CreateStudent request: ${request.name}`);
      return await this.userService.createStudent(request);
    } catch (error) {
      this.logger.error(`CreateStudent error: ${error.message}`, error.stack);
      return {
        error: {
          code: status.INTERNAL,
          message: error.message || 'Failed to create student',
          details: error.stack || '',
        },
      };
    }
  }
  // #endregion
}
