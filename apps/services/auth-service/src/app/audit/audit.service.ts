import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Client, ClientGrpc } from '@nestjs/microservices';
import { Transport } from '@nestjs/microservices';
import { join } from 'path';
import { Observable, throwError } from 'rxjs';
import { catchError, timeout, retry } from 'rxjs/operators';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';

interface AuditLog {
  id: number;
  userId: number;
  orgId?: bigint;
  userRole: string;
  actions: string;
  serviceName: string;
  resourceType: string;
  resourceId: number;
  description: string;
  metadata: { [key: string]: string };
  ipAddress: string;
  userAgent: string;
  timeStamp: Date;
  source?: string;
}

interface GetAuditLogRequest {
  id: string;
}

interface AuditLogResponse {
  audit_log: AuditLog;
  message: string;
}

interface AuditService {
  createAuditLog(request: CreateAuditLogRequest): Observable<AuditLogResponse>;
  getAuditLog(request: GetAuditLogRequest): Observable<AuditLogResponse>;
}

@Injectable()
export class AuditClientService implements OnModuleInit {
  private readonly logger = new Logger(AuditClientService.name);

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'audit',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/audit/audit.proto'
      ),
      url: 'audit-logging:50051',
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      },
    },
  })
  private readonly client: ClientGrpc;

  private auditService: AuditService;

  onModuleInit() {
    this.auditService = this.client.getService<AuditService>('AuditService');
  }

  private handleError(operation = 'operation') {
    return (error: any) => {
      this.logger.error(`${operation} failed: ${error.message}`, error.stack);
      return throwError(() => error);
    };
  }

  createAuditLog(request: CreateAuditLogRequest): Observable<AuditLogResponse> {
    // Ensure all required fields have values and match the proto file field names
    const sanitizedRequest = {
      userId: request.userId, // Fallback to 0 if missing
      orgId: request.orgId,
      userRole: request.userRole || 'system',
      actions: request.actions || 'UNKNOWN',
      serviceName: request.serviceName || 'auth-service',
      resourceType: request.resourceType || 'UNKNOWN',
      resourceId: request.resourceId,
      description: request.description || 'No description provided',
      metadata: request.metadata || {},
      ipAddress: request.ipAddress || '',
      userAgent: request.userAgent || '',
      source: request.source || 'api',
    };

    this.logger.debug(
      `Creating audit log: ${JSON.stringify(sanitizedRequest)}`
    );

    return this.auditService
      .createAuditLog(sanitizedRequest)
      .pipe(
        timeout(5000),
        retry(3),
        catchError(this.handleError('createAuditLog'))
      );
  }

  getAuditLog(request: GetAuditLogRequest): Observable<AuditLogResponse> {
    return this.auditService
      .getAuditLog(request)
      .pipe(
        timeout(5000),
        retry(3),
        catchError(this.handleError('getAuditLog'))
      );
  }
}
