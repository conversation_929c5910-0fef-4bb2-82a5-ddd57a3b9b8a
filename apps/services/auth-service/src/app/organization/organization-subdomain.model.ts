import { BaseModel } from '@apply-goal-backend/database';
import {
  Table,
  Column,
  Model,
  DataType,
  Index,
  AllowNull,
  Length,
} from 'sequelize-typescript';

@Table({
  tableName: 'organization_subdomains',
  timestamps: true,
  paranoid: true, // soft deletes
  indexes: [
    {
      unique: true,
      fields: ['subdomain'],
      where: {
        deletedAt: null,
      },
    },
    {
      fields: ['organizationId'],
    },
    {
      fields: ['universityId'],
    },
    {
      fields: ['campusId'],
    },
  ],
})
export class OrganizationSubdomain extends BaseModel {
  @AllowNull(false)
  @Length({ min: 2, max: 100 })
  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: 'Subdomain (e.g., "university1", "campus1")',
  })
  subdomain: string;

  @AllowNull(false)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Organization ID from organizations table',
  })
  organizationId: number;

  @AllowNull(true)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'University ID (nullable for non-university organizations)',
  })
  universityId?: number;

  @AllowNull(true)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: 'Campus ID (nullable for non-campus organizations)',
  })
  campusId?: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: 'Additional metadata or notes',
  })
  description?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this subdomain mapping is active',
  })
  isActive: boolean;
} 