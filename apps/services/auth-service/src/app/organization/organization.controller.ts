import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { OrganizationService } from './organization.service';
import { OrganizationSubdomainService } from './organization-subdomain.service';
import {
  CreateOrganizationRequest,
  CreateOrganizationResponse,
  GetOrganizationRequest,
  GetOrganizationResponse,
  UpdateOrganizationRequest,
  UpdateOrganizationResponse,
  DeleteOrganizationRequest,
  DeleteOrganizationResponse,
  ListOrganizationsRequest,
  ListOrganizationsResponse,
  GetOrganizationTreeRequest,
  GetOrganizationTreeResponse,
} from './organization.interface';
import {
  CreateOrganizationSubdomainRequest,
  CreateOrganizationSubdomainResponse,
  GetOrganizationSubdomainBySubdomainRequest,
  GetOrganizationSubdomainResponse,
  ListOrganizationSubdomainsRequest,
  ListOrganizationSubdomainsResponse,
  UpdateOrganizationSubdomainRequest,
  UpdateOrganizationSubdomainResponse,
  DeleteOrganizationSubdomainRequest,
  DeleteOrganizationSubdomainResponse,
} from './organization-subdomain.interface';

@Controller()
export class OrganizationController {
  private readonly logger = new Logger(OrganizationController.name);

  constructor(
    private readonly organizationService: OrganizationService,
    private readonly organizationSubdomainService: OrganizationSubdomainService,
  ) {}

  @GrpcMethod('AuthService', 'CreateOrganization')
  async createOrganization(
    request: CreateOrganizationRequest
  ): Promise<CreateOrganizationResponse> {
    try {
      this.logger.log(`gRPC CreateOrganization request: ${request.name}`);
      return await this.organizationService.createOrganization(request);
    } catch (error) {
      this.logger.error(
        `CreateOrganization error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to create organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'CreateCampusOrganization')
  async createCampusOrganization(
    request: any
  ): Promise<CreateOrganizationResponse> {
    try {
      this.logger.log(`gRPC CreateCampusOrganization request: ${request.name}`);
      return await this.organizationService.createCampusOrganization(request);
    } catch (error) {
      this.logger.error(
        `CreateCampusOrganization error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to create campus organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'GetOrganization')
  async getOrganization(
    request: GetOrganizationRequest
  ): Promise<GetOrganizationResponse> {
    try {
      this.logger.log(`gRPC GetOrganization request: ${request.id}`);
      return await this.organizationService.getOrganization(request);
    } catch (error) {
      this.logger.error(`GetOrganization error: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to get organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'UpdateOrganization')
  async updateOrganization(
    request: UpdateOrganizationRequest
  ): Promise<UpdateOrganizationResponse> {
    try {
      this.logger.log(`gRPC UpdateOrganization request: ${request.id}`);
      return await this.organizationService.updateOrganization(request);
    } catch (error) {
      this.logger.error(
        `UpdateOrganization error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to update organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'DeleteOrganization')
  async deleteOrganization(
    request: DeleteOrganizationRequest
  ): Promise<DeleteOrganizationResponse> {
    try {
      this.logger.log(`gRPC DeleteOrganization request: ${request.id}`);
      return await this.organizationService.deleteOrganization(request);
    } catch (error) {
      this.logger.error(
        `DeleteOrganization error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to delete organization',
      };
    }
  }

  @GrpcMethod('AuthService', 'ListOrganizations')
  async listOrganizations(
    request: ListOrganizationsRequest
  ): Promise<ListOrganizationsResponse> {
    try {
      this.logger.log('gRPC ListOrganizations request');
      return await this.organizationService.listOrganizations(request);
    } catch (error) {
      this.logger.error(
        `ListOrganizations error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to list organizations',
        organizations: [],
        total: 0,
        page: request.page || 1,
        limit: request.limit || 10,
      };
    }
  }

  @GrpcMethod('AuthService', 'GetOrganizationTree')
  async getOrganizationTree(
    request: GetOrganizationTreeRequest
  ): Promise<GetOrganizationTreeResponse> {
    try {
      this.logger.log(`gRPC GetOrganizationTree request: ${request.organizationId}`);
      return await this.organizationService.getOrganizationTree(request);
    } catch (error) {
      this.logger.error(
        `GetOrganizationTree error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to get organization tree',
      };
    }
  }

  // Organization Subdomain Methods
  @GrpcMethod('AuthService', 'CreateOrganizationSubdomain')
  async createOrganizationSubdomain(
    request: CreateOrganizationSubdomainRequest
  ): Promise<CreateOrganizationSubdomainResponse> {
    try {
      this.logger.log(`gRPC CreateOrganizationSubdomain request: ${request.subdomain}`);
      return await this.organizationSubdomainService.createSubdomain(request);
    } catch (error) {
      this.logger.error(
        `CreateOrganizationSubdomain error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to create organization subdomain',
      };
    }
  }

  @GrpcMethod('AuthService', 'GetOrganizationSubdomainBySubdomain')
  async getOrganizationSubdomainBySubdomain(
    request: GetOrganizationSubdomainBySubdomainRequest
  ): Promise<GetOrganizationSubdomainResponse> {
    try {
      this.logger.log(`gRPC GetOrganizationSubdomainBySubdomain request: ${request.subdomain}`);
      return await this.organizationSubdomainService.getSubdomainBySubdomain(request);
    } catch (error) {
      this.logger.error(
        `GetOrganizationSubdomainBySubdomain error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to get organization subdomain',
      };
    }
  }

  @GrpcMethod('AuthService', 'ListOrganizationSubdomains')
  async listOrganizationSubdomains(
    request: ListOrganizationSubdomainsRequest
  ): Promise<ListOrganizationSubdomainsResponse> {
    try {
      this.logger.log('gRPC ListOrganizationSubdomains request');
      return await this.organizationSubdomainService.listSubdomains(request);
    } catch (error) {
      this.logger.error(
        `ListOrganizationSubdomains error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to list organization subdomains',
        data: [],
        total: 0,
        page: request.page || 1,
        limit: request.limit || 10,
      };
    }
  }

  @GrpcMethod('AuthService', 'UpdateOrganizationSubdomain')
  async updateOrganizationSubdomain(
    request: UpdateOrganizationSubdomainRequest
  ): Promise<UpdateOrganizationSubdomainResponse> {
    try {
      this.logger.log(`gRPC UpdateOrganizationSubdomain request: ${request.id}`);
      return await this.organizationSubdomainService.updateSubdomain(request);
    } catch (error) {
      this.logger.error(
        `UpdateOrganizationSubdomain error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to update organization subdomain',
      };
    }
  }

  @GrpcMethod('AuthService', 'DeleteOrganizationSubdomain')
  async deleteOrganizationSubdomain(
    request: DeleteOrganizationSubdomainRequest
  ): Promise<DeleteOrganizationSubdomainResponse> {
    try {
      this.logger.log(`gRPC DeleteOrganizationSubdomain request: ${request.id}`);
      return await this.organizationSubdomainService.deleteSubdomain(request);
    } catch (error) {
      this.logger.error(
        `DeleteOrganizationSubdomain error: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to delete organization subdomain',
      };
    }
  }
}
