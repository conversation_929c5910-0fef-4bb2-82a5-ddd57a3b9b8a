// Organization DTOs and Interfaces

export interface CreateOrganizationRequest {
  name: string;
  type: string;
  imageUrl?: string;
  description?: string;
  website?: string;
  address?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  userId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface CreateOrganizationResponse {
  success: boolean;
  message: string;
  organization?: OrganizationInfo;
}

export interface GetOrganizationRequest {
  id: bigint;
  userId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface GetOrganizationResponse {
  success: boolean;
  message: string;
  organization?: OrganizationInfo;
}

export interface UpdateOrganizationRequest {
  id: bigint;
  name?: string;
  type?: string;
  imageUrl?: string;
  description?: string;
  website?: string;
  address?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  userId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface UpdateOrganizationResponse {
  success: boolean;
  message: string;
  organization?: OrganizationInfo;
}

export interface DeleteOrganizationRequest {
  id: bigint;
  userId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface DeleteOrganizationResponse {
  success: boolean;
  message: string;
}

export interface ListOrganizationsRequest {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  userId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface ListOrganizationsResponse {
  success: boolean;
  message: string;
  organizations: OrganizationInfo[];
  total: number;
  page: number;
  limit: number;
}

export interface OrganizationInfo {
  id: bigint;
  name: string;
  type: string;
  imageUrl?: string;
  description?: string;
  website?: string;
  address?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Internal service interfaces
export interface CreateOrganizationData {
  name: string;
  type: string;
  imageUrl?: string;
  description?: string;
  website?: string;
  address?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  campusId?: bigint;
}

export interface UpdateOrganizationData {
  name?: string;
  type?: string;
  imageUrl?: string;
  description?: string;
  website?: string;
  address?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
}

export interface OrganizationFilters {
  search?: string;
  type?: string;
  country?: string;
  isActive?: boolean;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  offset: number;
}

export const ORGANIZATION_TYPES = ['university', 'agency', 'company', 'campus'] as const;

export type OrganizationType = (typeof ORGANIZATION_TYPES)[number];

export interface GetOrganizationTreeRequest {
  organizationId: bigint;
  userId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
  includeEmployees?: boolean;
}

export interface GetOrganizationTreeResponse {
  success: boolean;
  message: string;
  organizationTree?: OrganizationTreeInfo;
}

export interface OrganizationTreeInfo {
  organization: OrganizationInfo;
  departments: DepartmentTreeInfo[];
  totalEmployees: number;
  totalDepartments: number;
}

export interface DepartmentTreeInfo {
  id: number;
  name: string;
  parentId?: number;
  description?: string;
  children?: DepartmentTreeInfo[];
  employees?: EmployeeTreeInfo[];
  employeeCount: number;
}

export interface EmployeeTreeInfo {
  id: number;
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  designation?: string;
  employeeId?: string;
  department?: string;
  joiningDate?: string;
  jobStatus?: string;
  jobType?: string;
  imageUrl?: string;
}
