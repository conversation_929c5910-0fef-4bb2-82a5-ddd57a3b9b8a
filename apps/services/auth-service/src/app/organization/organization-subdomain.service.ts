import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { OrganizationSubdomain } from './organization-subdomain.model';
import {
  CreateOrganizationSubdomainRequest,
  UpdateOrganizationSubdomainRequest,
  GetOrganizationSubdomainBySubdomainRequest,
  GetOrganizationSubdomainByIdRequest,
  ListOrganizationSubdomainsRequest,
  DeleteOrganizationSubdomainRequest,
  OrganizationSubdomainInfo,
  CreateOrganizationSubdomainResponse,
  GetOrganizationSubdomainResponse,
  ListOrganizationSubdomainsResponse,
  UpdateOrganizationSubdomainResponse,
  DeleteOrganizationSubdomainResponse,
} from './organization-subdomain.interface';

@Injectable()
export class OrganizationSubdomainService {
  private readonly logger = new Logger(OrganizationSubdomainService.name);

  constructor(
    @InjectModel(OrganizationSubdomain)
    private organizationSubdomainModel: typeof OrganizationSubdomain,
  ) {}

  async createSubdomain(
    request: CreateOrganizationSubdomainRequest,
  ): Promise<CreateOrganizationSubdomainResponse> {
    try {
      this.logger.log(`Creating organization subdomain: ${request.subdomain}`);

      // Check if subdomain already exists
      const existingSubdomain = await this.organizationSubdomainModel.findOne({
        where: { subdomain: request.subdomain },
      });

      if (existingSubdomain) {
        return {
          success: false,
          message: `Subdomain '${request.subdomain}' already exists`,
        };
      }

      const subdomain = await this.organizationSubdomainModel.create({
        subdomain: request.subdomain,
        organizationId: request.organizationId,
        universityId: request.universityId,
        campusId: request.campusId,
        description: request.description,
        isActive: request.isActive ?? true,
      });

      this.logger.log(`Created organization subdomain: ${subdomain.id}`);

      return {
        success: true,
        message: 'Organization subdomain created successfully',
        data: this.mapToInfo(subdomain),
      };
    } catch (error) {
      this.logger.error('Error creating organization subdomain:', error);
      return {
        success: false,
        message: 'Failed to create organization subdomain',
      };
    }
  }

  async getSubdomainBySubdomain(
    request: GetOrganizationSubdomainBySubdomainRequest,
  ): Promise<GetOrganizationSubdomainResponse> {
    try {
      this.logger.log(`Getting organization subdomain by subdomain: ${request.subdomain}`);

            const subdomain = await this.organizationSubdomainModel.findOne({
        where: {
          subdomain: request.subdomain,
          isActive: true,
        },
        raw: false, // Ensure we get the full model instance
      });

      if (!subdomain) {
        return {
          success: false,
          message: `Organization subdomain '${request.subdomain}' not found`,
        };
      }

      return {
        success: true,
        message: 'Organization subdomain retrieved successfully',
        data: this.mapToInfo(subdomain),
      };
    } catch (error) {
      this.logger.error('Error getting organization subdomain by subdomain:', error);
      return {
        success: false,
        message: 'Failed to get organization subdomain',
      };
    }
  }

  async getSubdomainById(
    request: GetOrganizationSubdomainByIdRequest,
  ): Promise<GetOrganizationSubdomainResponse> {
    try {
      this.logger.log(`Getting organization subdomain by ID: ${request.id}`);

      const subdomain = await this.organizationSubdomainModel.findByPk(request.id);

      if (!subdomain) {
        return {
          success: false,
          message: `Organization subdomain with ID ${request.id} not found`,
        };
      }

      return {
        success: true,
        message: 'Organization subdomain retrieved successfully',
        data: this.mapToInfo(subdomain),
      };
    } catch (error) {
      this.logger.error('Error getting organization subdomain by ID:', error);
      return {
        success: false,
        message: 'Failed to get organization subdomain',
      };
    }
  }

  async listSubdomains(
    request: ListOrganizationSubdomainsRequest,
  ): Promise<ListOrganizationSubdomainsResponse> {
    try {
      this.logger.log('Listing organization subdomains');

      const page = request.page || 1;
      const limit = request.limit || 10;
      const offset = (page - 1) * limit;

      const whereClause: any = {};

      if (request.search) {
        whereClause.subdomain = {
          [Op.iLike]: `%${request.search}%`,
        };
      }

      if (request.organizationId) {
        whereClause.organizationId = request.organizationId;
      }

      if (request.universityId) {
        whereClause.universityId = request.universityId;
      }

      if (request.campusId) {
        whereClause.campusId = request.campusId;
      }

      // Only filter by isActive if explicitly provided, otherwise show all
      if (request.isActive !== undefined) {
        whereClause.isActive = request.isActive;
      }

      const { count, rows } = await this.organizationSubdomainModel.findAndCountAll({
        where: whereClause,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      const subdomains = rows.map((subdomain) => this.mapToInfo(subdomain));

      return {
        success: true,
        message: 'Organization subdomains retrieved successfully',
        data: subdomains,
        total: count,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error('Error listing organization subdomains:', error);
      return {
        success: false,
        message: 'Failed to list organization subdomains',
        data: [],
        total: 0,
        page: request.page || 1,
        limit: request.limit || 10,
      };
    }
  }

  async updateSubdomain(
    request: UpdateOrganizationSubdomainRequest,
  ): Promise<UpdateOrganizationSubdomainResponse> {
    try {
      this.logger.log(`Updating organization subdomain: ${request.id}`);

      const subdomain = await this.organizationSubdomainModel.findByPk(request.id);

      if (!subdomain) {
        return {
          success: false,
          message: `Organization subdomain with ID ${request.id} not found`,
        };
      }

      // Check if new subdomain already exists (if subdomain is being updated)
      if (request.subdomain && request.subdomain !== subdomain.subdomain) {
        const existingSubdomain = await this.organizationSubdomainModel.findOne({
          where: { subdomain: request.subdomain },
        });

        if (existingSubdomain) {
          return {
            success: false,
            message: `Subdomain '${request.subdomain}' already exists`,
          };
        }
      }

      const updateData: any = {};
      if (request.subdomain !== undefined) updateData.subdomain = request.subdomain;
      if (request.organizationId !== undefined) updateData.organizationId = request.organizationId;
      if (request.universityId !== undefined) updateData.universityId = request.universityId;
      if (request.campusId !== undefined) updateData.campusId = request.campusId;
      if (request.description !== undefined) updateData.description = request.description;
      if (request.isActive !== undefined) updateData.isActive = request.isActive;

      // Only update if there are actual changes
      if (Object.keys(updateData).length === 0) {
        return {
          success: true,
          message: 'No changes to update',
          data: this.mapToInfo(subdomain),
        };
      }

      await subdomain.update(updateData, { validate: false });

      this.logger.log(`Updated organization subdomain: ${subdomain.id}`);

      return {
        success: true,
        message: 'Organization subdomain updated successfully',
        data: this.mapToInfo(subdomain),
      };
    } catch (error) {
      this.logger.error('Error updating organization subdomain:', error);
      return {
        success: false,
        message: 'Failed to update organization subdomain',
      };
    }
  }

  async deleteSubdomain(
    request: DeleteOrganizationSubdomainRequest,
  ): Promise<DeleteOrganizationSubdomainResponse> {
    try {
      this.logger.log(`Deleting organization subdomain: ${request.id}`);

      const subdomain = await this.organizationSubdomainModel.findByPk(request.id);

      if (!subdomain) {
        return {
          success: false,
          message: `Organization subdomain with ID ${request.id} not found`,
        };
      }

      await subdomain.destroy();

      this.logger.log(`Deleted organization subdomain: ${request.id}`);

      return {
        success: true,
        message: 'Organization subdomain deleted successfully',
      };
    } catch (error) {
      this.logger.error('Error deleting organization subdomain:', error);
      return {
        success: false,
        message: 'Failed to delete organization subdomain',
      };
    }
  }

  private mapToInfo(subdomain: OrganizationSubdomain): OrganizationSubdomainInfo {
    return {
      id: Number(subdomain.id),
      subdomain: subdomain.subdomain,
      organizationId: subdomain.organizationId,
      universityId: subdomain.universityId || undefined,
      campusId: subdomain.campusId || undefined,
      description: subdomain.description,
      isActive: subdomain.isActive,
      createdAt: subdomain.createdAt.toISOString(),
      updatedAt: subdomain.updatedAt.toISOString(),
    };
  }
} 