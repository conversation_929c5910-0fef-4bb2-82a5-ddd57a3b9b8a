export interface CreateOrganizationSubdomainRequest {
  subdomain: string;
  organizationId: number;
  universityId?: number;
  campusId?: number;
  description?: string;
  isActive?: boolean;
}

export interface UpdateOrganizationSubdomainRequest {
  id: number;
  subdomain?: string;
  organizationId?: number;
  universityId?: number;
  campusId?: number;
  description?: string;
  isActive?: boolean;
}

export interface GetOrganizationSubdomainBySubdomainRequest {
  subdomain: string;
}

export interface GetOrganizationSubdomainByIdRequest {
  id: number;
}

export interface ListOrganizationSubdomainsRequest {
  page?: number;
  limit?: number;
  search?: string;
  organizationId?: number;
  universityId?: number;
  campusId?: number;
  isActive?: boolean;
}

export interface DeleteOrganizationSubdomainRequest {
  id: number;
}

export interface OrganizationSubdomainInfo {
  id: number;
  subdomain: string;
  organizationId: number;
  universityId?: number;
  campusId?: number;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateOrganizationSubdomainResponse {
  success: boolean;
  message: string;
  data?: OrganizationSubdomainInfo;
}

export interface GetOrganizationSubdomainResponse {
  success: boolean;
  message: string;
  data?: OrganizationSubdomainInfo;
}

export interface ListOrganizationSubdomainsResponse {
  success: boolean;
  message: string;
  data: OrganizationSubdomainInfo[];
  total: number;
  page: number;
  limit: number;
}

export interface UpdateOrganizationSubdomainResponse {
  success: boolean;
  message: string;
  data?: OrganizationSubdomainInfo;
}

export interface DeleteOrganizationSubdomainResponse {
  success: boolean;
  message: string;
} 