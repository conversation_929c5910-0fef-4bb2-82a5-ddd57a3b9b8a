import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, UniqueConstraintError } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { Organization } from './organization.model';
import { Department } from '../user/model/department.model';
import { EmployeePersonal } from '../employee/models/employee-personal.model';
import { EmployeeDepartment } from '../employee/models/employee-department.model';
import { User } from '../user/model/user.model';
import { AuditClientService } from '../audit/audit.service';
import { AppService } from '../app.service';
import {
  CreateOrganizationRequest,
  CreateOrganizationResponse,
  GetOrganizationRequest,
  GetOrganizationResponse,
  UpdateOrganizationRequest,
  UpdateOrganizationResponse,
  DeleteOrganizationRequest,
  DeleteOrganizationResponse,
  ListOrganizationsRequest,
  ListOrganizationsResponse,
  GetOrganizationTreeRequest,
  GetOrganizationTreeResponse,
  OrganizationInfo,
  CreateOrganizationData,
  UpdateOrganizationData,
  OrganizationTreeInfo,
  DepartmentTreeInfo,
  EmployeeTreeInfo,
  ORGANIZATION_TYPES,
} from './organization.interface';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name);

  constructor(
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(EmployeePersonal)
    private readonly employeePersonalModel: typeof EmployeePersonal,
    @InjectModel(EmployeeDepartment)
    private readonly employeeDepartmentModel: typeof EmployeeDepartment,
    @InjectModel(User)
    private readonly userModel: typeof User,
    private readonly auditClient: AuditClientService,
    private readonly appService: AppService
  ) {}

  // —1) exactly as in AuthService
  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditClient.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  // —2) same metrics helper
  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      if (operation === 'token')
        this.appService.trackTokenOperation('generate', duration);
      else if (operation === 'login')
        this.appService.trackLoginAttempt(
          status,
          status === 'success' ? 'valid_credentials' : 'invalid_credentials'
        );
      else if (operation === 'auth')
        this.appService.trackAuthorization('api', 'access', status);

      // custom ops for org can still use generic "auth" or omit
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }

  async createOrganization(
    request: CreateOrganizationRequest
  ): Promise<CreateOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Creating organization: ${request.name}`);

      if (!ORGANIZATION_TYPES.includes(request.type as any)) {
        throw new BadRequestException(
          `Invalid organization type. Must be one of: ${ORGANIZATION_TYPES.join(
            ', '
          )}`
        );
      }

      const data: CreateOrganizationData = {
        name: request.name,
        type: request.type,
        imageUrl: request.imageUrl,
        description: request.description,
        website: request.website,
        address: request.address,
        country: request.country,
        state: request.state,
        city: request.city,
        postalCode: request.postalCode,
        phone: request.phone,
        email: request.email,
      };

      const organization = await this.organizationModel.create(data as any);

      // audit + metrics
      await this.createAuditLog({
        userId: Number(request.userId),
        orgId: organization.id,
        userRole: request.roleName,
        actions: 'CREATE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(organization.id),
        description: `Created organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'create_organization',
        'success',
        Date.now() - startTime
      );

      return {
        success: true,
        message: 'Organization created successfully',
        organization: this.mapToOrganizationInfo(organization),
      };
    } catch (error) {
      this.logger.error(
        `Error creating organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('create_organization', 'error', Date.now() - startTime);

      if (error instanceof UniqueConstraintError) {
        throw new ConflictException(
          'Organization with this name already exists'
        );
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error('Failed to create organization');
    }
  }

  async getOrganization(
    request: GetOrganizationRequest
  ): Promise<GetOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Getting organization: ${request.id}`);
      const org = await this.organizationModel.findByPk(request.id);
      if (!org) throw new NotFoundException('Organization not found');

      await this.createAuditLog({
        userId: Number(request.userId),
        orgId: org.id,
        userRole: request.roleName,
        actions: 'VIEW_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(org.id),
        description: `Viewed organization: ${org.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics('get_organization', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Organization retrieved successfully',
        organization: this.mapToOrganizationInfo(org),
      };
    } catch (error) {
      this.logger.error(
        `Error getting organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('get_organization', 'error', Date.now() - startTime);
      if (error instanceof NotFoundException) throw error;
      throw new Error('Failed to get organization');
    }
  }

  async updateOrganization(
    request: UpdateOrganizationRequest
  ): Promise<UpdateOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Updating organization: ${request.id}`);

      const org = await this.organizationModel.findByPk(request.id);
      if (!org) throw new NotFoundException('Organization not found');

      if (request.type && !ORGANIZATION_TYPES.includes(request.type as any)) {
        throw new BadRequestException(
          `Invalid organization type. Must be one of: ${ORGANIZATION_TYPES.join(
            ', '
          )}`
        );
      }

      const updateData: UpdateOrganizationData = {};
      Object.assign(updateData, request);

      await org.update(updateData);

      await this.createAuditLog({
        userId: Number(request.userId),
        orgId: org.id,
        userRole: request.roleName,
        actions: 'UPDATE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(org.id),
        description: `Updated organization: ${org.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'update_organization',
        'success',
        Date.now() - startTime
      );

      return {
        success: true,
        message: 'Organization updated successfully',
        organization: this.mapToOrganizationInfo(org),
      };
    } catch (error) {
      this.logger.error(
        `Error updating organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('update_organization', 'error', Date.now() - startTime);

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      if (error instanceof UniqueConstraintError) {
        throw new ConflictException(
          'Organization with this name already exists'
        );
      }
      throw new Error('Failed to update organization');
    }
  }

  async deleteOrganization(
    request: DeleteOrganizationRequest
  ): Promise<DeleteOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Deleting organization: ${request.id}`);
      const org = await this.organizationModel.findByPk(request.id);
      if (!org) throw new NotFoundException('Organization not found');

      await org.destroy();

      await this.createAuditLog({
        userId: Number(request.userId),
        orgId: org.id,
        userRole: request.roleName,
        actions: 'DELETE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(org.id),
        description: `Deleted organization: ${org.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'delete_organization',
        'success',
        Date.now() - startTime
      );

      return { success: true, message: 'Organization deleted successfully' };
    } catch (error) {
      this.logger.error(
        `Error deleting organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('delete_organization', 'error', Date.now() - startTime);
      if (error instanceof NotFoundException) throw error;
      throw new Error('Failed to delete organization');
    }
  }

  async listOrganizations(
    request: ListOrganizationsRequest
  ): Promise<ListOrganizationsResponse> {
    const startTime = Date.now();
    try {
      this.logger.log('Listing organizations');

      const page = request.page || 1;
      const limit = request.limit || 10;
      const offset = (page - 1) * limit;

      const whereClause: any = {};

      // Add search functionality
      if (request.search) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${request.search}%` } },
          { description: { [Op.iLike]: `%${request.search}%` } },
          { email: { [Op.iLike]: `%${request.search}%` } },
        ];
      }

      // Add type filter
      if (request.type) {
        whereClause.type = request.type;
      }

      const { rows: organizations, count: total } =
        await this.organizationModel.findAndCountAll({
          where: whereClause,
          limit,
          offset,
          order: [['name', 'ASC']],
        });

      const organizationInfos = organizations.map((org) =>
        this.mapToOrganizationInfo(org)
      );

      // audit + metrics
      await this.createAuditLog({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'LIST_ORGANIZATIONS',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: 0, // List operation doesn't target specific org
        description: `Listed ${organizations.length} organizations`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.trackMetrics('list_organizations', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Organizations retrieved successfully',
        organizations: organizationInfos,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Error listing organizations: ${error.message}`,
        error.stack
      );
      this.trackMetrics('list_organizations', 'error', Date.now() - startTime);

      return {
        success: false,
        message: error.message || 'Failed to list organizations',
        organizations: [],
        total: 0,
        page: request.page || 1,
        limit: request.limit || 10,
      };
    }
  }

  async getOrganizationTree(
    request: GetOrganizationTreeRequest
  ): Promise<GetOrganizationTreeResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Getting organization tree for organization ID: ${request.organizationId}`);

      // 1. Get organization details
      const organization = await this.organizationModel.findByPk(
        Number(request.organizationId)
      );

      if (!organization) {
        return {
          success: false,
          message: 'Organization not found',
        };
      }

      // 2. Get all departments for this organization with hierarchy
      const allDepartments = await this.departmentModel.findAll({
        where: { organizationId: Number(request.organizationId) },
        order: [['name', 'ASC']],
      });

      // 3. Build department hierarchy
      const departmentMap = new Map();
      allDepartments.forEach(dept => {
        departmentMap.set(Number(dept.id), {
          id: Number(dept.id),
          name: dept.name,
          parentId: dept.parentId ? Number(dept.parentId) : null,
          description: undefined, // Department model doesn't have description field
          children: [],
          employees: [],
          employeeCount: 0
        });
      });

      // Build the hierarchy - process parents first, then children
      const rootDepartments = [];
      const childDepartments = [];
      
      // First pass: separate parents and children
      departmentMap.forEach((dept, id) => {
        if (dept.parentId) {
          childDepartments.push(dept);
        } else {
          rootDepartments.push(dept);
        }
      });

      // Second pass: add children to their parents
      childDepartments.forEach(dept => {
        const parent = departmentMap.get(dept.parentId);
        if (parent) {
          parent.children.push(dept);
        }
      });

      // 4. Get employees for each department if requested
      if (request.includeEmployees !== false) {
        for (const dept of departmentMap.values()) {
          const employees = await this.employeeDepartmentModel.findAll({
            where: { departmentId: dept.id },
            include: [
              {
                model: this.employeePersonalModel,
                as: 'employee',
              }
            ]
          });

          dept.employees = employees.map(empDept => ({
            id: Number(empDept.id),
            userId: Number(empDept.userId),
            firstName: empDept.employee?.firstName || '',
            lastName: empDept.employee?.lastName || '',
            email: '', // We'll get this from User model separately if needed
            phone: empDept.employee?.phone,
            designation: empDept.designation,
            employeeId: empDept.employeeId,
            department: empDept.department,
            joiningDate: empDept.employee?.joiningDate?.toISOString(),
            jobStatus: empDept.employee?.jobStatus,
            jobType: empDept.employee?.jobType,
            imageUrl: empDept.employee?.imageUrl,
          }));

          dept.employeeCount = dept.employees.length;
        }
      }

      // 5. Calculate totals
      const totalEmployees = Array.from(departmentMap.values())
        .reduce((total, dept) => total + dept.employeeCount, 0);
      const totalDepartments = allDepartments.length;

      // 6. Audit log
      await this.createAuditLog({
        userId: Number(request.userId),
        orgId: BigInt(request.organizationId),
        userRole: request.roleName,
        actions: 'GET_ORGANIZATION_TREE',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(request.organizationId),
        description: `Retrieved organization tree for organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.trackMetrics('get_organization_tree', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Organization tree retrieved successfully',
        organizationTree: {
          organization: this.mapToOrganizationInfo(organization),
          departments: rootDepartments,
          totalEmployees,
          totalDepartments,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting organization tree: ${error.message}`,
        error.stack
      );
      this.trackMetrics('get_organization_tree', 'error', Date.now() - startTime);

      return {
        success: false,
        message: error.message || 'Failed to get organization tree',
      };
    }
  }

  // Find organization by campus ID
  async findOrganizationByCampusId(campusId: bigint): Promise<Organization | null> {
    try {
      this.logger.log(`Finding organization by campusId: ${campusId}`);
      
      const organization = await this.organizationModel.findOne({
        where: { campusId: campusId }
      });

      if (organization) {
        this.logger.log(`Found organization: ${organization.name} (ID: ${organization.id}) for campusId: ${campusId}`);
      } else {
        this.logger.log(`No organization found for campusId: ${campusId}`);
      }

      return organization;
    } catch (error) {
      this.logger.error(`Error finding organization by campusId ${campusId}: ${error.message}`, error.stack);
      return null;
    }
  }

  // Campus Organization Methods
  async createCampusOrganization(
    request: any
  ): Promise<CreateOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Creating campus organization: ${request.name}`);

      const data: CreateOrganizationData = {
        name: request.name,
        type: 'campus',
        imageUrl: null,
        description: request.description || `Campus organization for ${request.name}`,
        website: null,
        address: request.address,
        country: request.country,
        state: request.state,
        city: request.city,
        postalCode: request.postalCode,
        phone: request.phone,
        email: request.email,
        campusId: request.campusId || null,  // NEW: Store campus ID
      };

      const organization = await this.organizationModel.create(data as any);

      // audit + metrics
      await this.createAuditLog({
        userId: Number(request.userId),
        orgId: organization.id,
        userRole: request.roleName,
        actions: 'CREATE_CAMPUS_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(organization.id),
        description: `Created campus organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'create_campus_organization',
        'success',
        Date.now() - startTime
      );

      return {
        success: true,
        message: 'Campus organization created successfully',
        organization: this.mapToOrganizationInfo(organization),
      };
    } catch (error) {
      this.logger.error(
        `Error creating campus organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('create_campus_organization', 'error', Date.now() - startTime);

      if (error instanceof UniqueConstraintError) {
        throw new ConflictException(
          'Campus organization with this name already exists'
        );
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error('Failed to create campus organization');
    }
  }

  private mapToOrganizationInfo(organization: Organization): OrganizationInfo {
    return {
      id: organization.id,
      name: organization.name,
      type: organization.type,
      imageUrl: organization.imageUrl,
      description: organization.description,
      website: organization.website,
      address: organization.address,
      country: organization.country,
      state: organization.state,
      city: organization.city,
      postalCode: organization.postalCode,
      phone: organization.phone,
      email: organization.email,
      isActive: organization.isActive,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,
    };
  }
}
