import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Organization } from './organization.model';
import { OrganizationSubdomain } from './organization-subdomain.model';
import { OrganizationController } from './organization.controller';
import { OrganizationService } from './organization.service';
import { OrganizationSubdomainService } from './organization-subdomain.service';
import { AuditModule } from '../audit/audit.module';
import { DatabaseModule } from '../migration/database.module';
import { AppService } from '../app.service';
import { MetricsModule } from '../metrics/metrics.module';

@Module({
  imports: [
    DatabaseModule,
    SequelizeModule.forFeature([Organization, OrganizationSubdomain]),
    AuditModule,
    MetricsModule,
  ],
  controllers: [OrganizationController],
  providers: [OrganizationService, OrganizationSubdomainService, AppService],
  exports: [SequelizeModule, OrganizationService, OrganizationSubdomainService],
})
export class OrganizationModule {}
