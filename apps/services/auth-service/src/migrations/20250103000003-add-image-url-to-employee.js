'use strict';

module.exports = {
    async up(queryInterface, Sequelize) {
        console.log('Running migration: add-image-url-to-employee');

        // Add imageUrl column to employee_personal table
        await queryInterface.addColumn('employee_personal', 'imageUrl', {
            type: Sequelize.TEXT,
            allowNull: true,
            after: 'maritalStatus'
        });

        console.log('Migration completed: add-image-url-to-employee');
    },

    async down(queryInterface, Sequelize) {
        console.log('Rolling back migration: add-image-url-to-employee');

        // Remove imageUrl column from employee_personal table
        await queryInterface.removeColumn('employee_personal', 'imageUrl');

        console.log('Rollback completed: add-image-url-to-employee');
    }
}; 