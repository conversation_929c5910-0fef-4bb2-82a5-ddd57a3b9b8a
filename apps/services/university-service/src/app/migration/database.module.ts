import { <PERSON><PERSON>le, OnModuleInit } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { getDatabaseConfig } from '@apply-goal-backend/database';
import { Sequelize } from 'sequelize-typescript';

// Import all models
import { University } from '../../models/university.model';
import { Campus } from '../../models/campus.model';
import { Course } from '../../models/course.model';
import { Country } from '../../models/country.model';
import { StudyField } from '../../models/study-field.model';
import { TuitionFinancialAid } from '../../models/tuition-financial-aid.model';
import { UniversityAlumni } from '../../models/university-alumni.model';
import { UniversityBank } from '../../models/university-bank.model';
import { UniversityCourseCommission } from '../../models/university-course-commission.model';
import { CourseTestRequirement } from '../../models/course-test-requirement.model';
import { EligibleCountry } from '../../models/eligible-country.model';
import { File } from '../../models/file.model';
import { Language } from '../../models/language.model';
import { SemesterTimeframe } from '../../models/semester-timeframe.model';
import { CourseIntake } from '../../models/course-intake.model';
import { CourseLectureLanguage } from '../../models/course-lecture-language.model';
import { CourseStudyField } from '../../models/course-study-field.model';
import { CourseSubject } from '../../models/course-subject.model';
import { ApplicationStep } from '../../models/application-step.model';
import { CampusCourse } from '../../models/campus-course.model';

const models = [
  University,
  Campus,
  Course,
  Country,
  StudyField,
  TuitionFinancialAid,
  UniversityAlumni,
  UniversityBank,
  UniversityCourseCommission,
  CourseTestRequirement,
  EligibleCountry,
  File,
  Language,
  SemesterTimeframe,
  CourseIntake,
  CourseLectureLanguage,
  CourseStudyField,
  CourseSubject,
  ApplicationStep,
  CampusCourse,
];

@Module({
  imports: [
    SequelizeModule.forRoot({
      ...getDatabaseConfig('university'),
      models,
      autoLoadModels: true,
      synchronize: false, // We're using migrations, so set this to false
    }),
    SequelizeModule.forFeature(models),
  ],
  exports: [SequelizeModule],
})
export class DatabaseModule implements OnModuleInit {
  constructor(private readonly sequelize: Sequelize) {}
  
  onModuleInit() {
    // Initialize any model scopes if needed
  }
} 