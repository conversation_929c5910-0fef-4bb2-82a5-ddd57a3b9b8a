import { Controller, Get, Post, HttpCode, HttpStatus } from '@nestjs/common';
import { MigrationService } from './migration.service';

@Controller('migrations')
export class MigrationController {
  constructor(private readonly migrationService: MigrationService) {}

  @Get('status')
  async getMigrationStatus() {
    return await this.migrationService.getMigrationStatus();
  }

  @Post('run')
  @HttpCode(HttpStatus.OK)
  async runMigrations() {
    const executed = await this.migrationService.migrate();
    return {
      message: 'Migrations completed successfully',
      executed: executed.map(m => m.name),
    };
  }
} 