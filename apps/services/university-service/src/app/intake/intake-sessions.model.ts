import {
  Table,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
  BelongsToMany,
  AllowNull,
  <PERSON><PERSON><PERSON>,
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { University } from '../university/university.model';
import { IntakeStudentType } from './intake-student-type.model';
import { ProgramLevel } from '../program-level/programLevel.model';
import { ProgramLevelIntake } from '../program-level/programLevelIntake.model';
import { DataTypes } from 'sequelize';
import { Intake } from './intake.model';
import { Course } from '../course/course.model';
import { CourseIntakeSessions } from '../course/courseIntakeSessions.model';

@Table({ tableName: 'intakes_sessions' })
export class IntakeSession extends BaseModel {
  @ForeignKey(() => Intake)
  @Column(DataType.BIGINT)
  intakeId!: number;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataTypes.BOOLEAN)
  @Default(false)
  isActive!: boolean;

  @Column(DataType.STRING)
  activeYear!: string

  @BelongsTo(() => Intake)
  intake!: Intake

  @BelongsToMany(() => Course, {
    through: () => CourseIntakeSessions,
    as: 'courses',
    foreignKey: 'intakeSessionId',
    otherKey: 'courseId',
  })
  courses!: Course[]
}
