import { Table, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { Course } from './course.model';
import { ProgramLevelTestScore } from '../program-level/applicationTestScore.model';
import { IntakeSession } from '../intake/intake-sessions.model';

@Table({ tableName: 'course_intake_sessions' })
export class CourseIntakeSessions extends BaseModel {
  @ForeignKey(() => Course)
  @Column(DataType.BIGINT)
  courseId!: number;

  @BelongsTo(() => Course)
  course!: Course;

  @ForeignKey(() => IntakeSession)
  @Column(DataType.BIGINT)
  intakeSessionId!: number;

  @BelongsTo(() => IntakeSession)
  session!: IntakeSession;
}
