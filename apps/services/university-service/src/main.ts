/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { MetricsService, TracingService } from '@apply-goal-backend/monitoring';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { join } from 'path';
import * as express from 'express';
import { healthRouter } from './routes/health.routes';
import { GrpcExceptionFilter } from './app/filters/grpc-exception.filter';
import { SeederService } from './seeders/seeder.service';
import { MigrationService } from './app/migration/migration.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Add express middleware for health routes
  app.use(express.json());
  app.use('/health', healthRouter);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // Run database migrations on startup
  try {
    const migrationService = app.get(MigrationService);
    await migrationService.migrate();
    Logger.log('Database migrations completed successfully');
  } catch (error) {
    Logger.error('Failed to run migrations on startup', error);
    // Don't exit - allow the app to start even if migrations fail
  }

  // Setup gRPC Microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: ['university'],
      protoPath: [
        join(process.cwd(), 'libs/shared/dto/src/lib/university/university.proto'),
      ],
      url: '0.0.0.0:50059',
    },
  });

  // Set global prefix and validation pipe
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  app.useGlobalPipes(new ValidationPipe());
  
  // Enable CORS
  app.enableCors();

  const port = process.env.PORT || 5020;

  // Start all microservices first, then the HTTP server
  await app.startAllMicroservices();
  await app.listen(port);

  // Run seeders after application starts
  try {
    const seederService = app.get(SeederService);
    await seederService.runAllSeeders();
  } catch (error) {
    Logger.error('Failed to run seeders:', error);
    // Don't stop the application if seeding fails
  }

  Logger.log(`🚀 HTTP Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`📊 Metrics server is running on: http://localhost:${port}/metrics`);
  Logger.log(`🔗 gRPC server is running on: 0.0.0.0:50059`);
}

bootstrap();
