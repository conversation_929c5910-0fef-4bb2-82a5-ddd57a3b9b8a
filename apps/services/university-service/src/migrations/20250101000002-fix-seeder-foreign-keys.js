'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // This migration ensures that the seeder foreign key constraints are properly handled
    // by checking if the referenced tables exist before seeding
    
    // Check if program_levels table exists and has data
    const programLevelsExist = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM program_levels",
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    if (programLevelsExist[0].count === 0) {
      console.log('Program levels table is empty, skipping test scores creation');
      return;
    }
    
    // Check if program_level_test_scores table exists
    const testScoresTableExists = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_name = 'program_level_test_scores'",
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    if (testScoresTableExists[0].count === 0) {
      console.log('program_level_test_scores table does not exist, skipping');
      return;
    }
    
    // Clear any existing test scores that might have invalid foreign keys
    await queryInterface.sequelize.query(
      "DELETE FROM program_level_test_scores WHERE programLevelId NOT IN (SELECT id FROM program_levels)",
      { type: Sequelize.QueryTypes.DELETE }
    );
    
    console.log('Foreign key constraint check completed');
  },

  async down(queryInterface, Sequelize) {
    // No rollback needed for this migration
  }
}; 