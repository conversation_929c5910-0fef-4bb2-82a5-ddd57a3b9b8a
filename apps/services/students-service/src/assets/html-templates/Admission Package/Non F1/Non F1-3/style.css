.non-f-1-3,
.non-f-1-3 * {
  box-sizing: border-box;
}
.non-f-1-3 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
   width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.greeting-container {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 373px;
}
.greeting-text {
  color: #ffffff;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 8.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.greeting-container2 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 178.02px;
}
.greeting-container3 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 38.02px;
}
.greeting-container4 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 389px;
}
.greeting-text2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-container5 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 56px;
}
.greeting-container6 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 196px;
}
.greeting-container7 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 275px;
}
.group-34447 {
  position: absolute;
  inset: 0;
}
.group-34420 {
  width: 58px;
  height: 85px;
  position: static;
}
.greeting-container8 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 81px;
  top: 84px;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
}
.group-34418 {
  width: 7px;
  height: 75px;
  position: static;
}
.greeting-container9 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 94px;
}
.greeting-text4 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.4px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.greeting-container10 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 104px;
}
.greeting-text5 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.greeting-container11 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 113px;
}
.greeting-container12 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 122px;
}
.greeting-container13 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 132px;
}
.greeting-container14 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 141px;
}
.greeting-container15 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 150px;
}
.greeting-container16 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 107px;
  top: 159px;
}
.group-34421 {
  width: 109px;
  height: 85px;
  position: static;
}
.group-34419 {
  width: 38px;
  height: 75px;
  position: static;
}
.greeting-container17 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 249px;
  top: 94px;
}
.greeting-container18 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 249px;
  top: 104px;
}
.greeting-container19 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 249px;
  top: 113px;
}
.greeting-container20 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 249px;
  top: 122px;
}
.greeting-container21 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 249px;
  top: 132px;
}
.greeting-container22 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 249px;
  top: 141px;
}
.greeting-container23 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 249px;
  top: 150px;
}
.greeting-container24 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 246.5px;
  top: 159px;
}
.greeting-container25 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 211px;
  top: 84px;
}
.group-34422 {
  width: 58px;
  height: 86px;
  position: static;
}
.group-344192 {
  width: 38px;
  height: 76px;
  position: static;
}
.greeting-container26 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 424.82px;
  top: 94px;
}
.greeting-container27 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 426.82px;
  top: 104px;
}
.greeting-container28 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 426.82px;
  top: 113px;
}
.greeting-container29 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 426.82px;
  top: 122px;
}
.greeting-container30 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 426.82px;
  top: 132px;
}
.greeting-container31 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 428.82px;
  top: 141px;
}
.greeting-container32 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 428.82px;
  top: 150px;
}
.greeting-container33 {
  padding: 0px 2px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 428.82px;
  top: 160px;
}
.greeting-container34 {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 413.82px;
  top: 84px;
}
.greeting-container35 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 463px;
}
.greeting-text6 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-6-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-6-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-container36 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 52.98px;
  top: 500px;
}
.greeting-container37 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 676px;
}
.greeting-text7 {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 750.72px;
  width: 507px;
}
.greeting-text-7-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-7-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-7-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-7-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
.steps-list-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 488px;
  position: absolute;
  left: 72px;
  top: 528px;
}
.frame {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.step-number {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  font-weight: 400;
  position: relative;
}
.step-instruction {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.step-instruction2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.frame2 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.step-instruction3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.3px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  width: 471px;
}
.steps-list-container2 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 492px;
  position: absolute;
  left: 68px;
  top: 218px;
}
.fiber-manual-record {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.step-instruction4 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record2 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-paid-125-for-the-non-refundable-application-for-admissions-fee-plus-675-for-the-course-therefore-the-student-paid-800-total-to-iau {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record3 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-2-classes-and-withdraws-during-the-2nd-week {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-2-classes-and-withdraws-during-the-2nd-week-span {
}
.fiber-manual-record4 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-is-entitled-to-a-pro-rata-refund-of-87-50-of-his-her-675-tuition-paid-which-is-590-63 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.steps-list-container3 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 492px;
  position: absolute;
  left: 68px;
  top: 296px;
}
.fiber-manual-record5 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-non-refundable-application-for-admissions-fee-costs-125-while-a-3-unit-master-s-course-costs-975 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record6 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-paid-125-for-the-non-refundable-application-for-admissions-fee-plus-975-for-the-course-therefore-the-student-paid-1-100-total-to-iau {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.fiber-manual-record7 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-4-weeks-and-withdraws-during-the-4th-week {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.the-3-unit-course-spans-8-lessons-weeks-the-student-attends-4-weeks-and-withdraws-during-the-4th-week-span {
}
.fiber-manual-record8 {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 12px;
  height: 8px;
  position: relative;
  overflow: visible;
}
.the-student-is-entitled-to-a-pro-rata-refund-of-62-5-of-his-her-975-tuition-paid-which-is-609-38 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
