.non-f-1-2,
.non-f-1-2 * {
  box-sizing: border-box;
}
.non-f-1-2 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
   width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.container {
  position: absolute;
  inset: 0;
}
.container2 {
  width: 508px;
  height: 672.37px;
  position: static;
}
.header {
  width: 228px;
  height: 63.4px;
  position: static;
}
.date {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 57px);
  top: 99.84px;
}
.date2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 120.47px;
}
.date3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 141.83px;
}
.date4 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.022000000000000002em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 112.64px);
  top: 152.24px;
}
.header2 {
  width: 221px;
  height: 52.57px;
  position: static;
}
.date5 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 37.63px;
}
.date6 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 52.64px);
  top: 48.15px;
}
.date7 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.023em;
  font-weight: 700;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 58.26px;
}
.date8 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.02em;
  font-weight: 700;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 68.96px;
}
.date9 {
  color: #003eff;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 80.2px;
}
.date-9-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.03em;
  text-decoration: underline;
}
.date-9-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.03em;
}
.date-9-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.16em;
  font-weight: 700;
}
.date-9-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.04em;
  text-decoration: underline;
}
.container3 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 182.52px;
}
.date10 {
  color: #000000;
  text-align: center;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 10px;
  letter-spacing: -0.025em;
  font-weight: 700;
  position: relative;
  align-self: stretch;
}
.greeting-container {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.greeting-text {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.student-information {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 350px;
}
.section-title {
  background: #000000;
  border-style: solid;
  border-color: #000000;
  border-width: 0.75px;
  padding: 10px 2px 10px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 11px;
  position: relative;
}
.greeting-text2 {
  color: #ffffff;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 8.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.table-row {
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  height: 57px;
  position: relative;
}
.row-column {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0.75px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  width: 34px;
  position: relative;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.row-column2 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  width: 92px;
  position: relative;
}
.greeting-text4 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 400;
  position: relative;
}
.row-column3 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  width: 40px;
  position: relative;
}
.row-column4 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  width: 138px;
  position: relative;
}
.frame-1171276313 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  height: 57px;
  position: relative;
}
.row-column5 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 39px;
  height: 14.25px;
  position: relative;
}
.frame-1171276314 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 159px;
  height: 57px;
  position: relative;
}
.row-column6 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 164px;
  height: 14px;
  position: relative;
}
.row-column7 {
  border-style: solid;
  border-color: #ffffff;
  border-width: 0px 0.75px 0.75px 0px;
  padding: 0px 4px 0px 4px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  width: 164px;
  height: 15px;
  position: relative;
}
.container4 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  width: 508px;
  position: absolute;
  left: 52.98px;
  top: 248.06px;
}
.greeting-text5 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-5-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-5-span2 {
  font-family: "TwCenMt-BoldItalic", sans-serif;
  font-weight: 700;
  font-style: italic;
}
.container5 {
  display: flex;
  flex-direction: column;
  gap: 11px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 426px;
}
.greeting-container2 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.container6 {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 514px;
}
.container7 {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.greeting-text-5-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-5-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-5-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-5-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text6 {
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-6-span {
  color: #000000;
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-6-span2 {
  color: #000000;
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-6-span3 {
  color: #003eff;
  font-family: "TwCenMt-Regular", sans-serif;
  text-decoration: underline;
}
.greeting-text7 {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 750.72px;
  width: 507px;
}
.greeting-text-7-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-7-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-7-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-7-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
