.non-f-1-4,
.non-f-1-4 * {
  box-sizing: border-box;
}
.non-f-1-4 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.greeting-container {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 125.02px;
}
.greeting-text {
  color: #ffffff;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 8.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.greeting-container2 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 234.02px;
}
.greeting-container3 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 43.02px;
}
.greeting-container4 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 59px;
}
.greeting-text2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-2-span {
}
.greeting-text-2-span2 {
  text-decoration: underline;
}
.greeting-container5 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 141px;
}
.greeting-container6 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 252px;
}
.greeting-container7 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 565.02px;
}
.greeting-container8 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 583px;
}
.greeting-container9 {
  background: #000000;
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 630.02px;
}
.greeting-container10 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 648px;
}
.greeting-container11 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 703px;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-3-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-3-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-container12 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 472px;
}
.steps-list-container {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 500px;
}
.frame {
  display: flex;
  flex-direction: row;
  gap: 14px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.step-instruction {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.011899999999999999em;
  font-weight: 400;
  position: relative;
}
.step-instruction2 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0319em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.step-instruction-2-span {
  font-family: "TwCenMt-BoldItalic", sans-serif;
  font-weight: 700;
  font-style: italic;
}
.step-instruction-2-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
}
.frame2 {
  display: flex;
  flex-direction: row;
  gap: 11px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.step-instruction3 {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.1px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.step-instruction-3-span {
  font-family: "TwCenMt-BoldItalic", sans-serif;
  font-weight: 700;
  font-style: italic;
}
.step-instruction-3-span2 {
  font-family: "TwCenMt-Italic", sans-serif;
  font-style: italic;
}
.container {
  position: absolute;
  inset: 0;
}
.container2 {
  width: 234px;
  height: 24px;
  position: static;
}
.greeting-container13 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 24px;
  position: absolute;
  left: 54px;
  top: 290px;
}
.greeting-container14 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 24px;
  position: absolute;
  left: 166px;
  top: 290px;
}
.greeting-text4 {
  color: #000000;
  text-align: center;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.container3 {
  width: 234px;
  height: 16px;
  position: static;
}
.greeting-container15 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 16px;
  position: absolute;
  left: 54px;
  top: 313px;
}
.greeting-container16 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 16px;
  position: absolute;
  left: 166px;
  top: 313px;
}
.greeting-container17 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 16px;
  position: absolute;
  left: 54px;
  top: 328px;
}
.greeting-container18 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 16px;
  position: absolute;
  left: 166px;
  top: 328px;
}
.container4 {
  width: 234px;
  height: 23px;
  position: static;
}
.greeting-container19 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 23px;
  position: absolute;
  left: 53.98px;
  top: 358.05px;
}
.greeting-container20 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 23px;
  position: absolute;
  left: 165.98px;
  top: 358.05px;
}
.container5 {
  width: 234px;
  height: 17px;
  position: static;
}
.greeting-container21 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 17px;
  position: absolute;
  left: 54px;
  top: 380.09px;
}
.greeting-container22 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 17px;
  position: absolute;
  left: 166px;
  top: 380.09px;
}
.container6 {
  width: 234px;
  height: 18px;
  position: static;
}
.greeting-container23 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 18px;
  position: absolute;
  left: 54px;
  top: 396.13px;
}
.greeting-container24 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 18px;
  position: absolute;
  left: 166px;
  top: 396.13px;
}
.greeting-container25 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 18px;
  position: absolute;
  left: 54px;
  top: 413.13px;
}
.greeting-container26 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 18px;
  position: absolute;
  left: 166px;
  top: 413.13px;
}
.greeting-container27 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 17px;
  position: absolute;
  left: 54px;
  top: 430.13px;
}
.greeting-container28 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 17px;
  position: absolute;
  left: 166px;
  top: 430.13px;
}
.greeting-container29 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 18px;
  position: absolute;
  left: 54px;
  top: 446.13px;
}
.greeting-container30 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 18px;
  position: absolute;
  left: 166px;
  top: 446.13px;
}
.greeting-container31 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 113px;
  height: 16px;
  position: absolute;
  left: 54px;
  top: 343px;
}
.greeting-container32 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 16px;
  position: absolute;
  left: 166px;
  top: 343px;
}
.container7 {
  width: 253.02px;
  height: 24px;
  position: static;
}
.greeting-container33 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 24px;
  position: absolute;
  left: 306px;
  top: 289.86px;
}
.greeting-container34 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 24px;
  position: absolute;
  left: 463.02px;
  top: 289.86px;
}
.greeting-text5 {
  color: #000000;
  text-align: right;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  position: relative;
  flex: 1;
}
.container8 {
  width: 253.02px;
  height: 16px;
  position: static;
}
.greeting-container35 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 16px;
  position: absolute;
  left: 306px;
  top: 312.86px;
}
.greeting-container36 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 16px;
  position: absolute;
  left: 463.02px;
  top: 312.86px;
}
.greeting-container37 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 16px;
  position: absolute;
  left: 306px;
  top: 327.86px;
}
.greeting-container38 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 16px;
  position: absolute;
  left: 463.02px;
  top: 327.86px;
}
.greeting-container39 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 16px;
  position: absolute;
  left: 306px;
  top: 342.86px;
}
.greeting-container40 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 16px;
  position: absolute;
  left: 463.02px;
  top: 342.86px;
}
.container9 {
  width: 253.02px;
  height: 39.68px;
  position: static;
}
.greeting-container41 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 39.68px;
  position: absolute;
  left: 306px;
  top: 357.86px;
}
.greeting-text6 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  text-decoration: underline;
  position: relative;
  flex: 1;
}
.greeting-container42 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 39.68px;
  position: absolute;
  left: 463.02px;
  top: 357.86px;
}
.greeting-text7 {
  color: #000000;
  text-align: right;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 8px;
  line-height: 9.5px;
  letter-spacing: -0.0019em;
  font-weight: 700;
  text-decoration: underline;
  position: relative;
  flex: 1;
}
.container10 {
  width: 253.02px;
  height: 34.03px;
  position: static;
}
.greeting-container43 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 34.03px;
  position: absolute;
  left: 306.04px;
  top: 396.52px;
}
.greeting-container44 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 34.03px;
  position: absolute;
  left: 463.06px;
  top: 396.52px;
}
.container11 {
  width: 253.02px;
  height: 34px;
  position: static;
}
.greeting-container45 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 158px;
  height: 34px;
  position: absolute;
  left: 306.04px;
  top: 429.55px;
}
.greeting-container46 {
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  padding: 0px 6px 0px 6px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 34px;
  position: absolute;
  left: 463.06px;
  top: 429.55px;
}
.greeting-container47 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 179px;
}
.greeting-text-3-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-text-3-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-container48 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 78px;
}
.greeting-text8 {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 747.72px;
  width: 507px;
}
.greeting-text-8-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-8-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-8-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-8-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
