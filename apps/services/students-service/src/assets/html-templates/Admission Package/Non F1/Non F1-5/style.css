.non-f-1-5,
.non-f-1-5 * {
  box-sizing: border-box;
}
.non-f-1-5 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.border {
  position: absolute;
  inset: 0;
}
.middle-border {
  border-style: solid;
  border-color: #000000;
  border-width: 2.8px;
  width: 561.06px;
  height: 741.05px;
  position: absolute;
  left: 25.45px;
  top: 25.48px;
}
.outer-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 563.97px;
  height: 743.98px;
  position: absolute;
  left: 24.04px;
  top: 24.02px;
}
.inner-border {
  border-style: solid;
  border-color: #000000;
  border-width: 0.5px;
  width: 553.42px;
  height: 733.46px;
  position: absolute;
  left: 29.29px;
  top: 29.3px;
}
.greeting-container {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 52px;
}
.greeting-text {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-text-span {
  font-family: "TwCenMt-Regular", sans-serif;
}
.greeting-text-span2 {
  font-family: "TwCenMt-Bold", sans-serif;
  font-weight: 700;
}
.greeting-container2 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 80px;
}
.greeting-text2 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.greeting-container3 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 53px;
  top: 143.12px;
}
.greeting-text3 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Bold", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 700;
  position: relative;
}
.greeting-container4 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 136px;
  top: 143.12px;
}
.greeting-text4 {
  color: #000000;
  text-align: left;
  font-family: "TwCenMt-Regular", sans-serif;
  font-size: 9px;
  line-height: 9.5px;
  letter-spacing: -0.0219em;
  font-weight: 400;
  position: relative;
}
.greeting-container5 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 197px;
  top: 143.12px;
}
.greeting-container6 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 373px;
  top: 143.12px;
}
.greeting-container7 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 443px;
  top: 143.12px;
}
.greeting-container8 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 481px;
  top: 143.12px;
}
.greeting-container9 {
  padding: 0px 0.98px 0px 0.98px;
  display: flex;
  flex-direction: row;
  gap: 0px;
  align-items: center;
  justify-content: flex-start;
  width: 507px;
  position: absolute;
  left: 53px;
  top: 98px;
}
.greeting-text5 {
  color: #000000;
  text-align: center;
  font-family: "-", sans-serif;
  font-size: 7px;
  line-height: 9.5px;
  letter-spacing: 0undefined;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 747.72px;
  width: 507px;
}
.greeting-text-5-span {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.011899999999999999em;
}
.greeting-text-5-span2 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: -0.0219em;
}
.greeting-text-5-span3 {
  font-family: "TwCenMt-Bold", sans-serif;
  letter-spacing: 0.008100000000000001em;
  font-weight: 700;
}
.greeting-text-5-span4 {
  font-family: "TwCenMt-Regular", sans-serif;
  letter-spacing: 0.008100000000000001em;
}
.vector-157 {
  width: 284.41px;
  height: 0px;
  position: absolute;
  left: 48.66px;
  top: 143.12px;
  overflow: visible;
}
.vector-158 {
  width: 180.93px;
  height: 0px;
  position: absolute;
  left: 369.15px;
  top: 143.04px;
  overflow: visible;
}
.signature {
  width: 19.27%;
  height: 2.69%;
  position: absolute;
  right: 20.72%;
  left: 60.02%;
  bottom: 81.72%;
  top: 15.59%;
  object-fit: cover;
}
.date {
  color: #000000;
  text-align: left;
  font-family: "Cambria-Regular", sans-serif;
  font-size: 8px;
  letter-spacing: 0.03em;
  font-weight: 400;
  position: absolute;
  left: 493.73px;
  top: 127.89px;
}
