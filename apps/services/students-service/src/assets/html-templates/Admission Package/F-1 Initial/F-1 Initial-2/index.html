<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <style>
      a,
      button,
      input,
      select,
      h1,
      h2,
      h3,
      h4,
      h5,
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border: none;
        text-decoration: none;
        background: none;

        -webkit-font-smoothing: antialiased;
      }

      menu,
      ol,
      ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
      }
      p{
        font-size: 12px;
      }
    body {
      font-family: 'TwCenMt-Regular', sans-serif;
    }
    .tuition-container {
      width: 100%;
    }
    
    .campus-title {
      font-weight: bold;
      font-size: 11px;
    }
    
    .tuition-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 15px;
      margin-bottom: 20px;
    }
    
    .tuition-section {
      text-align: center;
    }
    
    .tuition-title {
      font-weight: bold;
      font-size: 9px;
      margin-bottom: 8px;
      line-height: 1.2;
      min-height: 24px;
      display: flex;
      align-items: center;
    }
    
    .tuition-rates {
      font-size: 9px;
      line-height: 1.3;
      margin-left: 22px;
    }
    
    .tuition-item {
      display: flex;
      align-items: center;
      margin-bottom: 3px;
    }
    
    .bullet {
      width: 3px;
      height: 3px;
      background: black;
      border-radius: 50%;
      margin-right: 6px;
      flex-shrink: 0;
    }
    </style>
    <title>Document</title>
  </head>
  <body>
    <div
      style="
        background: #ffffff;
        height: 99vh;
        position: relative;
        overflow: hidden;
        /* width: 100vw; */
        border-style: solid;
        border-color: #000000;
        border-width: 3px;
        padding: 2px;
        margin: 5px;
      "
    >
      <div
        style="
          border-style: solid;
          border-color: #000000;
          border-width: 0.5px;
          padding: 2px;
          height: 100%;
        "
      >
        <div
          style="
            border-style: solid;
            border-color: #000000;
            border-width: 0.5px;
            padding: 2px;
            height: 100%;
          "
        >
          <div
            style="
              display: flex;
              flex-direction: column;
              gap: 20px;
              justify-content: center;
              align-items: center;
              padding: 17px;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <div
                style="
                  color: #000000;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.025em;
                  font-weight: 700;
                "
              >
                {{universityName}}
              </div>
              <div
                style="
                  color: #000000;

                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.025em;
                  font-weight: 700;
                "
              >
               {{campusName}}
              </div>
              <div
                style="
                  color: #000000;
                  text-align: left;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.023em;
                  font-weight: 700;
                "
              >
               {{campusAddress}},{{campusPostalCode}}
              </div>
              <div
                style="
                  color: #000000;
                  text-align: left;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.02em;
                  font-weight: 700;
                "
              >
                Tel: {{campusContactNumber}}
              </div>
              <div
                style="
                  color: #000000;
                  text-align: left;
                  font-family: 'TwCenMt-Regular', sans-serif;
                  font-size: 9px;
                  letter-spacing: 0undefined;
                  font-weight: 400;
                "
              >
                <span>
                  <span
                    style="
                      font-family: 'TwCenMt-Regular', sans-serif;
                      letter-spacing: 0.03em;
                      text-decoration: underline;
                    "
                    >{{campusWebsite}}</span
                  >
                  <span
                    style="
                      font-family: 'TwCenMt-Regular', sans-serif;
                      letter-spacing: 0.03em;
                    "
                  ></span>
                  <span
                    style="
                      font-family: 'TwCenMt-Bold', sans-serif;
                      letter-spacing: 0.16em;
                      font-weight: 700;
                    "
                    >|</span
                  >
                  <span
                    style="
                      font-family: 'TwCenMt-Regular', sans-serif;
                      letter-spacing: 0.04em;
                      text-decoration: underline;
                    "
                    >{{campusEmail}}</span
                  >
                </span>
              </div>
            </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <div
                style="
                  color: #000000;
                  text-align: left;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.025em;
                  font-weight: 700;
                "
              >
                Method of Instruction: Hybrid
              </div>
              <div
                style="
                  color: #000000;
                  text-align: left;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.025em;
                  font-weight: 700;
                "
              >
                Classes will be held at:{{classStartDate}}
              </div>
              <div
                style="
                  color: #000000;
                  text-align: left;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.025em;
                  font-weight: 700;
                  margin-top: 10px;
                "
              >
               {{campusName}}
              </div>
              <div
                style="
                  color: #000000;
                  text-align: left;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.022000000000000002em;
                  font-weight: 700;
                "
              >
                {{campusAddress}},{{campusPostalCode}}
              </div>
            </div>

            <div
              style="
                display: flex;
                flex-direction: column;
                gap: 8px;
                align-items: center;
                justify-content: flex-start;
              "
            >
              <div
                style="
                  color: #000000;
                  text-align: center;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.025em;
                  font-weight: 700;
                  position: relative;
                  align-self: stretch;
                "
              >
                ENROLLMENT AGREEMENT
              </div>
              <div
                style="
                  padding: 0px 0.98px 0px 0.98px;
                  display: flex;
                  flex-direction: row;
                  gap: 0px;
                  align-items: center;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                <div
                  style="
                    color: #000000;
                    text-align: left;
                    font-family: 'TwCenMt-Regular', sans-serif;
                    font-size: 11px;
                    line-height: 14px;
                    letter-spacing: -0.0219em;
                    font-weight: 400;
                    position: relative;
                    flex: 1;
                  "
                >
                  The Enrollment Agreement is a written contract signed between a student and International American University concerning an educational program. The agreement specifies all costs the student named below must pay in order to enroll in the specific educational program for the specified semester. Costs for the program may change if the specifics of the educational program changes (i.e. student takes more units/courses than what was originally outlined in the original Enrollment Agreement). A copy of the completed enrollment agreement shall be given to the student upon enrollment.

                </div>
              </div>
            </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                gap: 8px;
                align-items: center;
                justify-content: flex-start;
              "
            >
              <div
                style="
                  color: #000000;
                  text-align: center;
                  font-family: 'TwCenMt-Bold', sans-serif;
                  font-size: 12px;
                  letter-spacing: -0.025em;
                  font-weight: 700;
                  position: relative;
                  align-self: stretch;
                "
              >
                CLASS LOCATION
              </div>
              <div
                style="
                  padding: 0px 0.98px 0px 0.98px;
                  display: flex;
                  flex-direction: row;
                  gap: 0px;
                  align-items: center;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                <div
                  style="
                    color: #000000;
                    text-align: left;
                    font-family: 'TwCenMt-Regular', sans-serif;
                    font-size: 11px;
                    line-height: 14px;
                    letter-spacing: -0.0219em;
                    font-weight: 400;
                    position: relative;
                    flex: 1;
                  "
                >
                  For hybrid instruction, location of instruction shall take
                  place at the designated main, branch or satellite campus.
                </div>
              </div>
              <div
                style="
                  padding: 0px 0.98px 0px 0.98px;
                  display: flex;
                  flex-direction: row;
                  gap: 0px;
                  align-items: center;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                <div
                  style="
                    color: #000000;
                    text-align: left;
                    font-family: '-', sans-serif;
                    font-size: 11px;
                    line-height: 14px;
                    letter-spacing: -0.0219em;
                    font-weight: 400;
                    position: relative;
                    flex: 1;
                  "
                >
                  <span>
                    <span style="font-family: 'TwCenMt-Regular', sans-serif">
                      For online students, instruction shall take place in IAUonline. IAU’s online instruction is offered in real time and shall transmit the first lesson and any
materials to any student within seven days after the institution accepts the student for admission. IAU shall transmit all of the lessons and other
materials to the student if the student: (A) has fully paid for the educational program; and after having received the first lesson and initial materials,
requests in writing that all of the material be sent. If IAU transmits the balance of the material as the student requests, IAU shall remain obligated to
provide the other educational services it agreed to provide, such as responses to student inquiries, student and faculty interaction, and evaluation and
comment on lessons submitted by the student, but shall not be obligated to pay any refund after all of the lessons and material are transmitted.
                    </span>
                  </span>
                </div>
              </div>
            </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                gap: 0px;
                align-items: flex-start;
                justify-content: flex-start;
                width: 100%;
              "
            >
              <table style="border-collapse: collapse; width: 100%; background-color: white; ">
        <tr>
            <td colspan="6" style="background-color: #000; color: white; font-weight: bold; text-align: left; padding: 2px;  font-size: 11px; font-family: 'TwCenMt-Bold', sans-serif; letter-spacing: 1px;">STUDENT INFORMATION</td>
        </tr>
        <tr>
            <td rowspan="4" style="border: 1px solid #87CDEA; padding: 8px 12px; vertical-align: top; font-weight: bold; width: 80px; text-align: left; font-size: 8.6px; color:#000000; vertical-align: middle;">Name:</td>
             <td rowspan="4" style="border: 1px solid #87CDEA; padding: 8px 12px; vertical-align: top; background-color: white; min-width: 150px; font-weight: 400; font-size: 8.6px; color: black; vertical-align: middle;">{{studentName}}</td>
            <td rowspan="4" style="border: 1px solid #87CDEA; padding: 8px 12px; vertical-align: top; font-weight: bold; width: 80px; text-align: left; font-size: 8.6px; color:#000000; vertical-align: middle;" >Address:</td>
            <td rowspan="4" style="border: 1px solid #87CDEA; padding: 8px 12px; vertical-align: top; background-color: white; min-width: 150px; font-weight: 400; font-size: 8.6px; color: black; vertical-align: middle;">{{presentAddress}}</td>
            <td style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; width: 100px; font-weight: 700; font-size: 8.6px; color:#000000">Gender:</td>
            <td  style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; background-color: white; width: 150px; font-weight: 400; font-size: 8.6px; color: black;">{{gender}}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; width: 100px; font-weight: 700; font-size: 8.6px; color:#000000">DOB:</td>
            <td  style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; background-color: white; width: 150px; font-weight: 400; font-size: 8.6px; color: black;">{{dateOfBirth}}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; width: 100px; font-weight: 700; font-size: 8.6px; color:#000000">Email:</td>
            <td  style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; background-color: white; width: 150px; font-weight: 400; font-size: 8.6px; color: black;">{{email}}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; width: 100px; font-weight: 700; font-size: 8.6px; color:#000000">Tel:</td>
            <td style="border: 1px solid #87CDEA; padding: 2px 4px; vertical-align: top; background-color: white; width: 150px; font-weight: 400; font-size: 8.6px; color: black;">{{phone}}</td>
        </tr>
    </table>
            </div>

            <div
              style="
                display: flex;
                flex-direction: column;
                gap: 11px;
                align-items: flex-start;
                justify-content: flex-start;
              "
            >
                <span colspan="6" style="background-color: #000; color: white; font-weight: bold; text-align: left; padding: 2px;  font-size: 11px; font-family: 'TwCenMt-Bold', sans-serif; letter-spacing: 1px; width: 100%;">TUITION FEES</span>
              <div class="tuition-container">
        <div class="campus-title">{{campusName}}</div>
        <div class="tuition-grid">
          <div class="tuition-section">
            <div class="tuition-title">Undergraduate Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $300</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $900</div>
              </div>
            </div>
          </div>
          
          <div class="tuition-section">
            <div class="tuition-title">Master Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $400</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $1,200</div>
              </div>
            </div>
          </div>
          
          <div class="tuition-section">
            <div class="tuition-title">Doctor of Business Administration Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $425</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $1,275</div>
              </div>
            </div>
          </div>
          
          <div class="tuition-section">
            <div class="tuition-title">Doctor of Management Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $450</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $1,350</div>
              </div>
            </div>
          </div>
        </div>

        <div class="campus-title">Orange County and San Diego</div>
        <div class="tuition-grid">
          <div class="tuition-section">
            <div class="tuition-title">Undergraduate Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $225</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $675</div>
              </div>
            </div>
          </div>
          
          <div class="tuition-section">
            <div class="tuition-title">Master Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $350</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $1,050</div>
              </div>
            </div>
          </div>
          
          <div class="tuition-section">
            <div class="tuition-title">Doctor of Business Administration Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $375</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $1,225</div>
              </div>
            </div>
          </div>
          
          <div class="tuition-section">
            <div class="tuition-title">Doctor of Management Tuition</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per unit: $450</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Per 3-unit course: $1,350</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span colspan="6" style="background-color: #000; color: white; font-weight: bold; text-align: left; padding: 2px;  font-size: 11px; font-family: 'TwCenMt-Bold', sans-serif; letter-spacing: 1px; width: 100%;">REGISTRATION</span>
              <div
                style="
                  padding: 0px 0.98px 0px 0.98px;
                  display: flex;
                  flex-direction: row;
                  gap: 0px;
                  align-items: center;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                <div
                  style="
                    color: #000000;
                    text-align: left;
                    font-family: 'TwCenMt-Regular', sans-serif;
                    font-size: 11px;
                    line-height: 14px;
                    letter-spacing: -0.0219em;
                    font-weight: 400;
                    position: relative;
                    flex: 1;
                  "
                >
                  IAU operates on a semester academic calendar, which is comprised of three (3) academic semesters (Fall, Summer, & Spring). For each
academic semester, a student must register through the university’s registration process. At the time of registration, the student will work with an
academic staff toselect the course(s) in which the student wishes to enroll for the academic semester. The student will register for classes for the
entire academic semester. Depending on the student’s Enrollment Status of full-time or part-time, the student will register for 1 to 4 courses per
academic semester. See explanation of Enrollment Status below.
                </div>
                
              </div>
              <div style="display: flex; justify-content: space-between; width: 100%;">
                    <div class="tuition-section">
            <div class="tuition-title">Fall (Sep-Dec)</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Session 1 (Sep-Oct)</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Session 2 (Nov-Dec)</div>
              </div>
            </div>
          </div>
          <div class="tuition-section">
            <div class="tuition-title">Summer (May-Aug)</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Session 1 (May-Jun)</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Session 2 (Jul-Aug)</div>
              </div>
            </div>
          </div>
          <div class="tuition-section">
            <div class="tuition-title">Spring (Jan-Apr)</div>
            <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Session 1 (Jan-Feb)</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Session 2 (Mar-Apr)</div>
              </div>
            </div>
          </div>
            </div>
              </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                gap: 9px;
                align-items: flex-start;
                justify-content: flex-start;
              "
            >
              <div
                style="
                  background: #000000;
                  padding: 0px 0.98px 0px 0.98px;
                  display: flex;
                  flex-direction: row;
                  gap: 0px;
                  align-items: center;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                  <span colspan="6" style="background-color: #000; color: white; font-weight: bold; text-align: left; padding: 2px;  font-size: 11px; font-family: 'TwCenMt-Bold', sans-serif; letter-spacing: 1px; width: 100%;">REGISTRATION</span>
              </div>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                  align-items: flex-start;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                <div
                  style="
                    padding: 0px 0.98px 0px 0.98px;
                    display: flex;
                    flex-direction: row;
                    gap: 0px;
                    align-items: center;
                    justify-content: flex-start;
                    align-self: stretch;
                    flex-shrink: 0;
                    position: relative;
                  "
                >
                  <div
                    style="
                      color: #000000;
                      text-align: left;
                      font-family: 'TwCenMt-Regular', sans-serif;
                      font-size: 11px;
                      line-height: 14px;
                      letter-spacing: -0.0219em;
                      font-weight: 400;
                      position: relative;
                      flex: 1;
                    "
                  >
                    Full-time matriculated students are required to enroll full-time for each mandatory Spring and Fall semester. F-1 students are required to enroll
full-time in the Summer semester if it is the initial or final enrollment semester of their program. Full-time enrollment is defined as:
                  <div class="tuition-rates">
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Undergraduate: 12 units per mandatory semester.</div>
              </div>
              <div class="tuition-item">
                <div class="bullet"></div>
                <div>Graduate: 9 units per mandatory semester.</div>
              </div>
            </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                gap: 9px;
                align-items: flex-start;
                justify-content: flex-start;
                width: 100%;
              "
            >
              <div
                style="
                  background: #000000;
                  padding: 0px 0.98px 0px 0.98px;
                  display: flex;
                  flex-direction: row;
                  gap: 0px;
                  align-items: center;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                  <span colspan="6" style="background-color: #000; color: white; font-weight: bold; text-align: left; padding: 2px;  font-size: 11px; font-family: 'TwCenMt-Bold', sans-serif; letter-spacing: 1px; width: 100%;">PART-TIME ENROLLMENT</span>
              </div>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                  align-items: flex-start;
                  justify-content: flex-start;
                  align-self: stretch;
                  flex-shrink: 0;
                  position: relative;
                "
              >
                <div
                  style="
                    padding: 0px 0.98px 0px 0.98px;
                    display: flex;
                    flex-direction: row;
                    gap: 0px;
                    align-items: center;
                    justify-content: flex-start;
                    align-self: stretch;
                    flex-shrink: 0;
                    position: relative;
                  "
                >
                  <div
                    style="
                      color: #000000;
                      text-align: left;
                      font-family: 'TwCenMt-Regular', sans-serif;
                      font-size: 11px;
                      line-height: 14px;
                      letter-spacing: -0.0219em;
                      font-weight: 400;
                      position: relative;
                      flex: 1;
                    "
                  >
                    Part-time matriculated and non-matriculated students are required to enroll 6 units per semester, including summer semester.

                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            style="
              color: #000000;
              text-align: center;
              font-family: 'TwCenMt-Regular', sans-serif;
              font-size: 9px;
              line-height: 14px;
              letter-spacing: 0undefined;
              font-weight: 400;
              position: absolute;
              bottom: 10px;
              width: 100%;
            "
          >
            <span>
              <span
                style="
                  font-family: 'TwCenMt-Regular', sans-serif;
                  letter-spacing: -0.011899999999999999em;
                "
                >Enrollment Agreement</span
              >
              <span
                style="
                  font-family: 'TwCenMt-Regular', sans-serif;
                  letter-spacing: -0.0219em;
                "
              ></span>
              <span
                style="
                  font-family: 'TwCenMt-Bold', sans-serif;
                  letter-spacing: 0.008100000000000001em;
                  font-weight: 700;
                "
                >|</span
              >
              <span
                style="
                  font-family: 'TwCenMt-Regular', sans-serif;
                  letter-spacing: 0.008100000000000001em;
                "
                >Page 1</span
              >
              <span
                style="
                  font-family: 'TwCenMt-Bold', sans-serif;
                  letter-spacing: 0.008100000000000001em;
                  font-weight: 700;
                "
                >of 4</span
              >
            </span>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>