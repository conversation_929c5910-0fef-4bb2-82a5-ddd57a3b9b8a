<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="./vars.css">
  <link rel="stylesheet" href="./style.css">
  
  
  <style>
   a,
   button,
   input,
   select,
   h1,
   h2,
   h3,
   h4,
   h5,
   * {
       box-sizing: border-box;
       margin: 0;
       padding: 0;
       border: none;
       text-decoration: none;
       background: none;

       -webkit-font-smoothing: antialiased;
   }

   menu, ol, ul {
       list-style-type: none;
       margin: 0;
       padding: 0;
   }
   .main {
    display: flex;
    justify-content: center;
   }
   body {
     margin: 0;
     padding: 0;
     font-family: Arial, sans-serif;
     width: 595px;
     height: 842px;
     overflow: hidden;
   }
   @media print {
     body {
       -webkit-print-color-adjust: exact;
       print-color-adjust: exact;
       width: 595px;
       height: 842px;
       margin: 0;
       padding: 0;
     }
     @page {
       size: 595px 842px;
       margin: 0;
     }
   }
   /* Ensure each page takes exact design dimensions */
   .cover-page {
     width: 595px !important;
     height: 842px !important;
     margin: 0 !important;
     padding: 0 !important;
     overflow: hidden !important;
     position: relative !important;
     background: #ffffff !important;
   }
   /* Fix image sizing and positioning for design */
   img {
     max-width: 100%;
     height: auto;
     display: block;
   }
   .layer-1 {
     width: 100% !important;
     height: auto !important;
     max-width: 278px !important;
     max-height: 63px !important;
   }
   .university-name-vertical {
     width: auto !important;
     height: auto !important;
     max-width: 100% !important;
     max-height: 100% !important;
   }
   .icon {
     width: 400px !important;
     height: 400px !important;
     object-fit: contain !important;
     max-width: 400px !important;
     max-height: 400px !important;
   }
   /* Ensure proper positioning within design bounds */
   .iau-3 {
     width: 278px !important;
     height: 63px !important;
     max-width: 278px !important;
     max-height: 63px !important;
   }
   .university-name {
     width: 24px !important;
     height: 842px !important;
     max-width: 24px !important;
     max-height: 842px !important;
   }
  </style>
  <title>Document</title>
</head>
<body>
  <div class="main">
    <div class="cover-page">
      <div class="iau-3">
        <img class="layer-1" src="layer-10.svg" />
      </div>
      <div class="letter-title">Conditional Acceptance Letter</div>
      <div class="intake">{{intake}} Intake</div>
      <div class="received-message">You’ve received a</div>
      <div class="congratulations">Congratulations</div>
      <div class="acceptance-message">
        You have been conditionally accepted into the  academic program at
        <br />
        {{universityName}}.
      </div>
      <div class="note">
        Note: This acceptance is conditional upon fulfilling the listed academic and
        administrative requirements as communicated by the Office of Admissions.
      </div>
      <!-- <div class="ellipse-1087"></div>
      <div class="ellipse-1088"></div> -->
      <div class="university-name">
        <img class="university-name-vertical" src="university-name-vertical0.svg" />
      </div>
      <div class="student-info">
        <div class="student-details">
          <div class="name">{{name}}</div>
          <div class="id-number">ID NUMBER: {{idNumber}}</div>
        </div>
      </div>
      <img class="icon" src="icon0.png" />
    </div>
  </div>
</body>
</html>