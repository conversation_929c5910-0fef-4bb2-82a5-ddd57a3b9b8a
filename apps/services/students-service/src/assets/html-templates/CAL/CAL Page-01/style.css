.cover-page,
.cover-page * {
  box-sizing: border-box;
}
.cover-page {
  background: #ffffff;
  height: 842px;
  width: 595px;
  position: relative;
  overflow: hidden;
}
.background-shape {
  border-radius: 35px;
  border-style: solid;
  border-color: rgba(0, 113, 181, 0.3);
  border-width: 1.5px;
  width: 315px;
  height: 425px;
  position: absolute;
  left: -693px;
  top: 495.53px;
  transform-origin: 0 0;
  transform: rotate(-49.222deg) scale(1, 1);
}
.background-image {
  width: 595px;
  height: 419px;
  position: absolute;
  left: 0px;
  top: 423px;
  object-fit: cover;
}
.iau-3 {
  width: 278px;
  height: 63px;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 73px;
  overflow: hidden;
}
.layer-1 {
  height: auto;
  position: absolute;
  left: 0px;
  top: 0px;
  overflow: visible;
}
.letter-title {
  color: #000000;
  text-align: center;
  font-family: "Roboto-Bold", sans-serif;
  font-size: 24px;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 166.5px);
  top: 374px;
}
.intake {
  color: #565964;
  text-align: center;
  font-family: "Roboto-Bold", sans-serif;
  font-size: 18px;
  letter-spacing: -0.02em;
  font-weight: 700;
  position: absolute;
  left: calc(50% - 64.5px);
  top: 414px;
}
.received-message {
  color: #000000;
  text-align: center;
  font-family: "Roboto-Regular", sans-serif;
  font-size: 18px;
  letter-spacing: -0.02em;
  font-weight: 400;
  position: absolute;
  left: calc(50% - 68.5px);
  top: 344px;
}
.congratulations {
  color: #0071b5;
  text-align: center;
  font-family: "TimesNewRoman-Regular", sans-serif;
  font-size: 44px;
  letter-spacing: -0.02em;
  font-weight: 400;
  position: absolute;
  left: calc(50% - 136.5px);
  top: calc(50% - 164px);
  display: flex;
  align-items: center;
  justify-content: center;
}
.acceptance-message {
  color: #4e4e4e;
  text-align: center;
  font-family: "Arial-Regular", sans-serif;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  left: calc(50% - 211.5px);
  top: 455px;
}
.note {
  color: #565964;
  text-align: center;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: absolute;
  left: 50%;
  translate: -50%;
  top: 808px;
  width: 496px;
}
.ellipse-1087 {
  border-radius: 50%;
  width: 65px;
  height: 65px;
  position: absolute;
  left: -33px;
  top: -31px;
  filter: blur(75px);
}
.ellipse-1088 {
  border-radius: 50%;
  width: 65px;
  height: 65px;
  position: absolute;
  left: 538px;
  top: -26px;
  filter: blur(75px);
}
.university-name {
  background: #0071b5;
  width: 24px;
  height: 842px;
  position: absolute;
  left: 48px;
  top: calc(50% - -421px);
  transform-origin: 0 0;
  transform: rotate(180deg) scale(1, 1);
  overflow: hidden;
}
.university-name-vertical {
  height: auto;
  position: absolute;
  left: 6.82px;
  top: 50%;
  translate: 0 -50%;
  overflow: visible;
}
.student-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: flex-start;
  width: 410.5px;
  position: absolute;
  left: 94px;
  top: 594px;
  border-top: 2px solid #565964;
}
.vector-191 {
  flex-shrink: 0;
  width: 410.5px;
  height: 0px;
  position: relative;
  overflow: visible;
}
.student-details {
  display: flex;
  flex-direction: column;
  gap: 7px;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
  margin-top: 10px;
}
.name {
  color: #000000;
  text-align: center;
  font-family: "CantataOne-Regular", sans-serif;
  font-size: 22px;
  letter-spacing: -0.02em;
  font-weight: 400;
  position: relative;
}
.id-number {
  color: #000000;
  text-align: center;
  font-family: "CantataOne-Regular", sans-serif;
  font-size: 14px;
  letter-spacing: -0.02em;
  font-weight: 400;
  position: relative;
}
.icon {
  width: 400px;
  height: 400px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  object-fit: cover;
  aspect-ratio: 1;
}
