
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        p, h1, h2, ul, li {
            margin: 0;
        }

    </style>
</head>

<body>
    <div style="padding: 22px 63px;">
        <div style="display:flex; align-items:flex-end; gap:8px; margin-bottom:4px;">
          <span style="font-size:22px; font-weight:700; color:#1A64B7; letter-spacing:.5px;">IAU</span>
          <span style="font-size:22px; font-weight:700; color:#000;">| ADMISSIONS APPLICATION</span>
      </div>
        <div style="margin-top: 12px;">
            <p style="font-size:9px; weight: 400; line-height:1.45; text-align:justify; margin:6px 0 14px;">
              Complete this Application for Admissions as a PDF form. Please note that our university will NOT accept hand-written applications. Once
completed, print it, sign, and date. Submit this Application, Application Fee, and all other required admission documents. If you have any
questions regarding this section, please contact the Office of Admissions at (213) 262-3939 or email <NAME_EMAIL>.
          </p>
        </div>
        <div style="background:#000;
            color:#fff;
            padding:5px 8px;
            font-family:'Times New Roman', Times, serif;
            font-weight:400;
            font-style: italic;
            font-size:9px;
            line-height:1.2;
            text-transform:uppercase;
            margin:10px 0 8px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;"
        >
          1) Please fill out the following with your personal information:
        </div>
         <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px;">

            <!-- ROW 1: LEGAL NAME -->
            <div style="display:grid; grid-template-columns: 55px 1fr; column-gap:12px; align-items:end;">
              <!-- Left label -->
              <div style="font-weight:700; text-transform:uppercase;">LEGAL NAME:</div>

              <!-- Three fields (First / Middle / Last) -->
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr; column-gap:16px; align-items:end;">
                <!-- First -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">First Name</div>
                </div>
                <!-- Middle -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">Middle Name</div>
                </div>
                <!-- Last -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic;">{}</div>
                  <div style="font-size:9px; text-transform:uppercase; letter-spacing:.2px;">Last Name</div>
                </div>
              </div>
            </div>
         </div>
         <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px;">
            <!-- ROW: PHONE | EMAIL | GENDER -->
            <div style="display:grid; grid-template-columns: 70px 1fr 30px 2.4fr 30px 60px; column-gap:12px; align-items:end;">

              <!-- TELEPHONE label -->
              <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                TELEPHONE #1:
              </div>

              <!-- Phone field + tiny caption -->
              <div style="display:flex; flex-direction:column; gap:2px;">
                <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px;">
                  {}
                </div>
                <div style="font-size:6.5px; text-transform:uppercase; letter-spacing:.25px;">
                  MAIN
                </div>
              </div>

              <!-- EMAIL label -->
              <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                EMAIL:
              </div>

              <!-- Email field -->
              <div style="display:flex; flex-direction:column; gap:2px;">
                <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px;">
                  {}
                </div>
              </div>

              <!-- GENDER label -->
              <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                GENDER:
              </div>

              <!-- Gender value (no underline, as in screenshot) -->
              <div style="margin-top: 12px; line-height:12px;">
                {}
              </div>
            </div>
            <div style="display:grid; grid-template-columns: 70px 1fr 80px 2.4fr 30px 60px; column-gap:12px; align-items:end;">

              <!-- TELEPHONE label -->
              <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                DOB (MM/DD/YYYY):
              </div>

              <!-- Phone field + tiny caption -->
              <div style="display:flex; flex-direction:column; gap:2px;">
                <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px;">
                  {}
                </div>
              </div>

              <!-- EMAIL label -->
              <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                HOW DID YOU HEAR ABOUT IAU:
              </div>

              <!-- Email field -->
              <div style="display:flex; flex-direction:column; gap:2px;">
                <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px;">
                  {}
                </div>
              </div>

              <!-- GENDER label -->
              <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                MILITARY FORCE:
              </div>

              <!-- Gender value (no underline, as in screenshot) -->
              <div style="margin-top: 12px; line-height:12px;">
                {}
              </div>
            </div>
          </div>
        <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px; margin-top:14px;">
          <div style="display:grid; grid-template-columns: 1.5fr 1fr; column-gap:18px; align-items:start;">

            <!-- LEFT SIDE -->
            <div style="position:relative; padding-top:10px;">
              <!-- top horizontal line -->
              <div style="border-top:1px solid #9E9E9E; height:0;"></div>

              <!-- label sitting on the line -->
              <div style="position:absolute; top:-8px; left:0; font-size:9px; font-weight:700; letter-spacing:.2px; background:#fff; padding:0 6px 0 0;">
                {}
              </div>

              <!-- BELOW the line -->
              <div style="display:grid; grid-template-columns: auto 1fr; column-gap:8px; align-items:end; margin-top:6px;">
                <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">CURRENT STATUS:</div>
              </div>
            </div>

            <!-- RIGHT SIDE -->
            <div style="display:grid; grid-template-columns: 160px auto; column-gap:8px; align-items:center;">
              <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px; line-height:1.25;">
                IF YOU ARE REAPPLYING, WHAT WAS<br>YOUR FORMER STUDENT ID #?
              </div>
              <div style="background:#fff; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; text-align:center; font-style:italic; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                {}
              </div>
            </div>
          </div>
            <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px; margin-top:12px;">
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr 1fr; column-gap:18px; align-items:start;">

                <!-- 1) ETHNIC SURVEY -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    ETHNIC SURVEY
                  </div>
                </div>

                <!-- 2) COUNTRY OF CITIZENSHIP -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    COUNTRY OF CITIZENSHIP
                  </div>
                </div>

                <!-- 3) COUNTRY OF BIRTH -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    COUNTRY OF BIRTH
                  </div>
                </div>

                <!-- 4) CITY OF BIRTH (green caption) -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px; color:#0FAA5A;">
                    CITY OF BIRTH
                  </div>
                </div>
              </div>
            </div>
            <div style="background:#000;
              color:#fff;
              padding:5px 8px;
              font-family:'Times New Roman', Times, serif;
              font-weight:400;
              font-style: italic;
              font-size:9px;
              line-height:1.2;
              text-transform:uppercase;
              margin:10px 0 8px;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;"
            >
              2) PLEASE FILL OUT THE AREA BELOW WITH YOUR RESIDENCE ADDRESS:
            </div>
            <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px; margin-top:12px;">
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr; column-gap:18px; align-items:start;">

                <!-- 1) ETHNIC SURVEY -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    STREET
                  </div>
                </div>

                <!-- 2) COUNTRY OF CITIZENSHIP -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    APT#
                  </div>
                </div>

                <!-- 3) COUNTRY OF BIRTH -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    CITY
                  </div>
                </div>
              </div>
            </div>
            <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px; margin-top:12px;">
              <div style="display:grid; grid-template-columns: 1fr 1fr 1fr 1fr; column-gap:18px; align-items:start;">

                <!-- 1) ETHNIC SURVEY -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    STATE
                  </div>
                </div>

                <!-- 2) COUNTRY OF CITIZENSHIP -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    PROVINCE/TERRIOTORY
                  </div>
                </div>

                <!-- 3) COUNTRY OF BIRTH -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    COUNTRY
                  </div>
                </div>

                <!-- 4) CITY OF BIRTH (green caption) -->
                <div style="display:flex; flex-direction:column; gap:3px;">
                  <div style="background:#eaeaea; border-bottom:1px solid #bdbdbd; padding:2px 6px; line-height:12px; min-height:12px; font-style:italic; text-align:left; -webkit-print-color-adjust:exact; print-color-adjust:exact;">
                    {}
                  </div>
                  <div style="font-weight:700; font-style:italic; text-transform:uppercase; letter-spacing:.25px;">
                    POSTAL CODE
                  </div>
                </div>
              </div>
            </div>
            <div style="background:#000;
              color:#fff;
              padding:5px 8px;
              font-family:'Times New Roman', Times, serif;
              font-weight:400;
              font-style: italic;
              font-size:9px;
              line-height:1.2;
              text-transform:uppercase;
              margin:10px 0 8px;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;"
            >
              3) PLEASE SPECIFY PROGRAM OF STUDY:
            </div>
            <div style="width:100%; font-family:'Times New Roman', Times, serif; color:#000; font-size:7.5px;">
            <!-- ROW: PHONE | EMAIL | GENDER -->
              <div style="display:grid; grid-template-columns: 70px 1fr 50px 1.5fr 50px 1fr; column-gap:12px; align-items:end;">

                <!-- TELEPHONE label -->
                <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                  METHOD OF INSTRUCTION:
                </div>

                <!-- Phone field + tiny caption -->
                <div style="display:flex; flex-direction:column; gap:2px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px; background-color: #eaeaea;">
                    {}
                  </div>
                </div>

                <!-- EMAIL label -->
                <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                  ENTERING YEAR:
                </div>

                <!-- Email field -->
                <div style="display:flex; flex-direction:column; gap:2px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px; background-color: #eaeaea;">
                    {}
                  </div>
                </div>

                <!-- GENDER label -->
                <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                  SELECT TERM:
                </div>

                <!-- Gender value (no underline, as in screenshot) -->
                <div style="margin-top: 12px; line-height:12px; background-color: #eaeaea;">
                  {}
                </div>
              </div>
              <div style="display:grid; grid-template-columns: 70px 1.5fr 50px 1fr; column-gap:12px; align-items:end; margin-top: 10px;">

                <!-- TELEPHONE label -->
                <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                  PROGRAM OF STUDY:
                </div>

                <!-- Phone field + tiny caption -->
                <div style="display:flex; flex-direction:column; gap:2px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px; background-color: #eaeaea;">
                    {}
                  </div>
                </div>

                <!-- EMAIL label -->
                <div style="font-weight:700; text-transform:uppercase; letter-spacing:.25px;">
                  ENGLISH PROFICIENCY:
                </div>

                <!-- Email field -->
                <div style="display:flex; flex-direction:column; gap:2px;">
                  <div style="border-bottom:1px solid #000; padding:2px 6px; text-align:center; font-style:italic; line-height:12px; min-height:12px; background-color: #eaeaea;">
                    {}
                  </div>
                </div>
              </div>
            </div>
            <div style="background:#000;
              color:#fff;
              padding:5px 8px;
              font-family:'Times New Roman', Times, serif;
              font-weight:400;
              font-style: italic;
              font-size:9px;
              line-height:1.2;
              text-transform:uppercase;
              margin:10px 0 8px;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;"
            >
              4) SKILLS ASSESSMENT:
            </div>
            <div style="margin-top: 2px;">
              <p style="font-size:9px; weight: 400; line-height:1.45; text-align:justify; margin:6px 0 14px;">
                To ensure that students will be successful, an assessment shall be made at the time of admissions evaluation. An assessment shall be made on whether each
prospective student has the skills and competencies to succeed in an online learning environment, as well as a student's access to computer, software, and internet
technologies. These will be taken into consideration before admitting a prospective student into the program. Applicants are to respond to the survey below so that
IAU may assess the applicant's ability to be successful in an online learning environment. (NOTE: Answering "No" to any one question does not necessarily disqualify
you from admissions.)
            </p>
          </div>
           <div style="display: flex; gap: 18px; margin-bottom: 10px; margin-left: 54px; font-size: 10px;">
            <p style="font-weight: 400;">Yes</p>
            <p style="font-weight: 400;">No</p>
        </div>

        <!-- Checklist Items -->
        <div style="display: flex; flex-direction: column; gap: 10px; margin-left: 34px;">
            <div style="display: flex; gap: 10px; align-items: flex-start; font-weight: 400; text-align: justify; margin-left: 4px;">
                1.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px; font-weight: 400;">I have access to a reliable computer with a stable internet connection.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                2.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I am capable of using standard hardware, such as a computer, USB drive, mouse, keyboard, etc.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                3.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I possess good computer software skills and can use Microsoft Office software (Word, Excel, Powerpoint), web browsers,
and email programs.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                4.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I am a user/subscriber of web forums, blogs, social networking sites (LinkedIn, Facebook), e- commerce
(Amazon, eBay) sites.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                5.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I have previously completed a course, academic program, and/or a professional training course online.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                6.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I am capable of conducting online searches and doing internet research using popular search engines (Google, Yahoo).</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                7.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I am good at prioritizing tasks and often get things done ahead of time without being reminded by my instructor.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                8. 
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I can commit 10-15 hours to studying per week for every 3 unit course I enroll and can plan blocks of time to devote to my
studies.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start; margin-left: 4px;">
                9. 
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I possess good reading comprehension and can comprehend college-level texts with minimal guidance from an instructor.</p>
            </div>
            <div style="display: flex; gap: 10px; align-items: flex-start;">
                10.
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                    <g clip-path="url(#clip0_13169_1393)">
                        <path
                            d="M3.38656 9.44035C3.20707 8.41802 3.74087 7.72502 4.46352 7.15533C5.22051 6.55754 5.98686 6.87595 6.13046 7.80618C6.22098 8.39617 6.27405 8.99552 6.40984 9.57457C6.59401 10.3534 6.95768 10.4517 7.53986 9.9039C9.52989 8.03094 11.5121 6.15017 13.4974 4.27253C13.6488 4.12893 13.7831 3.94476 13.9641 3.86203C14.231 3.73873 14.5463 3.6045 14.8148 3.64508C14.9318 3.66225 15.0208 4.09459 15.0629 4.349C15.1737 5.01703 14.9131 5.51336 14.4246 5.99877C11.9101 8.48826 9.44404 11.0261 6.93115 13.5156C6.49724 13.9448 6.01964 14.5489 5.25484 14.1571C4.46196 13.7513 3.71589 13.3221 3.69404 12.2264C3.67531 11.2961 3.49582 10.369 3.38812 9.44035H3.38656Z"
                            fill="#565964" />
                    </g>
                    <defs>
                        <clipPath id="clip0_13169_1393">
                            <rect width="11.7372" height="10.6462" fill="white"
                                transform="translate(3.35156 3.63721)" />
                        </clipPath>
                    </defs>
                </svg>
                <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="0.5" y="0.571777" width="17.0391" height="16.9805" fill="#F1F4FF" stroke="#565964" />
                </svg>
                <p style="font-size: 12px;">I have local people around me (family, friends, and professional colleagues) who can provide moral, academic, and/or
professional support to pursue my academic program.</p>
            </div>
            </div>
          </div>
        </div>
      </div>
      <div style="
        position:relative; left:28px; right:28px; bottom:8px;
        color:#BFBFBF;
        font-family:'Times New Roman', Times, serif; font-weight:400; font-size:9px; line-height:1.3;
        padding:5px 8px;
        -webkit-print-color-adjust:exact; print-color-adjust:exact;">
        3440 Wilshire Blvd., Suite #1000 | Los Angeles, CA 90010 | Tel: ************ | Fax: ************ | Email: <EMAIL> | Web: www.iaula.edu
      </div>
</body>
</html>