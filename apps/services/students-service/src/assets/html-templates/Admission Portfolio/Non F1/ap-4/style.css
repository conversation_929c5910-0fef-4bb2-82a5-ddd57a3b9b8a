.non-f-1-4,
.non-f-1-4 * {
  box-sizing: border-box;
}
.non-f-1-4 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.non-f-1-ap-page-0004-2 {
  opacity: 0.2;
  width: 612px;
  height: 792px;
  position: absolute;
  left: 0px;
  top: 0px;
  object-fit: cover;
  aspect-ratio: 612/792;
}
.group-34508 {
  position: absolute;
  inset: 0;
}
.title {
  color: #000000;
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 700;
  position: absolute;
  left: 65px;
  top: 13px;
}
.iau {
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 400;
  position: absolute;
  left: 27px;
  top: 13px;
}
.iau-span {
  color: #0080c0;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-weight: 700;
}
.iau-span2 {
  color: #0080c0;
  font-family: "Optima-Bold", sans-serif;
  font-weight: 700;
}
.iau-span3 {
  color: #000000;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-weight: 700;
}
.div {
  color: #000000;
  text-align: left;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 700;
  position: absolute;
  left: 58px;
  top: 12px;
}
.group-34566 {
  position: absolute;
  inset: 0;
}
.title2 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 700;
  position: absolute;
  left: 65px;
  top: 216px;
}
.iau2 {
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 400;
  position: absolute;
  left: 27px;
  top: 216px;
}
.iau-2-span {
  color: #0080c0;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-weight: 700;
}
.iau-2-span2 {
  color: #0080c0;
  font-family: "Optima-Bold", sans-serif;
  font-weight: 700;
}
.iau-2-span3 {
  color: #000000;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-weight: 700;
}
.div2 {
  color: #000000;
  text-align: left;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 700;
  position: absolute;
  left: 58px;
  top: 215px;
}
.group-34567 {
  position: absolute;
  inset: 0;
}
.title3 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 700;
  position: absolute;
  left: 66px;
  top: 328px;
}
.iau3 {
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 400;
  position: absolute;
  left: 28px;
  top: 328px;
}
.iau-3-span {
  color: #0080c0;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-weight: 700;
}
.iau-3-span2 {
  color: #0080c0;
  font-family: "Optima-Bold", sans-serif;
  font-weight: 700;
}
.iau-3-span3 {
  color: #000000;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-weight: 700;
}
.div3 {
  color: #000000;
  text-align: left;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  font-weight: 700;
  position: absolute;
  left: 59px;
  top: 327px;
}
.bill-to-text {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 9px;
  line-height: 11px;
  font-weight: 400;
  position: absolute;
  left: 41px;
  top: 43px;
  width: 508px;
}
.bill-to-text2 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 9.949999809265137px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 36px;
  top: 365px;
  width: 558px;
}
.bill-to-text3 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 9.949999809265137px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 36px;
  top: 485px;
  width: 564px;
}
.bill-to-text4 {
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 9.899999618530273px;
  line-height: 12px;
  font-weight: 700;
  position: absolute;
  left: 35px;
  top: 449px;
  width: 556px;
}
.bill-to-text-4-span {
  color: #000000;
}
.bill-to-text-4-span2 {
  color: #1952bb;
}
.yes {
  color: #000000;
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 9px;
  font-weight: 700;
  position: absolute;
  left: 59px;
  top: 70px;
}
._1 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: absolute;
  left: 40px;
  top: 85px;
}
._2 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 9px;
  font-weight: 400;
  position: absolute;
  left: 40px;
  top: 113px;
}
._3 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: absolute;
  left: 40px;
  top: 141px;
}
._4 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: absolute;
  left: 41px;
  top: 172px;
}
._5 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 10px;
  font-weight: 400;
  position: absolute;
  left: 42px;
  top: 196px;
}
.no {
  color: #000000;
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 9px;
  font-weight: 700;
  position: absolute;
  left: 89px;
  top: 70px;
}
.i-am-currently-authorized-to-study-in-the-u-s-and-will-reside-within-the-state-of-california {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 8.579999923706055px;
  font-weight: 400;
  position: absolute;
  left: 111px;
  top: 82px;
}
.i-will-be-completing-my-program-online-outside-the-u-s {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 9px;
  font-weight: 400;
  position: absolute;
  left: 111px;
  top: 105px;
}
.if-i-have-been-referred-by-a-member-of-the-iau-community-i-have-provided-the-referrer-name-on-page-3-section-1-of-the-application-for-admission-s-form {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 9px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 111px;
  top: 128px;
  width: 463px;
}
.i-would-like-transfer-credit-trc-evaluated-by-checking-yes-in-this-box-i-understand-that-i-must-provide-the-appropriate-transcript-s-or-a-course-by-course-foreign-credential-evaluation-i-also-understand-that-trc-is-awarded-at-the-sole-discretion-of-the-office-of-admissions {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 9px;
  font-weight: 400;
  position: absolute;
  left: 111px;
  top: 157px;
  width: 459px;
}
.are-you-a-current-military-member-in-active-duty-or-a-military-veteran {
  color: #000000;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 8.5px;
  font-weight: 400;
  position: absolute;
  left: 112px;
  top: 196px;
}
.group-34546 {
  position: absolute;
  inset: 0;
}
.rectangle-41966 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 57px;
  top: 83px;
}
.layer-1 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 60.35px;
  top: 86.57px;
  overflow: visible;
}
.group-34548 {
  position: absolute;
  inset: 0;
}
.rectangle-419662 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 88px;
  top: 105.93px;
}
.layer-12 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 91.35px;
  top: 109.49px;
  overflow: visible;
}
.group-34549 {
  position: absolute;
  inset: 0;
}
.rectangle-419663 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 57px;
  top: 135px;
}
.layer-13 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 60.35px;
  top: 138.57px;
  overflow: visible;
}
.group-34551 {
  position: absolute;
  inset: 0;
}
.rectangle-419664 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 88px;
  top: 164.93px;
}
.layer-14 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 91.35px;
  top: 168.49px;
  overflow: visible;
}
.group-34553 {
  position: absolute;
  inset: 0;
}
.rectangle-419665 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 89px;
  top: 189.93px;
}
.layer-15 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 92.35px;
  top: 193.49px;
  overflow: visible;
}
.group-34547 {
  position: absolute;
  inset: 0;
}
.rectangle-419666 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 57px;
  top: 105.93px;
}
.layer-16 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 60.35px;
  top: 109.49px;
  overflow: hidden;
}
.group-34565 {
  position: absolute;
  inset: 0;
}
.rectangle-419667 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 88px;
  top: 82px;
}
.layer-17 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 91.35px;
  top: 85.57px;
  overflow: hidden;
}
.group-34550 {
  position: absolute;
  inset: 0;
}
.rectangle-419668 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 88px;
  top: 135px;
}
.layer-18 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 91.35px;
  top: 138.57px;
  overflow: hidden;
}
.group-34552 {
  position: absolute;
  inset: 0;
}
.rectangle-419669 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 57px;
  top: 165.93px;
}
.layer-19 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 60.35px;
  top: 169.49px;
  overflow: hidden;
}
.group-34554 {
  position: absolute;
  inset: 0;
}
.rectangle-4196610 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 58px;
  top: 189.93px;
}
.layer-110 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 61.35px;
  top: 193.49px;
  overflow: hidden;
}
.terms-text {
  color: #bfbfbf;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 8.039999961853027px;
  line-height: 10.5px;
  letter-spacing: -0.005em;
  font-weight: 400;
  position: absolute;
  left: 29px;
  top: 771px;
}
.page-4 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 8.039999961853027px;
  line-height: 10.5px;
  font-weight: 700;
  position: absolute;
  left: 568px;
  top: 772px;
  width: 28.73px;
}
.label {
  color: #000000;
  text-align: center;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 8px;
  line-height: 10.5px;
  font-weight: 700;
  position: absolute;
  left: 32px;
  top: 251.5px;
}
.group-34509 {
  position: absolute;
  inset: 0;
}
.frame-1171276171 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0px 0.5px 0px;
  padding: 1.5px 0px 1.5px 0px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 175px;
  position: absolute;
  left: 87px;
  top: 246px;
}
.frame-1171276156 {
  background: #e6e6e6;
  padding: 0px 7px 0px 2px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
.odil {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 12px;
  line-height: 10.5px;
  font-weight: 400;
  position: absolute;
  left: 3px;
  top: -1.5px;
}
.input-label {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Italic", sans-serif;
  font-size: 8px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 87px;
  top: 259px;
  width: 156.75px;
}
.group-34510 {
  position: absolute;
  inset: 0;
}
.frame-11712761712 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0px 0.5px 0px;
  padding: 1.5px 0px 1.5px 0px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 147px;
  position: absolute;
  left: 267px;
  top: 246px;
}
.input-label2 {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Italic", sans-serif;
  font-size: 8px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 267px;
  top: 259px;
  width: 131.67px;
}
.frame-11712761713 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0px 0.5px 0px;
  padding: 1.5px 0px 1.5px 0px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 166px;
  position: absolute;
  left: 420px;
  top: 247px;
}
.kizi {
  color: #000000;
  text-align: left;
  font-family: "Roboto-Regular", sans-serif;
  font-size: 12px;
  line-height: 10.5px;
  font-weight: 400;
  position: absolute;
  left: 2px;
  top: -3.5px;
}
.input-label3 {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Italic", sans-serif;
  font-size: 8px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 420px;
  top: 260px;
  width: 148.69px;
}
.group-34570 {
  position: absolute;
  inset: 0;
}
.frame-11712761714 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0px 0.5px 0px;
  padding: 1.5px 0px 1.5px 0px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 148px;
  position: absolute;
  left: 438px;
  top: 273px;
}
.father {
  color: #000000;
  text-align: left;
  font-family: "Roboto-Regular", sans-serif;
  font-size: 12px;
  line-height: 10.5px;
  font-weight: 400;
  position: absolute;
  left: 2px;
  top: -1.5px;
}
.input-label4 {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Italic", sans-serif;
  font-size: 8px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 438px;
  top: 286px;
  width: 132.56px;
}
.group-34568 {
  position: absolute;
  inset: 0;
}
.sevinch-tursunkulova {
  color: #000000;
  text-align: left;
  font-family: "Roboto-Regular", sans-serif;
  font-size: 12.149999618530273px;
  line-height: 10.5px;
  font-weight: 400;
  position: absolute;
  left: 48px;
  top: 615px;
}
.label2 {
  color: #000000;
  text-align: center;
  font-family: "Optima-Bold", sans-serif;
  font-size: 11.949999809265137px;
  line-height: 10.5px;
  font-weight: 700;
  position: absolute;
  left: 47px;
  top: 656px;
}
.vector-189 {
  width: 261.5px;
  height: 0px;
  position: absolute;
  left: 45.5px;
  top: 642.5px;
  overflow: visible;
}
.group-34569 {
  position: absolute;
  inset: 0;
}
._6-23-2025 {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Regular", sans-serif;
  font-size: 12.149999618530273px;
  line-height: 10.5px;
  font-weight: 400;
  position: absolute;
  left: 379px;
  top: 616px;
  width: 53px;
}
.label3 {
  color: #000000;
  text-align: center;
  font-family: "Optima-Bold", sans-serif;
  font-size: 11.949999809265137px;
  line-height: 10.5px;
  font-weight: 700;
  position: absolute;
  left: 380px;
  top: 656px;
}
.vector-190 {
  width: 153.79px;
  height: 0px;
  position: absolute;
  left: 377px;
  top: 643.5px;
  overflow: visible;
}
.label4 {
  color: #000000;
  text-align: center;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 7.949999809265137px;
  line-height: 10.5px;
  font-weight: 700;
  position: absolute;
  left: 32px;
  top: 280px;
}
.label5 {
  color: #000000;
  text-align: center;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 7.949999809265137px;
  line-height: 10.5px;
  font-weight: 700;
  position: absolute;
  left: 230px;
  top: 278px;
}
.frame-1171276454 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 139px;
  position: absolute;
  left: 87px;
  top: 272px;
}
.frame-11712761715 {
  border-style: solid;
  border-color: #000000;
  border-width: 0px 0px 0.5px 0px;
  padding: 1.5px 0px 1.5px 0px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
}
._998-99-610-1308 {
  color: #000000;
  text-align: left;
  font-family: "Roboto-Regular", sans-serif;
  font-size: 12px;
  line-height: 10.5px;
  font-weight: 400;
  position: absolute;
  left: 1px;
  top: -1.5px;
}
.input-label5 {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Italic", sans-serif;
  font-size: 8px;
  font-weight: 400;
  font-style: italic;
  position: relative;
  align-self: stretch;
}
.frame-1171276455 {
  display: flex;
  flex-direction: column;
  gap: 0px;
  align-items: flex-start;
  justify-content: flex-start;
  width: 145px;
  position: absolute;
  left: 285px;
  top: 273px;
}
