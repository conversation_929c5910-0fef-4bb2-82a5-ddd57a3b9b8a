.non-f-1-2,
.non-f-1-2 * {
  box-sizing: border-box;
}
.non-f-1-2 {
  background: #ffffff;
  height: 792px;
  position: relative;
  overflow: hidden;
  width: 595px;
}
.non-f-1-ap-page-0002-1 {
  opacity: 0.1;
  width: 612px;
  height: 792px;
  position: absolute;
  left: 50%;
  translate: -50% -50%;
  top: 50%;
  object-fit: cover;
  aspect-ratio: 612/792;
}
.iau-admissions-portfolio-checklist {
  text-align: left;
  position: absolute;
  left: 25.08px;
  top: 13.4px;
}
.iau-admissions-portfolio-checklist-span {
  color: #027fc1;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  letter-spacing: -0.02em;
  font-weight: 700;
}
.iau-admissions-portfolio-checklist-span2 {
  color: #027fc1;
  font-family: "Optima-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  letter-spacing: -0.04em;
  font-weight: 700;
}
.iau-admissions-portfolio-checklist-span3 {
  color: #027fc1;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-size: 16px;
  line-height: 24.5px;
  letter-spacing: -0.01em;
  font-weight: 700;
}
.iau-admissions-portfolio-checklist-span4 {
  color: #000000;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-size: 15px;
  line-height: 24.5px;
  font-weight: 700;
}
.iau-admissions-portfolio-checklist-span5 {
  color: #000000;
  font-family: "TimesNewRoman-Bold", sans-serif;
  font-size: 18px;
  line-height: 24.5px;
  font-weight: 700;
}
.bill-to-label {
  padding: 0px 0px 0px 10px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: flex-start;
  width: 550px;
  position: absolute;
  left: 27px;
  top: 42px;
  overflow: hidden;
}
.bill-to-text {
  color: #000000;
  text-align: justified;
  font-family: "-", sans-serif;
  font-size: 9px;
  line-height: 10.5px;
  font-weight: 400;
  position: relative;
  flex: 1;
}
.bill-to-text-span {
  font-family: "Optima-Normal", sans-serif;
}
.bill-to-text-span2 {
  font-family: "Optima-Bold", sans-serif;
  font-weight: 700;
}
.terms-text {
  color: #bfbfbf;
  text-align: left;
  font-family: "Optima-Normal", sans-serif;
  font-size: 8.039999961853027px;
  line-height: 10.5px;
  letter-spacing: -0.005em;
  font-weight: 400;
  position: absolute;
  left: 30.8px;
  top: 762.21px;
}
.page-2 {
  color: #000000;
  text-align: left;
  font-family: "Optima-Bold", sans-serif;
  font-size: 8.039999961853027px;
  line-height: 10.5px;
  font-weight: 700;
  position: absolute;
  left: 573.85px;
  top: 762.32px;
  width: 28.73px;
}
.group-34546 {
  position: absolute;
  inset: 0;
}
.rectangle-41966 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 37.91px;
  top: 115.92px;
}
.layer-1 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 41.27px;
  top: 119.48px;
  overflow: visible;
}
.group-34549 {
  position: absolute;
  inset: 0;
}
.rectangle-419662 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 42.04px;
  top: 252.5px;
}
.layer-12 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 45.39px;
  top: 256.07px;
  overflow: visible;
}
.group-34552 {
  position: absolute;
  inset: 0;
}
.rectangle-419663 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 42.04px;
  top: 312.59px;
}
.layer-13 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 45.39px;
  top: 316.15px;
  overflow: visible;
}
.group-34553 {
  position: absolute;
  inset: 0;
}
.rectangle-419664 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 42.04px;
  top: 332.69px;
}
.layer-14 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 45.39px;
  top: 336.26px;
  overflow: visible;
}
.group-34555 {
  position: absolute;
  inset: 0;
}
.rectangle-419665 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 42.83px;
  top: 369.8px;
}
.layer-15 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 46.18px;
  top: 373.37px;
  overflow: visible;
}
.group-34556 {
  position: absolute;
  inset: 0;
}
.rectangle-419666 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 43.47px;
  top: 430.35px;
}
.layer-16 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 46.82px;
  top: 433.92px;
  overflow: visible;
}
.group-34557 {
  position: absolute;
  inset: 0;
}
.rectangle-419667 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 43.47px;
  top: 505.5px;
}
.layer-17 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 46.82px;
  top: 509.06px;
  overflow: visible;
}
.group-34559 {
  position: absolute;
  inset: 0;
}
.rectangle-419668 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 191.41px;
  top: 505.5px;
}
.layer-18 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 194.76px;
  top: 509.06px;
  overflow: hidden;
}
.group-34561 {
  position: absolute;
  inset: 0;
}
.rectangle-419669 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 44.34px;
  top: 567.39px;
}
.layer-19 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 47.69px;
  top: 570.95px;
  overflow: hidden;
}
.group-34562 {
  position: absolute;
  inset: 0;
}
.rectangle-4196610 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 43.07px;
  top: 629.33px;
}
.layer-110 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 46.43px;
  top: 632.89px;
  overflow: hidden;
}
.group-34563 {
  position: absolute;
  inset: 0;
}
.rectangle-4196611 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 43.27px;
  top: 656.39px;
}
.layer-111 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 46.62px;
  top: 659.95px;
  overflow: hidden;
}
.rectangle-4196612 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #000000;
  border-width: 1px;
  width: 540.54px;
  height: 43.8px;
  position: absolute;
  left: 33.98px;
  top: 710.72px;
}
.group-34560 {
  position: absolute;
  inset: 0;
}
.rectangle-4196613 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 382.14px;
  top: 504.72px;
}
.layer-112 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 385.5px;
  top: 508.28px;
  overflow: hidden;
}
.group-34554 {
  position: absolute;
  inset: 0;
}
.rectangle-4196614 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 445.12px;
  top: 330.94px;
}
.layer-113 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 448.47px;
  top: 334.5px;
  overflow: visible;
}
.group-34547 {
  position: absolute;
  inset: 0;
}
.rectangle-4196615 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 225.59px;
  top: 115.06px;
}
.layer-114 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 228.95px;
  top: 118.62px;
  overflow: hidden;
}
.group-34550 {
  position: absolute;
  inset: 0;
}
.rectangle-4196616 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 42.04px;
  top: 272.61px;
}
.layer-115 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 45.39px;
  top: 276.17px;
  overflow: hidden;
}
.group-34551 {
  position: absolute;
  inset: 0;
}
.rectangle-4196617 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 42.04px;
  top: 292.48px;
}
.layer-116 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 45.39px;
  top: 296.05px;
  overflow: hidden;
}
.group-34548 {
  position: absolute;
  inset: 0;
}
.rectangle-4196618 {
  background: #f1f4ff;
  border-style: solid;
  border-color: #565964;
  border-width: 1px;
  width: 18.04px;
  height: 17.98px;
  position: absolute;
  left: 414.05px;
  top: 115.29px;
}
.layer-117 {
  width: 11.74px;
  height: 10.65px;
  position: absolute;
  left: 417.4px;
  top: 118.86px;
  overflow: hidden;
}
.email-please-send-one-email-with-all-required {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0.003em;
  font-weight: 400;
  position: absolute;
  left: 65.57px;
  top: 107.99px;
  width: 140.08px;
}
.email-please-send-one-email-with-all-required-span {
  font-family: "MyriadPro-Bold", sans-serif;
  font-weight: 700;
}
.email-please-send-one-email-with-all-required-span2 {
  font-family: "MyriadPro-Regular", sans-serif;
}
.exam-scores-toefl-pbt-i-bt-ielts-i-tep-toeic-or-gre {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 8px;
  font-weight: 400;
  position: absolute;
  left: 117.45px;
  top: 387.37px;
  width: 205.77px;
}
.exam-scores-toefl-pbt-i-bt-ielts-i-tep-toeic-or-gre-span {
  font-family: "MyriadPro-Bold", sans-serif;
  font-weight: 700;
}
.exam-scores-toefl-pbt-i-bt-ielts-i-tep-toeic-or-gre-span2 {
  font-family: "MyriadPro-Regular", sans-serif;
}
.academic-background-a-high-school-diploma-or-completion-of-24-semester-or-36-quarter-college-level-units-taught-in-english {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 8px;
  font-weight: 400;
  position: absolute;
  left: 117.45px;
  top: 396.61px;
  width: 435.05px;
}
.academic-background-a-high-school-diploma-or-completion-of-24-semester-or-36-quarter-college-level-units-taught-in-english-span {
  font-family: "MyriadPro-Bold", sans-serif;
  font-weight: 700;
}
.academic-background-a-high-school-diploma-or-completion-of-24-semester-or-36-quarter-college-level-units-taught-in-english-span2 {
  font-family: "MyriadPro-Regular", sans-serif;
}
.in-person-please-schedule-an-appointment-with-us-to-submit-documents-in-person-please-note-that-in-person-submissions-are-by-appointment-only {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0.003em;
  font-weight: 400;
  position: absolute;
  left: 247.04px;
  top: 95.5px;
  width: 144.95px;
}
.in-person-please-schedule-an-appointment-with-us-to-submit-documents-in-person-please-note-that-in-person-submissions-are-by-appointment-only-span {
  font-family: "MyriadPro-Bold", sans-serif;
  font-weight: 700;
}
.in-person-please-schedule-an-appointment-with-us-to-submit-documents-in-person-please-note-that-in-person-submissions-are-by-appointment-only-span2 {
  font-family: "MyriadPro-Regular", sans-serif;
}
.in-person-please-schedule-an-appointment-with-us-to-submit-documents-in-person-please-note-that-in-person-submissions-are-by-appointment-only-span3 {
  font-family: "MyriadPro-Regular", sans-serif;
  text-decoration: underline;
}
.mail-please-send-all-required-documentsto {
  color: #000000;
  text-align: left;
  font-family: "-", sans-serif;
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0.003em;
  font-weight: 400;
  position: absolute;
  left: 436.14px;
  top: 96.15px;
  width: 144.95px;
}
.mail-please-send-all-required-documentsto-span {
  font-family: "MyriadPro-Bold", sans-serif;
  font-weight: 700;
}
.mail-please-send-all-required-documentsto-span2 {
  font-family: "MyriadPro-Regular", sans-serif;
}
.documents-to {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 11px;
  font-weight: 400;
  position: absolute;
  left: 65.57px;
  top: 133.04px;
  width: 140.08px;
}
.adm-iaula-edu {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-BoldItalic", sans-serif;
  font-size: 10px;
  line-height: 11px;
  font-weight: 700;
  font-style: italic;
  position: absolute;
  left: 65.57px;
  top: 145.08px;
  width: 140.08px;
}
.please-call-213-262-3939-to-make-an-appointment {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 10px;
  line-height: 13px;
  letter-spacing: 0.01em;
  font-weight: 700;
  position: absolute;
  left: 247.04px;
  top: 163.45px;
  width: 140.19px;
}
.office-of-admissions-international-american-university-3440-wilshire-blvd-suite-1000-los-angeles-ca-90010 {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0.01em;
  font-weight: 700;
  position: absolute;
  left: 436.14px;
  top: 128.36px;
  width: 157.5px;
}
.open-house-orientation-required-for-all-applicants {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 70.21px;
  top: 255.49px;
  width: 232.71px;
}
.signed-school-performance-fact-sheet-link-can-be-found-on-https-iaula-edu-downloads-spfs-required-for-ca-residents-only {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 69.46px;
  top: 276.17px;
  width: 537.56px;
}
.application-for-admissions-fee-all-applicants-125 {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 70.27px;
  top: 293.95px;
  width: 225.02px;
}
.photo-headshot-printed-photo-or-emailed-jpeg-is-acceptable {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 69.74px;
  top: 317.8px;
  width: 262.36px;
}
.academic-credentials-original-transcripts-or-foreign-credential-evaluation {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 71.26px;
  top: 340.91px;
  width: 313px;
}
.evidence-of-english-proficiency-if-english-is-not-your-native-language-please-provide-us-with-one-of-the-following {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 71.26px;
  top: 372.01px;
  width: 484.05px;
}
.copy-of-identification-card-please-provide-us-with-one-of-the-following {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 71.26px;
  top: 433.24px;
  width: 302.75px;
}
.letter-of-interest {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 67.39px;
  top: 508.49px;
  width: 71.86px;
}
.notice-of-basic-eligibility-nobe {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0.01em;
  font-weight: 400;
  position: absolute;
  left: 67.17px;
  top: 570.28px;
  width: 139.11px;
}
.transfer-credit-trc {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0.01em;
  font-weight: 400;
  position: absolute;
  left: 68.79px;
  top: 632.32px;
  width: 90.31px;
}
.standardized-exams-credit-sec {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 68.79px;
  top: 660.44px;
  width: 138.91px;
}
.two-letters-of-recommendation {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 220.98px;
  top: 507.71px;
  width: 139.14px;
}
.resume {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 410.93px;
  top: 504.72px;
  width: 34.07px;
}
.please-view-website-for-more-information-on-test-score-and-grade-requirements {
  color: #000000;
  text-align: left;
  font-family: "MyriadPro-Regular", sans-serif;
  font-size: 8px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 81.84px;
  top: 407.79px;
  width: 278.27px;
}
.high-school-diploma-ged-or-at-bfor-undergraduate-applicants-only {
  color: #000000;
  text-align: left;
  font-family: "TimesNewRoman-Regular", sans-serif;
  font-size: 10px;
  line-height: 12px;
  font-weight: 400;
  position: absolute;
  left: 467.88px;
  top: 332.72px;
  width: 142.97px;
}
.part-1-all-applicants-must-have-the-following {
  color: #0080c0;
  text-align: left;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 12px;
  font-weight: 700;
  position: absolute;
  left: 38.82px;
  top: 226.27px;
}
.part-2-for-dba-applicants-only {
  color: #0080c0;
  text-align: left;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 12px;
  font-weight: 700;
  position: absolute;
  left: 37.57px;
  top: 481.96px;
}
.part-3-all-va-gi-bill-applicants-only {
  color: #0080c0;
  text-align: left;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 12px;
  font-weight: 700;
  position: absolute;
  left: 37.19px;
  top: 547.06px;
}
.part-4-optional {
  color: #0080c0;
  text-align: left;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 12px;
  font-weight: 700;
  position: absolute;
  left: 39.04px;
  top: 607.34px;
}
.part-5-notes {
  color: #0080c0;
  text-align: left;
  font-family: "MyriadPro-Bold", sans-serif;
  font-size: 12px;
  font-weight: 700;
  position: absolute;
  left: 36.45px;
  top: 695.09px;
}
.please-indicate-below-if-you-have-any-special-circumstances-or-factorsso-that-a-admissions-advisor-can-better-assist-you {
  color: #0080c0;
  text-align: left;
  font-family: "Calibri-Italic", sans-serif;
  font-size: 10px;
  letter-spacing: -0.01em;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 116.29px;
  top: 695.09px;
}
.vector-183 {
  width: 2.63px;
  height: 0px;
  position: absolute;
  left: 81.84px;
  top: 391.54px;
  transform: translate(0px, -0.38px);
  overflow: visible;
}
.vector-185 {
  width: 2.63px;
  height: 0px;
  position: absolute;
  left: 96.75px;
  top: 455.41px;
  transform: translate(0px, -0.38px);
  overflow: visible;
}
.vector-187 {
  width: 1.86px;
  height: 0px;
  position: absolute;
  left: 260.21px;
  top: 454.24px;
  overflow: visible;
}
.vector-188 {
  width: 1.86px;
  height: 0px;
  position: absolute;
  left: 260.68px;
  top: 468.61px;
  overflow: visible;
}
.vector-186 {
  width: 2.63px;
  height: 0px;
  position: absolute;
  left: 96.19px;
  top: 468.82px;
  transform: translate(0px, -0.38px);
  overflow: visible;
}
.vector-184 {
  width: 2.63px;
  height: 0px;
  position: absolute;
  left: 81.84px;
  top: 402.61px;
  transform: translate(0px, -0.38px);
  overflow: visible;
}
.copy-of-u-s-passport {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Italic", sans-serif;
  font-size: 8px;
  line-height: 12px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 101.64px;
  top: 450.09px;
}
.copy-of-drivers-license-or-other-state-photo-identity-card-issued-by-the-department-of-motor-vehicles {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Italic", sans-serif;
  font-size: 8px;
  line-height: 12px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 263.9px;
  top: 448px;
}
.copy-of-foreign-government-issued-identification {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Italic", sans-serif;
  font-size: 8px;
  line-height: 12px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 264.7px;
  top: 462.34px;
}
.copy-of-permanent-residence-card {
  color: #000000;
  text-align: left;
  font-family: "Calibri-Italic", sans-serif;
  font-size: 8px;
  line-height: 12px;
  font-weight: 400;
  font-style: italic;
  position: absolute;
  left: 99.97px;
  top: 462.82px;
  width: 114.63px;
}
