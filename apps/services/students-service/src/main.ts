/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { join } from 'path';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MetricsService, TracingService } from '@apply-goal-backend/monitoring';
import { MigrationService } from './app/migration/migration.service';
import { SeedService } from './app/seed/seed.service';
import { SEED_CONFIG } from './app/seed/seed.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get monitoring services
  const metricsService = app.get(MetricsService);
  const tracingService = app.get(TracingService);

  // Start tracing
  await tracingService.start();

  // Run database migrations on startup
  try {
    const migrationService = app.get(MigrationService);
    await migrationService.migrate();
    Logger.log('Database migrations completed successfully');
  } catch (error) {
    Logger.error('Failed to run migrations on startup', error);
    // Don't exit - allow the app to start even if migrations fail
  }



  // HTTP Middleware (Express)
  app.use(express.json());
  app.use('/health', healthRouter);

  // Setup gRPC Microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: ['students', 'student_documents'],
      protoPath: [
        join(process.cwd(), 'libs/shared/dto/src/lib/students/students.proto'),
        join(process.cwd(), 'libs/shared/dto/src/lib/students/student-documents.proto'),
      ],
      url: '0.0.0.0:50058',
    },
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  const port = process.env.PORT || 5008;
  await app.startAllMicroservices();
  await app.listen(port);

  // Auto-seed data based on configuration (AFTER microservices are started)
  if (SEED_CONFIG.autoSeed && SEED_CONFIG.seedOnStartup) {
    if (!SEED_CONFIG.developmentOnly || process.env.NODE_ENV === 'development') {
      try {
        const seedService = app.get(SeedService);
        Logger.log('🌱 Starting automatic seeding...');
        Logger.log(`📊 Seed Config: ${JSON.stringify(SEED_CONFIG, null, 2)}`);
        await seedService.seedAll();
        Logger.log('✅ Seed data created successfully');
      } catch (error) {
        Logger.error('❌ Failed to seed data on startup', error);
        // Don't exit - allow the app to start even if seeding fails
      }
    } else {
      Logger.log('🌱 Skipping auto-seed: not in development mode');
    }
  } else {
    Logger.log('🌱 Auto-seed disabled by configuration');
  }

  Logger.log(
    `📊 Metrics server is running on: http://localhost:${
      process.env.METRICS_PORT || '5008'
    }/metrics`
  );
}

bootstrap();
