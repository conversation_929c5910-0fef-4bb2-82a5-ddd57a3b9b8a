import { <PERSON>, Logger } from '@nestjs/common';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { StudentService } from './student.service';
import { AdmissionPackageService } from './admission-package.service';
import { AdmDocumentService } from './adm-document.service';
import { AdmissionPortfolioService } from './admission-portfolio.service';
import { ApplicationService } from '../application/application.service';
import { UniversityClientService } from '../university/university-client.service';
import { status } from '@grpc/grpc-js';

@Controller()
export class StudentController {
  private readonly logger = new Logger(StudentController.name);

  constructor(
    private readonly studentService: StudentService,
    private readonly admissionPackageService: AdmissionPackageService,
    private readonly admDocumentService: AdmDocumentService,
    private readonly admissionPortfolioService: AdmissionPortfolioService,
    private readonly applicationService: ApplicationService,
    private readonly universityClientService: UniversityClientService
  ) {}
  // Add this method inside your StudentController class

  // private async validateRequirementsForAP(comprehensiveData: any): Promise<{
  //   isValid: boolean;
  //   errors: string[];
  //   missingDocuments: string[];
  // }> {
  //   const result = {
  //     isValid: true,
  //     errors: [],
  //     missingDocuments: []
  //   };

  //   try {
  //     // Get course academic level
  //     const lastAcademic = comprehensiveData.course?.lastAcademic;
  //     if (!lastAcademic) {
  //       result.errors.push('Course academic level not found');
  //       result.isValid = false;
  //       return result;
  //     }

  //     // Define requirements mapping
  //     const requirements = {
  //       academic: {
  //         HSC: ['ssc', 'hsc'],
  //         "Bachelor's Program": ['ssc', 'hsc', 'bsc'],
  //         "Master's Program": ['ssc', 'hsc', 'bsc', 'msc'],
  //         'PhD Program': ['ssc', 'hsc', 'bsc', 'msc', 'phd']
  //       },
  //       identity: ['passport', 'profile'],
  //       proficiency: ['ielts', 'toefl', 'pte', 'duolingo'],
  //       others: ['sop', 'lor', 'resume', 'experience-certificate']
  //     };

  //     // ✅ STEP 1: Check Academic Level Requirements
  //     const requiredAcademicDocs = requirements.academic[lastAcademic];
  //     if (!requiredAcademicDocs) {
  //       result.errors.push(`Unknown academic level: ${lastAcademic}`);
  //       result.isValid = false;
  //       return result;
  //     }

  //     const academicRequirements =
  //       comprehensiveData.applicationRequirement?.academic || [];

  //     for (const requiredDoc of requiredAcademicDocs) {
  //       const hasDocument = academicRequirements.some(
  //         (doc: any) =>
  //           doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
  //       );

  //       if (!hasDocument) {
  //         result.missingDocuments.push(requiredDoc);
  //         result.errors.push(
  //           `Missing required academic document: ${requiredDoc.toUpperCase()}`
  //         );
  //         result.isValid = false;
  //       } else {
  //         // Check if document has URL
  //         const document = academicRequirements.find(
  //           (doc: any) =>
  //             doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
  //         );

  //         if (!document.url || document.url.trim() === '') {
  //           result.errors.push(
  //             `Academic document "${requiredDoc.toUpperCase()}" is missing URL (not uploaded)`
  //           );
  //           result.isValid = false;
  //         }
  //       }
  //     }

  //     // ✅ STEP 2: Check Identity Documents
  //     const identityRequirements =
  //       comprehensiveData.applicationRequirement?.identity || [];

  //     for (const requiredDoc of requirements.identity) {
  //       const hasDocument = identityRequirements.some(
  //         (doc: any) =>
  //           doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
  //       );

  //       if (!hasDocument) {
  //         result.missingDocuments.push(requiredDoc);
  //         result.errors.push(
  //           `Missing required identity document: ${requiredDoc.toUpperCase()}`
  //         );
  //         result.isValid = false;
  //       } else {
  //         // Check if document has URL
  //         const document = identityRequirements.find(
  //           (doc: any) =>
  //             doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
  //         );

  //         if (!document.url || document.url.trim() === '') {
  //           result.errors.push(
  //             `Identity document "${requiredDoc.toUpperCase()}" is missing URL (not uploaded)`
  //           );
  //           result.isValid = false;
  //         }
  //       }
  //     }

  //     return result;
  //   } catch (error) {
  //     result.errors.push(`Validation error: ${error.message}`);
  //     result.isValid = false;
  //     return result;
  //   }
  // }

  private async validateRequirementsForAP(comprehensiveData: any): Promise<{
    isValid: boolean;
    errors: string[];
    missingDocuments: string[];
    validationStep: string; // ✅ Add this to know which step failed
  }> {
    const result = {
      isValid: true,
      errors: [],
      missingDocuments: [],
      validationStep: 'none'
    };

    try {
      // Get application type from student data
      const applicationType = comprehensiveData.student?.applicationType || 'F-1 Initial';
      this.logger.log(`🔍 Validating AP requirements for application type: ${applicationType}`);

      // Get course academic level
      const lastAcademic = comprehensiveData.course?.lastAcademic;
      if (!lastAcademic) {
        result.errors.push('Course academic level not found');
        result.isValid = false;
        result.validationStep = 'academic_level_check';
        return result;
      }

      // Define requirements mapping
      const requirements = {
        academic: {
          HSC: ['ssc', 'hsc'],
          "Bachelor's Program": ['ssc', 'hsc', 'bsc'],
          "Master's Program": ['ssc', 'hsc', 'bsc', 'msc'],
          'PhD Program': ['ssc', 'hsc', 'bsc', 'msc', 'phd']
        },
        identity: ['passport', 'profile'],
        proficiency: ['ielts', 'toefl', 'pte', 'duolingo'],
        others: ['sop', 'lor', 'resume', 'experience-certificate']
      };

      // ✅ STEP 1: Define visa document requirements based on application type
      const visaDocumentRequirements: { [key: string]: Array<{ field: string; name: string }> } = {
        'F-1 Initial': [], // F-1 Initial students don't need I-20, I-94, I-797C
        'F-1 Intial': [], // Handle typo in database
        'Transfer': [
          { field: 'i20Url', name: 'I-20' },
          { field: 'i94Url', name: 'I-94' }
        ], // Transfer students need I-20 and I-94
        'F-1 Transfer': [
          { field: 'i20Url', name: 'I-20' },
          { field: 'i94Url', name: 'I-94' }
        ], // F-1 Transfer students need I-20 and I-94
        'Reinstatement': [
          { field: 'i20Url', name: 'I-20' },
          { field: 'i94Url', name: 'I-94' },
          { field: 'i797cUrl', name: 'I-797C' }
        ], // Reinstatement students need all three
        'F-1 Reinstatement': [
          { field: 'i20Url', name: 'I-20' },
          { field: 'i94Url', name: 'I-94' },
          { field: 'i797cUrl', name: 'I-797C' }
        ], // F-1 Reinstatement students need all three
        'New Program': [
          { field: 'i20Url', name: 'I-20' },
          { field: 'i94Url', name: 'I-94' }
        ], // New Program students need I-20 and I-94
        'Change of Status': [
          { field: 'i20Url', name: 'I-20' },
          { field: 'i94Url', name: 'I-94' },
          { field: 'i797cUrl', name: 'I-797C' }
        ], // Change of Status students need all three
        'Non-F-1': [], // Non-F-1 students don't need these documents
        'Non F1': [] // Handle alternative naming
      };

      // Get required visa documents for this application type
      const requiredVisaDocuments = visaDocumentRequirements[applicationType] || [];

      this.logger.log(`📋 Required visa documents for ${applicationType}:`,
        requiredVisaDocuments.map(doc => doc.name).join(', ') || 'None');

      //  ✅ STEP 2: Validate visa related documents based on application type
      for (const doc of requiredVisaDocuments) {
        const url = comprehensiveData[doc.field];

        if (!url || url.trim() === '') {
          result.missingDocuments.push(doc.name);
          result.errors.push(
            `Missing required visa document for ${applicationType}: ${doc.name} (${doc.field})`
          );
          result.isValid = false;
          this.logger.error(`❌ Missing visa document for ${applicationType}: ${doc.name}`);
          return result;
        } else {
          // Validate URL format
          try {
            new URL(url);
            this.logger.debug(`✅ ${doc.name} URL is valid for ${applicationType}: ${url}`);
          } catch {
            result.errors.push(`Invalid URL format for ${doc.name}: ${url}`);
            result.isValid = false;
            this.logger.error(`❌ Invalid URL for ${doc.name}: ${url}`);
            return result;
          }
        }
      }

      // ✅ STEP 3: Check Academic Level Requirements
      const requiredAcademicDocs = requirements.academic[lastAcademic];
      if (!requiredAcademicDocs) {
        result.errors.push(`Unknown academic level: ${lastAcademic}`);
        result.isValid = false;
        result.validationStep = 'academic_level_mapping';
        return result;
      }

      const academicRequirements =
        comprehensiveData.applicationRequirement?.academic || [];

      for (const requiredDoc of requiredAcademicDocs) {
        const hasDocument = academicRequirements.some(
          (doc: any) =>
            doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
        );

        if (!hasDocument) {
          result.missingDocuments.push(requiredDoc);
          result.errors.push(
            `Missing required academic document: ${requiredDoc.toUpperCase()}`
          );
          result.isValid = false;
          result.validationStep = 'academic_document_missing';
          return result;
        } else {
          const document = academicRequirements.find(
            (doc: any) =>
              doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
          );

          if (!document.url || document.url.trim() === '') {
            result.errors.push(
              `Academic document "${requiredDoc.toUpperCase()}" is missing URL (not uploaded)`
            );
            result.isValid = false;
            result.validationStep = 'academic_document_no_url';
            return result;
          }
        }
      }

      // ✅ STEP 4: Check Identity Documents
      const identityRequirements =
        comprehensiveData.applicationRequirement?.identity || [];

      for (const requiredDoc of requirements.identity) {
        const hasDocument = identityRequirements.some(
          (doc: any) =>
            doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
        );

        if (!hasDocument) {
          result.missingDocuments.push(requiredDoc);
          result.errors.push(
            `Missing required identity document: ${requiredDoc.toUpperCase()}`
          );
          result.isValid = false;
          result.validationStep = 'identity_document_missing';
          return result;
        } else {
          const document = identityRequirements.find(
            (doc: any) =>
              doc.documentName.toLowerCase() === requiredDoc.toLowerCase()
          );

          if (!document.url || document.url.trim() === '') {
            result.errors.push(
              `Identity document "${requiredDoc.toUpperCase()}" is missing URL (not uploaded)`
            );
            result.isValid = false;
            result.validationStep = 'identity_document_no_url';
            return result;
          }
        }
      }

      // ✅ All validations passed - Log summary
      this.logger.log(`✅ AP validation passed for ${applicationType}:`, {
        requiredVisaDocuments: requiredVisaDocuments.length,
        requiredAcademicDocuments: requiredAcademicDocs.length,
        requiredIdentityDocuments: requirements.identity.length
      });

      result.validationStep = 'all_passed';
      return result;
    } catch (error) {
      result.errors.push(`Validation error: ${error.message}`);
      result.isValid = false;
      result.validationStep = 'validation_error';
      return result;
    }
  }

  private convertIdToNumber(id: number | bigint): number {
    if (typeof id === 'bigint') {
      return Number(id);
    }
    return id;
  }
  private normalizeId(id: any): bigint {
    if (typeof id === 'object' && id.low !== undefined) {
      return BigInt(id.low) + (BigInt(id.high) << 32n);
    }
    return BigInt(id);
  }

  /**
   * Comprehensive method to gather all required data for ADM document generation
   * Includes: Student personal info, Application data, University comprehensive data
   */

  @GrpcMethod('StudentService', 'CreateStudent')
  async createStudent(data: any): Promise<any> {
    try {
      this.logger.log('Creating student:', data);
      const student = await this.studentService.createStudent(data);
      return { student };
    } catch (error) {
      this.logger.error('Error creating student:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to create student'
      });
    }
  }

  @GrpcMethod('StudentService', 'CreateStudentAfterRegistration')
  async createStudentAfterRegistration(data: any): Promise<any> {
    try {
      this.logger.log('Creating student after registration completion:', data);
      this.logger.log('Data keys:', Object.keys(data));
      this.logger.log('user_id value:', data.user_id);
      this.logger.log('userId value:', data.userId);

      // Validate required fields for post-registration student creation
      if (!data.email) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Missing required field: email'
        });
      }

      // Temporarily make user_id optional until gRPC code generation is fixed
      if (!data.user_id) {
        this.logger.warn(
          'user_id field is missing, this may indicate gRPC code generation issue'
        );
        // Continue without user_id for now
      }

      // Parse name field if it contains full name
      let firstName = data.first_name || '';
      let lastName = data.last_name || '';

      if (data.name && !firstName && !lastName) {
        const nameParts = data.name.trim().split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
      }

      // Prepare student data with proper field mapping
      const studentData = {
        ...data,
        firstName: firstName,
        lastName: lastName,
        organizationId: data.organizationId || 1, // Default organization
        user_id: data.userId // Ensure user_id is passed through
      };

      this.logger.log(`Creating student with user_id: ${data.user_id}`);
      const student = await this.studentService.createStudent(studentData);

      this.logger.log(
        `Student created successfully after registration: ${student.id}`
      );
      return { student };
    } catch (error) {
      this.logger.error('Error creating student after registration:', error);

      // Handle specific error cases
      if (error.message?.includes('already exists')) {
        throw new RpcException({
          code: status.ALREADY_EXISTS,
          message: 'Student with this email already exists'
        });
      }

      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to create student after registration'
      });
    }
  }

  @GrpcMethod('StudentService', 'CleanupDuplicateStudents')
  async cleanupDuplicateStudents(): Promise<any> {
    try {
      this.logger.log('Starting duplicate student cleanup via gRPC');
      const result = await this.studentService.cleanupDuplicateStudents();
      return {
        success: true,
        message: `Cleanup completed. Merged: ${result.merged}, Deleted: ${result.deleted}`,
        data: result
      };
    } catch (error) {
      this.logger.error('Error during duplicate cleanup:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to cleanup duplicate students'
      });
    }
  }

  @GrpcMethod('StudentService', 'GenerateAdmDocument')
  async generateAdmDocument(request: {
    studentId: string;
    applicationId: number | bigint;
    templateType?: 'pdf' | 'html';
    outputFormat?: string;
    includeAllPages?: boolean;
    pageNumbers?: number[];
  }) {
    const startTime = Date.now();
    try {
      this.logger.log(
        'gRPC GenerateAdmDocument request',
        request,
        typeof request.applicationId
      );

      // Step 1: Get comprehensive ADM data (student + application + university)
      const numericeId = this.normalizeId(request.applicationId);
      const comprehensiveData =
        await this.applicationService.findOneWithRelatedData(numericeId);
      this.logger.log('Comprehensive ADM data:', comprehensiveData);

      // Determine template type (default to 'html' if not specified)
      const templateType = request.templateType || 'html';
      this.logger.log(
        `Using ${templateType} templates for ADM document generation`
      );

      // Step 2: Generate ADM document with comprehensive data and MinIO upload
      const result = await this.admDocumentService.generateAdmDocumentWithMinio(
        comprehensiveData, // This contains student.applicationType
        templateType
      );

      // Step 3: Update the application to mark ADM document as generated
      try {
        const normalizedId = this.normalizeId(request.applicationId);
        await this.applicationService.update(normalizedId, {
          isAdmGenerated: true,
          admUrl: result
        });
        this.logger.log(
          `Updated application ${normalizedId} with isAdmGenerated = true`
        );
      } catch (error) {
        this.logger.warn(`Could not update application flag: ${error.message}`);
      }

      return {
        status: 200,
        message: `ADM document generated successfully using ${templateType} templates`,
        data: {
          documentContent: Buffer.from(''), // Content is uploaded to MinIO, not returned
          filename: `ADM-Document-${request.studentId}.pdf`,
          contentType: 'application/pdf',
          fileSize: 0, // Size not available in new implementation
          generatedAt: new Date().toISOString(),
          minioUrl: result,
          objectKey: result.split('/').pop() || '',
          templateType: templateType
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to generate ADM document: ${error.message}`);
      return {
        status: error?.response?.statusCode || 500,
        message: error?.response?.message || 'Failed to generate ADM document',
        data: null,
        error: error.message || 'Unknown error occurred'
      };
    }
  }

  @GrpcMethod('StudentService', 'GenerateAdmissionPortfolio')
  async generateAdmissionPortfolio(request: {
    studentId: string;
    applicationId: number | bigint;
    templateType?: 'pdf' | 'html';
    outputFormat?: string;
    includeAllPages?: boolean;
    pageNumbers?: number[];
  }) {
    const startTime = Date.now();
    try {
      this.logger.log('gRPC GenerateAdmissionPortfolio request', request);

      // Get student data with all related information
      const numericeId = this.normalizeId(request.applicationId);
      const comprehensiveData =
        await this.applicationService.findOneWithRelatedData(numericeId);

      this.logger.log(
        'Comprehensive Admission portfolio data====================>:',
        comprehensiveData
      );

      const validationResult = await this.validateRequirementsForAP(
        comprehensiveData
      );

      if (!validationResult.isValid) {
        this.logger.error(
          'Requirements validation failed:',
          validationResult.errors
        );

        // ✅ FIX: Return the error response directly, not wrapped
        return {
          status: 400,
          message:
            'Cannot generate Admission Portfolio - Requirements validation failed',
          data: null,
          error: `Validation failed: ${validationResult.errors.join(
            '; '
          )}. Missing documents: ${validationResult.missingDocuments.join(
            ', '
          )}`
          // error: {
          //   validationErrors: validationResult.errors,
          //   missingDocuments: validationResult.missingDocuments,
          //   details: `Missing: ${validationResult.missingDocuments.join(', ')}. Errors: ${validationResult.errors.join('; ')}`
          // }
        };
      }

      // Determine template type (default to 'html' if not specified)
      const templateType = request.templateType || 'html';

      // Generate Admission Portfolio with MinIO upload
      const result =
        await this.admissionPortfolioService.generateAdmissionPortfolioWithMinio(
          comprehensiveData,
          templateType
        );

      // Update the application to mark Admission Portfolio as generated
      try {
        const numericId = this.convertIdToNumber(request.applicationId);
        await this.applicationService.update(numericId, {
          isApGenerated: true,
          apUrl: result
        });
        this.logger.log(
          `Updated application ${numericId} with isApGenerated = true`
        );
      } catch (error) {
        this.logger.warn(`Could not update application flag: ${error.message}`);
      }

      return {
        status: 200,
        message: `Admission Portfolio generated successfully using ${templateType} templates`,
        data: {
          documentContent: Buffer.from(''), // Content is uploaded to MinIO, not returned
          filename: `Admission-Portfolio-${request.studentId}.pdf`,
          contentType: 'application/pdf',
          fileSize: 0, // Size not available in new implementation
          generatedAt: new Date().toISOString(),
          minioUrl: result,
          objectKey: result.split('/').pop() || '',
          templateType: templateType
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate Admission Portfolio: ${error.message}`
      );
      return {
        status: error?.response?.statusCode || 500,
        message:
          error?.response?.message || 'Failed to generate Admission Portfolio',
        data: null,
        error: error.message || 'Unknown error occurred'
      };
    }
  }

  @GrpcMethod('StudentService', 'GetStudent')
  async getStudent(data: { id: string }): Promise<any> {
    try {
      this.logger.log('Getting student:', data.id);
      const student = await this.studentService.getStudent(data.id);
      return { student };
    } catch (error) {
      this.logger.error('Error getting student:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get student'
      });
    }
  }

  @GrpcMethod('StudentService', 'CheckStudentExistsByEmail')
  async checkStudentExistsByEmail(data: { email: string }): Promise<any> {
    try {
      this.logger.log('Checking if student exists by email:', data.email);
      const student = await this.studentService.checkStudentExistsByEmail(
        data.email
      );
      return {
        exists: !!student,
        student: student || null
      };
    } catch (error) {
      this.logger.error('Error checking student by email:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to check student by email'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetStudentByEmail')
  async getStudentByEmail(data: { email: string }): Promise<any> {
    try {
      this.logger.log('Getting student by email:', data.email);
      const student = await this.studentService.getStudentByEmail(data.email);
      if (!student) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Student not found'
        });
      }
      return { student };
    } catch (error) {
      this.logger.error('Error getting student by email:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get student by email'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetStudentByEmail')
  async getStudentByEmailGrpc(data: { email: string }): Promise<any> {
    try {
      this.logger.log('Getting student by email (gRPC):', data.email);
      const student = await this.studentService.getStudentByEmail(data.email);
      return { student };
    } catch (error) {
      this.logger.error('Error getting student by email (gRPC):', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get student by email'
      });
    }
  }

  @GrpcMethod('StudentService', 'UpdateStudent')
  async updateStudent(data: any): Promise<any> {
    try {
      this.logger.log('Updating student:', data.id);
      this.logger.log('Update data:', JSON.stringify(data, null, 2));
      const student = await this.studentService.updateStudent(data.id, data);
      return { student };
    } catch (error) {
      this.logger.error('Error updating student:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to update student'
      });
    }
  }

  @GrpcMethod('StudentService', 'DeleteStudent')
  async deleteStudent(data: { id: string }): Promise<any> {
    try {
      this.logger.log('Deleting student:', data.id);
      await this.studentService.deleteStudent(data.id);
      return { success: true };
    } catch (error) {
      this.logger.error('Error deleting student:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to delete student'
      });
    }
  }

  @GrpcMethod('StudentService', 'ListStudents')
  async listStudents(data: any): Promise<any> {
    try {
      this.logger.log('Listing students with filters:', data);
      const result = await this.studentService.listStudents(data);
      return result;
    } catch (error) {
      this.logger.error('Error listing students:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to list students'
      });
    }
  }

  @GrpcMethod('StudentService', 'EnrollInCourse')
  async enrollInCourse(data: {
    studentId: string;
    course_id: string;
    semester: string;
    course_data?: any;
  }): Promise<any> {
    try {
      this.logger.log('Enrolling student in course:', data);
      const enrollment = await this.studentService.enrollInCourse(
        data.studentId,
        data.course_id,
        data.semester,
        data.course_data
      );
      return { enrollment };
    } catch (error) {
      this.logger.error('Error enrolling in course:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : error.message?.includes('already enrolled')
        ? status.ALREADY_EXISTS
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to enroll in course'
      });
    }
  }

  @GrpcMethod('StudentService', 'DropCourse')
  async dropCourse(data: {
    studentId: string;
    course_id: string;
    semester: string;
  }): Promise<any> {
    try {
      this.logger.log('Dropping course:', data);
      await this.studentService.dropCourse(
        data.studentId,
        data.course_id,
        data.semester
      );
      return { success: true };
    } catch (error) {
      this.logger.error('Error dropping course:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to drop course'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetEnrollments')
  async getEnrollments(data: {
    studentId: string;
    semester?: string;
  }): Promise<any> {
    try {
      this.logger.log('Getting enrollments:', data);
      const enrollments = await this.studentService.getEnrollments(
        data.studentId,
        data.semester
      );
      return { enrollments };
    } catch (error) {
      this.logger.error('Error getting enrollments:', error);
      throw new RpcException({
        code: status.INTERNAL,
        message: error.message || 'Failed to get enrollments'
      });
    }
  }

  @GrpcMethod('StudentService', 'UpdateGrades')
  async updateGrades(data: {
    studentId: string;
    course_id: string;
    grade_data: any;
  }): Promise<any> {
    try {
      this.logger.log('Updating grades:', data);
      const grade = await this.studentService.updateGrades(
        data.studentId,
        data.course_id,
        data.grade_data
      );
      return { grade };
    } catch (error) {
      this.logger.error('Error updating grades:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to update grades'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetAcademicProgress')
  async getAcademicProgress(data: { studentId: string }): Promise<any> {
    try {
      this.logger.log('Getting academic progress:', data.studentId);
      const progress = await this.studentService.getAcademicProgress(
        data.studentId
      );
      return { progress };
    } catch (error) {
      this.logger.error('Error getting academic progress:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get academic progress'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetTranscript')
  async getTranscript(data: { studentId: string }): Promise<any> {
    try {
      this.logger.log('Getting transcript:', data.studentId);
      const transcript = await this.studentService.getTranscript(
        data.studentId
      );
      return { transcript };
    } catch (error) {
      this.logger.error('Error getting transcript:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get transcript'
      });
    }
  }

  @GrpcMethod('StudentService', 'CreateOrUpdateStudentAcademic')
  async createOrUpdateStudentAcademic(data: any): Promise<any> {
    try {
      this.logger.log(
        'Creating/updating student academic info:',
        JSON.stringify(data, null, 2)
      );
      const result = await this.studentService.createOrUpdateStudentAcademic(
        data
      );
      this.logger.log('Service result:', JSON.stringify(result, null, 2));

      // Return the result in the format expected by StudentAcademicResponse proto
      return {
        isActive: true,
        id: result.id?.toString() || '0',
        studentId: result.studentId?.toString() || '0',
        academicRecords: result.academicRecords || [],
        proficiencyRecords: result.proficiencyRecords || [],
        publicationRecords: result.publicationRecords || [],
        otherActivities: result.otherActivities || []
      };
    } catch (error) {
      this.logger.error(
        'Error creating/updating student academic info:',
        error
      );
      throw new RpcException({
        code: status.INTERNAL,
        message:
          error.message || 'Failed to create/update student academic info'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetStudentAcademic')
  async getStudentAcademic(data: any): Promise<any> {
    try {
      // Handle both field names for backward compatibility
      const studentId = data.studentId;
      this.logger.log('Getting student academic info for:', studentId);
      const result = await this.studentService.getStudentAcademic(studentId);
      return {
        isActive: true,
        id: result.id?.toString() || '0',
        studentId: result.studentId?.toString() || '0',
        academicRecords: result.academicRecords || [],
        proficiencyRecords: result.proficiencyRecords || [],
        publicationRecords: result.publicationRecords || [],
        otherActivities: result.otherActivities || []
      };
    } catch (error) {
      this.logger.error('Error getting student academic info:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get student academic info'
      });
    }
  }

  // University-related gRPC methods
  @GrpcMethod('StudentService', 'GetUniversityDetails')
  async getUniversityDetails(data: { universityId: number }): Promise<any> {
    try {
      this.logger.log('Getting university details:', data.universityId);
      const university = await this.studentService.getUniversityDetails(
        data.universityId
      );
      return { university };
    } catch (error) {
      this.logger.error('Error getting university details:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get university details'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetUniversitiesForStudent')
  async getUniversitiesForStudent(data: { studentId: string }): Promise<any> {
    try {
      this.logger.log('Getting universities for student:', data.studentId);
      const universities = await this.studentService.getUniversitiesForStudent(
        data.studentId
      );
      return { universities };
    } catch (error) {
      this.logger.error('Error getting universities for student:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get universities for student'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetCoursesForUniversity')
  async getCoursesForUniversity(data: { universityId: number }): Promise<any> {
    try {
      this.logger.log('Getting courses for university:', data.universityId);
      const courses = await this.studentService.getCoursesForUniversity(
        data.universityId
      );
      return { courses };
    } catch (error) {
      this.logger.error('Error getting courses for university:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get courses for university'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetIntakesForUniversity')
  async getIntakesForUniversity(data: { universityId: number }): Promise<any> {
    try {
      this.logger.log('Getting intakes for university:', data.universityId);
      const intakes = await this.studentService.getIntakesForUniversity(
        data.universityId
      );
      return { intakes };
    } catch (error) {
      this.logger.error('Error getting intakes for university:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get intakes for university'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetProgramLevelsForUniversity')
  async getProgramLevelsForUniversity(data: {
    universityId: number;
  }): Promise<any> {
    try {
      this.logger.log(
        'Getting program levels for university:',
        data.universityId
      );
      const programLevels =
        await this.studentService.getProgramLevelsForUniversity(
          data.universityId
        );
      return { programLevels };
    } catch (error) {
      this.logger.error('Error getting program levels for university:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get program levels for university'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetFieldsOfStudyForUniversity')
  async getFieldsOfStudyForUniversity(data: {
    universityId: number;
  }): Promise<any> {
    try {
      this.logger.log(
        'Getting fields of study for university:',
        data.universityId
      );
      const fieldsOfStudy =
        await this.studentService.getFieldsOfStudyForUniversity(
          data.universityId
        );
      return { fieldsOfStudy };
    } catch (error) {
      this.logger.error('Error getting fields of study for university:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get fields of study for university'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetCampusesForUniversity')
  async getCampusesForUniversity(data: { universityId: number }): Promise<any> {
    try {
      this.logger.log('Getting campuses for university:', data.universityId);
      const campuses = await this.studentService.getCampusesForUniversity(
        data.universityId
      );
      return { campuses };
    } catch (error) {
      this.logger.error('Error getting campuses for university:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get campuses for university'
      });
    }
  }

  @GrpcMethod('StudentService', 'GetFeesForUniversity')
  async getFeesForUniversity(data: { universityId: number }): Promise<any> {
    try {
      this.logger.log('Getting fees for university:', data.universityId);
      const fees = await this.studentService.getFeesForUniversity(
        data.universityId
      );
      return { fees };
    } catch (error) {
      this.logger.error('Error getting fees for university:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to get fees for university'
      });
    }
  }

  @GrpcMethod('StudentService', 'EnrollInCourseWithValidation')
  async enrollInCourseWithValidation(data: {
    studentId: string;
    courseId: string;
    semester: string;
    universityId: number;
  }): Promise<any> {
    try {
      this.logger.log('Enrolling student in course with validation:', data);
      const enrollment = await this.studentService.enrollInCourseWithValidation(
        data.studentId,
        data.courseId,
        data.semester,
        data.universityId
      );
      return { enrollment };
    } catch (error) {
      this.logger.error('Error enrolling in course with validation:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : error.message?.includes('already enrolled')
        ? status.ALREADY_EXISTS
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to enroll in course with validation'
      });
    }
  }

  @GrpcMethod('StudentService', 'UpdateStudentApplicationType')
  async updateStudentApplicationType(data: {
    studentId: string;
    applicationType: string;
  }): Promise<any> {
    try {
      this.logger.log(
        `Updating applicationType via gRPC for ${data.studentId} to ${data.applicationType}`
      );
      const student = await this.studentService.updateStudentApplicationType(
        data.studentId,
        data.applicationType
      );
      return { student };
    } catch (error) {
      this.logger.error('Error updating student applicationType:', error);
      const code = error.message?.includes('not found')
        ? status.NOT_FOUND
        : status.INTERNAL;
      throw new RpcException({
        code,
        message: error.message || 'Failed to update applicationType'
      });
    }
  }
}
