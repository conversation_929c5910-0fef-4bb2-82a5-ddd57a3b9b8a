import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  OnModuleInit,
  Inject
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, where, WhereOptions } from 'sequelize';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { Student } from './models/student.model';
import { Enrollment } from './models/enrollment.model';
import { Grade } from './models/grade.model';
import { EmergencyContact } from './models/emergency-contact.model';
import { StudentAcademicBackground } from './models/student-academic.model';
import { StudentPersonalInfo } from './models/student-personal-info.model';
import { StudentAcademicRecord } from './models/student-academic-record.model';
import { StudentProficiencyRecord } from './models/student-proficiency-record.model';
import { StudentPublication } from './models/student-publication.model';
import { StudentSocialLink } from './models/student-social-link.model';
import { StudentOtherActivity } from './models/student-other-activity.model';
import { CreateStudentRequest } from './students.interface';
import { UniversityClientService } from '../university/university-client.service';

interface AuthService {
  createStudent(data: any): Observable<any>;
}

@Injectable()
export class StudentService implements OnModuleInit {
  private readonly logger = new Logger(StudentService.name);
  private authService: AuthService;

  constructor(
    @InjectModel(Student)
    private readonly studentModel: typeof Student,
    @InjectModel(Enrollment)
    private readonly enrollmentModel: typeof Enrollment,
    @InjectModel(Grade)
    private readonly gradeModel: typeof Grade,
    @InjectModel(StudentAcademicBackground)
    private readonly studentAcademicModel: typeof StudentAcademicBackground,
    @InjectModel(StudentPersonalInfo)
    private readonly studentPersonalInfoModel: typeof StudentPersonalInfo,
    @InjectModel(EmergencyContact)
    private readonly emergencyContactModel: typeof EmergencyContact,
    @InjectModel(StudentAcademicRecord)
    private readonly studentAcademicRecordModel: typeof StudentAcademicRecord,
    @InjectModel(StudentProficiencyRecord)
    private readonly studentProficiencyRecordModel: typeof StudentProficiencyRecord,
    @InjectModel(StudentPublication)
    private readonly studentPublicationModel: typeof StudentPublication,
    @InjectModel(StudentSocialLink)
    private readonly studentSocialLinkModel: typeof StudentSocialLink,
    @InjectModel(StudentOtherActivity)
    private readonly studentOtherActivityModel: typeof StudentOtherActivity,
    @Inject('AUTH_SERVICE')
    private readonly authClient: ClientGrpc,
    private readonly universityClientService: UniversityClientService
  ) {}

  onModuleInit() {
    this.authService = this.authClient.getService<AuthService>('AuthService');
  }

  // Student CRUD Operations
  async createStudent(studentData: CreateStudentRequest): Promise<Student> {
    try {
      // Handle userId mapping from different sources
      let userId: number | null = null;
      
      // Check for userId from CreateStudentRequest
      if (studentData.userId) {
        userId = Number(studentData.userId);
        this.logger.log(`Using userId from CreateStudentRequest: ${userId}`);
      }
      // Check for user_id from CreateStudentAfterRegistrationRequest
      else if (studentData.user_id) {
        userId = Number(studentData.user_id);
        this.logger.log(`Using user_id from CreateStudentAfterRegistrationRequest: ${userId}`);
      }
      
      // Check if student with studentId already exists
      const studentInfo: any = {
        firstName: studentData.firstName || '',
        lastName: studentData.lastName || '',
        email: studentData.email
      };

      // Only add optional fields if they have values
      if (studentData.nameInNative)
        studentInfo.nameInNative = studentData.nameInNative;
      if (studentData.phone) studentInfo.phone = studentData.phone;
      if (studentData.guardianPhone)
        studentInfo.guardianPhone = studentData.guardianPhone;
      if (studentData.dateOfBirth)
        studentInfo.dateOfBirth = studentData.dateOfBirth;
      if (studentData.gender) studentInfo.gender = studentData.gender;
      if (studentData.fatherName)
        studentInfo.fatherName = studentData.fatherName;
      if (studentData.motherName)
        studentInfo.motherName = studentData.motherName;
      if (studentData.nid) studentInfo.nid = studentData.nid;
      if (studentData.passport) studentInfo.passport = studentData.passport;
      if (studentData.presentAddress)
        studentInfo.presentAddress = studentData.presentAddress;
      if (studentData.permanentAddress)
        studentInfo.permanentAddress = studentData.permanentAddress;
      if (studentData.maritalStatus)
        studentInfo.maritalStatus = studentData.maritalStatus;
      if (studentData.sponsor) studentInfo.sponsor = studentData.sponsor;
      if (studentData.emergencyContact)
        studentInfo.emergencyContact = studentData.emergencyContact;
      if (studentData.preferredSubject)
        studentInfo.preferredSubject = studentData.preferredSubject;
      if (studentData.preferredCountry)
        studentInfo.preferredCountry = studentData.preferredCountry;
      if (studentData.socialLinks)
        studentInfo.socialLinks = studentData.socialLinks;
      if (studentData.reference) studentInfo.reference = studentData.reference;
      if (studentData.note) studentInfo.note = studentData.note;

      this.logger.log(
        'Student data received:',
        JSON.stringify(studentData, null, 2)
      );
      this.logger.log(
        'Student info to create:',
        JSON.stringify(studentInfo, null, 2)
      );

      // Check for existing student by multiple criteria to prevent duplicates
      let existingStudent = null;
      
      // First, check by studentId if provided
      if (studentData.studentId) {
        existingStudent = await this.studentModel.findOne({
          where: { studentId: studentData.studentId }
        });
        if (existingStudent) {
          this.logger.log(`Found existing student by studentId: ${studentData.studentId}`);
        }
      }

      // If not found by studentId, check by userId if provided
      if (!existingStudent && studentData.userId) {
        existingStudent = await this.studentModel.findOne({
          where: { userId: Number(studentData.userId) }
        });
        if (existingStudent) {
          this.logger.log(`Found existing student by userId: ${studentData.userId}`);
        }
      }

      // If still not found, check by email in personal info
      if (!existingStudent && studentData.email) {
        const existingPersonalInfo = await this.studentPersonalInfoModel.findOne({
          where: { email: studentData.email }
        });
        
        if (existingPersonalInfo) {
          existingStudent = await this.studentModel.findOne({
            where: { id: existingPersonalInfo.studentId }
          });
          if (existingStudent) {
            this.logger.log(`Found existing student by email: ${studentData.email}`);
          }
        }
      }

      if (existingStudent) {
        this.logger.log(
          `Existing student found: ${existingStudent.studentId} (ID: ${existingStudent.id})`
        );

        // Update existing student's userId if provided and different
        if (userId && existingStudent.userId !== userId) {
          this.logger.log(`Updating existing student userId from ${existingStudent.userId} to ${userId}`);
          await existingStudent.update({ userId: userId });
        }

        // Update existing student personal info
        const existingPersonalInfo =
          await this.studentPersonalInfoModel.findOne({
            where: { studentId: existingStudent.id }
          });

        if (existingPersonalInfo) {
          const updatedStudentInfo = {
            ...studentInfo,
            // studentId: existingStudent.id,
            isPersonalInfoEdited: true
          };

          this.logger.log(
            `Updating student personal info: ${existingStudent.id}`
          );

          this.logger.log('student update info', JSON.stringify(updatedStudentInfo, null, 2));
          // await existingPersonalInfo.update(updatedStudentInfo);
          await this.studentPersonalInfoModel.update({
            ...studentInfo
          },{
            where: {studentId: existingStudent.id}
          })

          this.logger.log(`Student updated successfully: ${existingStudent.id}`);

          // fetch full student information with all related data
          const fullStudent = await this.studentModel.findOne({
            where: { studentId: existingStudent.studentId },
            include: [
              {
                model: this.studentPersonalInfoModel,
                as: 'personalInfo',
                required: false
              },
              {
                model: this.studentAcademicModel,
                as: 'academicBackgrounds',
                required: false
              }
            ]
          });

          this.logger.log(`Full student data: ${JSON.stringify(fullStudent, null, 2)}`);

          if (!fullStudent) {
            throw new NotFoundException('Student not found after update');
          }

          this.logger.log(`Updated student with full data: ${fullStudent.id}`);
          return fullStudent;
        } else {
          // Create personal info for existing student
          this.logger.log(
            `Creating personal info for existing student: ${existingStudent.id}`
          );
          await this.studentPersonalInfoModel.create({
            ...studentInfo,
            studentId: existingStudent.id,
            isPersonalInfoEdited: false
          });

          // Return the updated student with personal info
          const fullStudent = await this.studentModel.findOne({
            where: { studentId: existingStudent.studentId },
            include: [
              {
                model: this.studentPersonalInfoModel,
                as: 'personalInfo',
                required: false
              },
              {
                model: this.studentAcademicModel,
                as: 'academicBackgrounds',
                required: false
              }
            ]
          });

          return fullStudent;
        }
      } else {
        // create new student
        // Generate unique student ID
        const studentId = await this.generateStudentId();

        this.logger.log(`Creating student with studentId: ${studentId}`);

        const savedStudent = await this.studentModel.create({
          ...studentData,
          organizationId: studentData.organizationId
            ? Number(studentData.organizationId)
            : 1,
          agency_id: studentData.agencyId
            ? studentData.agencyId.toString()
            : null,
          studentId: studentId,
          enrollment_date: new Date(),
          userId: userId // Use the mapped userId
        });

        this.logger.log(
          `Student created successfully: ${JSON.stringify(savedStudent, null, 2)}`
        );

        // Create personal info
        this.logger.log(
          `Creating personal info with studentId: ${savedStudent.id}`
        );
        try {
          await this.studentPersonalInfoModel.create({
            ...studentInfo,
            studentId: savedStudent.id,
            isPersonalInfoEdited: false
          });
        } catch (error) {
          this.logger.error('Error creating personal info:', error);
          this.logger.error(
            'Student info being created:',
            JSON.stringify(studentInfo, null, 2)
          );
          this.logger.error('Student ID:', savedStudent.id);
          throw error;
        }

        // fetch full student information with all related data
        const fullStudent = await this.studentModel.findOne({
          where: { studentId: savedStudent.studentId },
          include: [
            {
              model: this.studentPersonalInfoModel,
              as: 'personalInfo',
              required: false
            },
            {
              model: this.studentAcademicModel,
              as: 'academicBackgrounds',
              required: false
            }
          ]
        });

        this.logger.log(
          `Full student data: ${JSON.stringify(fullStudent, null, 2)}`
        );

        if (!fullStudent) {
          throw new NotFoundException('Student not found after creation');
        }

        this.logger.log(`Created student with full data: ${fullStudent.id}`);
        return fullStudent;
      }
    } catch (error) {
      this.logger.error('Error creating student:', error);
      throw error;
    }
  }

  async getStudent(id: string): Promise<Student> {
    try {
      const student = await this.studentModel.findOne({
        where: { studentId: id },
        include: [
          {
            model: this.studentPersonalInfoModel,
            as: 'personalInfo',
            required: false
          },
          {
            model: this.studentAcademicModel,
            as: 'academicBackgrounds',
            required: false
          }
        ]
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // this.logger.debug(`Student data: ${JSON.stringify(student, null, 2)}`);

      return student;
    } catch (error) {
      this.logger.error('Error getting student:', error);
      throw error;
    }
  }

  // async updateStudent(id: string, updateData: any): Promise<Student> {
  //   try {

  //     console.log("+++++++++++++++++++++++++++++++++> student Update inforamtion data from service",id,updateData)
  //     const student = await this.getStudent(id);

  //     // Update student data
  //     await student.update(updateData);

  //     this.logger.log(`Updated student: ${id}`);
  //     return student;
  //   } catch (error) {
  //     this.logger.error('Error updating student:', error);
  //     throw error;
  //   }
  // }

  async updateStudent(studentId: string, updateData: any): Promise<Student> {
    try {
      this.logger.log('Updating student with ID:', studentId);
      this.logger.debug(
        'Incoming update data:',
        JSON.stringify(updateData, null, 2)
      );

      const student = await this.studentModel.findOne({ where: { studentId } });

      if (!student) throw new NotFoundException('Student not found');

      // Update student core fields
      const coreUpdate = {
        organizationId: updateData.organizationId || student.organizationId,
        agencyId: updateData.agencyId || student.agencyId,
        metadata: updateData.metadata || student.metadata,
        status: updateData.status || student.status,
        userId: updateData.userId ? Number(updateData.userId) : student.userId
      };
      await student.update(coreUpdate);

      // Prepare personal info update
      const personalInfoPayload: Partial<StudentPersonalInfo> = {
        firstName: updateData.firstName,
        lastName: updateData.lastName,
        nameInNative: updateData.nameInNative,
        email: updateData.email,
        phone: updateData.phone,
        guardianPhone: updateData.guardianPhone,
        dateOfBirth: updateData.dateOfBirth,
        gender: updateData.gender,
        fatherName: updateData.fatherName,
        motherName: updateData.motherName,
        nid: updateData.nid,
        passport: updateData.passport,
        presentAddress: updateData.presentAddress,
        permanentAddress: updateData.permanentAddress,
        maritalStatus: updateData.maritalStatus,
        sponsor: updateData.sponsor,
        emergencyContact: updateData.emergencyContact,
        preferredSubject: updateData.preferredSubject,
        preferredCountry: updateData.preferredCountry,
        socialLinks: updateData.socialLinks,
        reference: updateData.reference,
        note: updateData.note
      };

      // Update or create student personal info
      const personalInfo = await this.studentPersonalInfoModel.findOne({
        where: { studentId: student.id }
      });

      if (personalInfo) {
        // Set isPersonalInfoEdited to true when updating existing personal info
        await personalInfo.update({
          ...personalInfoPayload,
          isPersonalInfoEdited: true
        });
        this.logger.log(`Updated personal info and set isPersonalInfoEdited to true for student: ${studentId}`);
      } else {
        // For new personal info, isPersonalInfoEdited defaults to false
        await this.studentPersonalInfoModel.create({
          ...personalInfoPayload,
          studentId: student.id,
          isPersonalInfoEdited: false
        });
        this.logger.log(`Created new personal info for student: ${studentId}`);
      }

      // Update or create academic background
      const academic = await this.studentAcademicModel.findOne({
        where: { studentId: student.id }
      });

      const academicPayload = {
        academic_records: updateData.academic,
        proficiencyRecords: updateData.proficiency,
        publicationRecords: updateData.publications,
        otherActivities: updateData.otherActivities
      } as any;

      if (academic) {
        await academic.update(academicPayload);
      } else {
        await this.studentAcademicModel.create({
          ...academicPayload,
          studentId: student.id
        });
      }

      this.logger.log(`Updated student: ${studentId}`);
      return student;
    } catch (error) {
      this.logger.error('Error updating student:', error);
      throw error;
    }
  }

  async updateStudentApplicationType(
    studentId: string,
    applicationType: string
  ): Promise<Student> {
    try {
      this.logger.log(
        `Updating applicationType for studentId ${studentId} to ${applicationType}`
      );
      const student = await this.studentModel.findOne({ where: { studentId } });
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      await student.update({ applicationType });
      this.logger.log(
        `Updated applicationType for studentId ${studentId} to ${applicationType}`
      );
      return student;
    } catch (error) {
      this.logger.error('Error updating applicationType:', error);
      throw error;
    }
  }

  async deleteStudent(id: string): Promise<void> {
    try {
      const student = await this.getStudent(id);
      await student.destroy();
      this.logger.log(`Deleted student: ${id}`);
    } catch (error) {
      this.logger.error('Error deleting student:', error);
      throw error;
    }
  }

  async listStudents(
    filters: any
  ): Promise<{ students: Student[]; total: number; page_token?: string }> {
    try {
      const {
        page_size = 10,
        page_token = '',
        filter = '',
        order_by = 'createdAt desc'
      } = filters;

      const whereCondition: WhereOptions = {};

      // Apply filters
      if (filter) {
        (whereCondition as any)[Op.or] = [
          {
            studentId: { [Op.iLike]: `%${filter}%` },
            organizationId: filters.organizationId
              ? Number(filters.organizationId)
              : undefined
          }
        ];
      }

      // Apply ordering
      const [orderField, orderDirection] = order_by.split(' ');
      const order: any = [[orderField, orderDirection.toUpperCase()]];

      // Apply pagination
      const offset = page_token ? parseInt(page_token, 10) : 0;

      const { rows: students, count: total } =
        await this.studentModel.findAndCountAll({
          where: whereCondition,
          order,
          limit: page_size,
          offset,
          include: [
            {
              model: this.studentPersonalInfoModel,
              as: 'personalInfo',
              required: false
            }
          ]
        });

      const nextPageToken =
        offset + page_size < total
          ? (offset + page_size).toString()
          : undefined;

      return {
        students,
        total,
        page_token: nextPageToken
      };
    } catch (error) {
      this.logger.error('Error listing students:', error);
      throw error;
    }
  }

  // Academic Operations
  async enrollInCourse(
    studentId: string,
    courseId: string,
    semester: string,
    courseData?: any
  ): Promise<Enrollment> {
    try {
      // Check if student exists
      await this.getStudent(studentId);

      // Check if already enrolled in this course for this semester
      const existingEnrollment = await this.enrollmentModel.findOne({
        where: { studentId: studentId, course_id: courseId, semester }
      });

      if (existingEnrollment) {
        throw new ConflictException(
          'Student is already enrolled in this course for this semester'
        );
      }

      // Get the student record to get the ID
      const student = await this.studentModel.findOne({
        where: { studentId: studentId }
      });
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const savedEnrollment = await this.enrollmentModel.create({
        studentId: student.id,
        course_id: courseId,
        semester,
        enrollment_date: new Date(),
        academic_year: new Date().getFullYear(),
        ...courseData
      });
      this.logger.log(`Student ${studentId} enrolled in course ${courseId}`);
      return savedEnrollment;
    } catch (error) {
      this.logger.error('Error enrolling in course:', error);
      throw error;
    }
  }

  async dropCourse(
    studentId: string,
    courseId: string,
    semester: string
  ): Promise<void> {
    try {
      const enrollment = await this.enrollmentModel.findOne({
        where: { studentId: studentId, course_id: courseId, semester }
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      await enrollment.update({
        status: 'dropped',
        drop_date: new Date()
      });

      this.logger.log(`Student ${studentId} dropped course ${courseId}`);
    } catch (error) {
      this.logger.error('Error dropping course:', error);
      throw error;
    }
  }

  async getEnrollments(
    studentId: string,
    semester?: string
  ): Promise<Enrollment[]> {
    try {
      const whereCondition: any = { studentId: studentId };
      if (semester) {
        whereCondition.semester = semester;
      }

      const enrollments = await this.enrollmentModel.findAll({
        where: whereCondition,
        include: [{ model: this.gradeModel }],
        order: [['createdAt', 'DESC']]
      });

      return enrollments;
    } catch (error) {
      this.logger.error('Error getting enrollments:', error);
      throw error;
    }
  }

  async updateGrades(
    studentId: string,
    courseId: string,
    gradeData: any
  ): Promise<Grade> {
    try {
      // Find the enrollment
      const enrollment = await this.enrollmentModel.findOne({
        where: { studentId: studentId, course_id: courseId }
      });

      if (!enrollment) {
        throw new NotFoundException('Enrollment not found');
      }

      // Check if grade already exists
      let grade = await this.gradeModel.findOne({
        where: { studentId: studentId, enrollment_id: enrollment.id }
      });

      if (grade) {
        // Update existing grade
        await grade.update(gradeData);
      } else {
        // Get the student record to get the ID
        const student = await this.studentModel.findOne({
          where: { studentId: studentId }
        });
        if (!student) {
          throw new NotFoundException('Student not found');
        }

        // Create new grade
        grade = await this.gradeModel.create({
          studentId: student.id,
          enrollment_id: enrollment.id,
          course_id: courseId,
          grade_date: new Date(),
          academic_year: enrollment.academic_year,
          semester: enrollment.semester,
          credits: enrollment.credits,
          course_name: enrollment.course_name,
          course_code: enrollment.course_code,
          ...gradeData
        });
      }

      // Update student GPA
      await this.updateStudentGPA(studentId);

      this.logger.log(
        `Updated grade for student ${studentId} in course ${courseId}`
      );
      return grade;
    } catch (error) {
      this.logger.error('Error updating grades:', error);
      throw error;
    }
  }

  async getAcademicProgress(studentId: string): Promise<any> {
    try {
      const student = await this.getStudent(studentId);
      const enrollments = await this.getEnrollments(studentId);
      const grades = await this.gradeModel.findAll({
        where: { studentId: studentId },
        order: [
          ['academic_year', 'DESC'],
          ['semester', 'DESC']
        ]
      });

      // Calculate academic statistics
      const totalCreditsAttempted = grades.reduce(
        (sum, grade) => sum + grade.credits,
        0
      );
      const totalQualityPoints = grades.reduce(
        (sum, grade) => sum + grade.quality_points,
        0
      );
      const currentGPA =
        totalCreditsAttempted > 0
          ? totalQualityPoints / totalCreditsAttempted
          : 0;

      const completedCourses = grades.filter(
        (grade) => grade.is_passing
      ).length;
      const failedCourses = grades.filter((grade) => !grade.is_passing).length;

      return {
        studentId: studentId,
        student_name: student.full_name,
        current_gpa: Math.round(currentGPA * 100) / 100,
        total_credits_attempted: totalCreditsAttempted,
        total_credits_earned: grades
          .filter((g) => g.is_passing)
          .reduce((sum, g) => sum + g.credits, 0),
        completed_courses: completedCourses,
        failed_courses: failedCourses,
        current_enrollments: enrollments.filter((e) => e.is_current).length,
        academic_standing: this.getAcademicStanding(currentGPA),
        semester_breakdown: this.getSemesterBreakdown(grades)
      };
    } catch (error) {
      this.logger.error('Error getting academic progress:', error);
      throw error;
    }
  }

  async getTranscript(studentId: string): Promise<any> {
    try {
      const student = await this.getStudent(studentId);
      const grades = await this.gradeModel.findAll({
        where: { studentId: studentId },
        order: [
          ['academic_year', 'ASC'],
          ['semester', 'ASC']
        ]
      });

      const semesterGroups = this.groupGradesBySemester(grades);

      return {
        student_info: {
          id: student.id,
          studentId: student.studentId,
          name: student.full_name,
          email: student.personalInfo?.email || '',
          major: 'Not specified', // This field was moved to academic records
          minor: 'Not specified', // This field was moved to academic records
          // enrollment_date: student.enrollment_date,
          // graduation_date: student.graduation_date,
          status: student.status
        },
        academic_summary: {
          // cumulative_gpa: student.gpa,
          // total_credits: student.total_credits,
          // completed_credits: student.total_credits, // Using total_credits as completed_credits
          // academic_standing: this.getAcademicStanding(student.gpa || 0),
        },
        semester_records: semesterGroups,
        generated_at: new Date()
      };
    } catch (error) {
      this.logger.error('Error getting transcript:', error);
      throw error;
    }
  }

  // Helper method to clean up duplicate student records
  async cleanupDuplicateStudents(): Promise<{ merged: number; deleted: number }> {
    try {
      this.logger.log('Starting duplicate student cleanup...');
      
      let mergedCount = 0;
      let deletedCount = 0;

      // Find all students with the same email
      const allStudents = await this.studentModel.findAll({
        include: [
          {
            model: this.studentPersonalInfoModel,
            as: 'personalInfo',
            required: false
          }
        ]
      });

      // Group students by email
      const studentsByEmail = new Map<string, Student[]>();
      
      for (const student of allStudents) {
        if (student.personalInfo?.email) {
          const email = student.personalInfo.email;
          if (!studentsByEmail.has(email)) {
            studentsByEmail.set(email, []);
          }
          studentsByEmail.get(email)!.push(student);
        }
      }

      // Process each group of duplicates
      for (const [email, students] of studentsByEmail.entries()) {
        if (students.length > 1) {
          this.logger.log(`Found ${students.length} duplicate students for email: ${email}`);
          
          // Sort students by completeness (most complete first)
          students.sort((a, b) => {
            const aFields = Object.values(a.personalInfo?.toJSON() || {}).filter(
              value => value !== null && value !== undefined && value !== ''
            ).length;
            const bFields = Object.values(b.personalInfo?.toJSON() || {}).filter(
              value => value !== null && value !== undefined && value !== ''
            ).length;
            return bFields - aFields; // Descending order
          });

          const primaryStudent = students[0];
          const duplicateStudents = students.slice(1);

          this.logger.log(`Keeping primary student: ${primaryStudent.studentId}, merging ${duplicateStudents.length} duplicates`);

          // Merge data from duplicate students into primary student
          for (const duplicate of duplicateStudents) {
            if (duplicate.personalInfo) {
              // Update primary student's personal info with any missing data
              const primaryPersonalInfo = primaryStudent.personalInfo;
              const duplicatePersonalInfo = duplicate.personalInfo;

              for (const [key, value] of Object.entries(duplicatePersonalInfo.toJSON())) {
                if (value && (!primaryPersonalInfo[key] || primaryPersonalInfo[key] === '')) {
                  primaryPersonalInfo[key] = value;
                }
              }

              await primaryPersonalInfo.save();
              mergedCount++;
            }

            // Delete the duplicate student and its personal info
            if (duplicate.personalInfo) {
              await duplicate.personalInfo.destroy();
            }
            await duplicate.destroy();
            deletedCount++;
          }
        }
      }

      this.logger.log(`Duplicate cleanup completed. Merged: ${mergedCount}, Deleted: ${deletedCount}`);
      return { merged: mergedCount, deleted: deletedCount };
    } catch (error) {
      this.logger.error('Error during duplicate cleanup:', error);
      throw error;
    }
  }

  // Helper methods
  private async generateStudentId(): Promise<string> {
    const year = new Date().getFullYear().toString().slice(-2);
    const randomNum = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `ST${year}${randomNum}`;
  }

  private async updateStudentGPA(studentId: string): Promise<void> {
    try {
      const grades = await this.gradeModel.findAll({
        where: { studentId: studentId }
      });

      const totalCreditsAttempted = grades.reduce(
        (sum, grade) => sum + grade.credits,
        0
      );
      const totalQualityPoints = grades.reduce(
        (sum, grade) => sum + grade.quality_points,
        0
      );

      const gpa =
        totalCreditsAttempted > 0
          ? totalQualityPoints / totalCreditsAttempted
          : 0;

      await this.studentModel.update(
        {
          gpa: Math.round(gpa * 100) / 100,
          total_credits: totalCreditsAttempted
        },
        { where: { studentId: studentId } }
      );
    } catch (error) {
      this.logger.error('Error updating student GPA:', error);
    }
  }

  private getAcademicStanding(gpa: number): string {
    if (gpa >= 3.5) return "Dean's List";
    if (gpa >= 3.0) return 'Good Standing';
    if (gpa >= 2.0) return 'Academic Warning';
    return 'Academic Probation';
  }

  private getSemesterBreakdown(grades: Grade[]): any[] {
    const semesterGroups = this.groupGradesBySemester(grades);
    return semesterGroups.map((group) => ({
      semester: group.semester,
      academic_year: group.academic_year,
      courses: group.grades.length,
      credits_attempted: group.grades.reduce((sum, g) => sum + g.credits, 0),
      credits_earned: group.grades
        .filter((g) => g.is_passing)
        .reduce((sum, g) => sum + g.credits, 0),
      semester_gpa:
        group.grades.length > 0
          ? Math.round(
              (group.grades.reduce((sum, g) => sum + g.quality_points, 0) /
                group.grades.reduce((sum, g) => sum + g.credits, 0)) *
                100
            ) / 100
          : 0
    }));
  }

  private groupGradesBySemester(grades: Grade[]): any[] {
    const groups: any = {};
    grades.forEach((grade) => {
      const key = `${grade.academic_year}-${grade.semester}`;
      if (!groups[key]) {
        groups[key] = {
          academic_year: grade.academic_year,
          semester: grade.semester,
          grades: []
        };
      }
      groups[key].grades.push(grade);
    });
    return Object.values(groups);
  }

  // Academic Background Operations
  async createOrUpdateStudentAcademic(
    academicData: any
  ): Promise<StudentAcademicBackground> {
    try {
      this.logger.log(
        'Received academic data:',
        JSON.stringify(academicData, null, 2)
      );

      // Handle both camelCase and snake_case field names for backward compatibility
      const studentId = academicData.studentId;

      if (!studentId) {
        throw new NotFoundException('Student ID is required');
      }

      this.logger.log('Using student ID:', studentId);

      const student = await this.studentModel.findOne({
        where: { studentId: studentId }
      });

      if (!student) {
        throw new NotFoundException(`Student not found with ID: ${studentId}`);
      }

      this.logger.log('Found student:', student.id);

      let academicBackground = await this.studentAcademicModel.findOne({
        where: { studentId: student.id }
      });

      this.logger.log(
        'Existing academic background:',
        academicBackground ? 'found' : 'not found'
      );

      // Handle both camelCase and snake_case field names for all academic data
      const updateData = {
        academicRecords: academicData.academic || [],
        proficiencyRecords: academicData.proficiency || [],
        publicationRecords: academicData.publications || [],
        otherActivities: academicData.otherActivities || []
      };

      this.logger.log('Update data:', JSON.stringify(updateData, null, 2));

      if (academicBackground) {
        // Update existing academic background
        await academicBackground.update(updateData);
        this.logger.log(`Updated academic background for student ${studentId}`);
      } else {
        // Create new academic background
        academicBackground = await this.studentAcademicModel.create({
          studentId: student.id,
          ...updateData
        });
        this.logger.log(`Created academic background for student ${studentId}`);
      }
      return academicBackground;
    } catch (error) {
      this.logger.error('Error creating/updating academic background:', error);
      throw error;
    }
  }

  async getStudentAcademic(
    studentId: string
  ): Promise<StudentAcademicBackground> {
    try {
      const student = await this.studentModel.findOne({
        where: { studentId: studentId }
      });
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const academicBackground = await this.studentAcademicModel.findOne({
        where: { studentId: student.id }
      });

      if (!academicBackground) {
        throw new NotFoundException('Academic background not found');
      }

      return academicBackground;
    } catch (error) {
      this.logger.error('Error getting student academic background:', error);
      throw error;
    }
  }

  // Check if student exists by email
  async checkStudentExistsByEmail(email: string): Promise<Student | null> {
    try {
      const personalInfo = await this.studentPersonalInfoModel.findOne({
        where: { email: email }
      });

      if (!personalInfo) {
        return null;
      }

      const student = await this.studentModel.findOne({
        where: { studentId: personalInfo.studentId },
        include: [
          {
            model: this.studentPersonalInfoModel,
            as: 'personalInfo',
            required: false
          },
          {
            model: this.studentAcademicModel,
            as: 'academicBackgrounds',
            required: false
          }
        ]
      });

      return student;
    } catch (error) {
      this.logger.error('Error checking student by email:', error);
      return null;
    }
  }

  // Get student by email
  async getStudentByEmail(email: string): Promise<Student | null> {
    try {
      // First, find all personal info records with this email
      const personalInfoRecords = await this.studentPersonalInfoModel.findAll({
        where: { email: email }
      });

      if (!personalInfoRecords || personalInfoRecords.length === 0) {
        return null;
      }

      // If multiple records exist, find the most complete one
      let bestStudent: Student | null = null;
      let maxFields = 0;

      for (const personalInfo of personalInfoRecords) {
        const student = await this.studentModel.findOne({
          where: { id: personalInfo.studentId },
          include: [
            {
              model: this.studentPersonalInfoModel,
              as: 'personalInfo',
              required: false
            },
            {
              model: this.studentAcademicModel,
              as: 'academicBackgrounds',
              required: false
            }
          ]
        });

        if (student) {
          // Count non-empty fields in personal info to determine completeness
          const personalInfoFields = Object.values(personalInfo.toJSON()).filter(
            value => value !== null && value !== undefined && value !== ''
          ).length;

          if (personalInfoFields > maxFields) {
            maxFields = personalInfoFields;
            bestStudent = student;
          }
        }
      }

      if (bestStudent) {
        this.logger.log(`Returning most complete student record for email ${email}: ${bestStudent.studentId}`);
      }

      return bestStudent;
    } catch (error) {
      this.logger.error('Error getting student by email:', error);
      return null;
    }
  }

  // University-related methods using university service
  async getUniversityDetails(universityId: number) {
    try {
      this.logger.log(`Fetching university details for ID: ${universityId}`);
      return await this.universityClientService.getUniversity(universityId);
    } catch (error) {
      this.logger.error(
        `Failed to get university details for ID ${universityId}:`,
        error
      );
      throw error;
    }
  }

  async getUniversitiesForStudent(studentId: string) {
    try {
      this.logger.log(`Fetching universities for student: ${studentId}`);
      const student = await this.getStudent(studentId);
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Get all universities (you can add filters based on student preferences)
      return await this.universityClientService.listUniversities();
    } catch (error) {
      this.logger.error(
        `Failed to get universities for student ${studentId}:`,
        error
      );
      throw error;
    }
  }

  async getCoursesForUniversity(universityId: number) {
    try {
      this.logger.log(`Fetching courses for university: ${universityId}`);
      return await this.universityClientService.listCourses({ universityId });
    } catch (error) {
      this.logger.error(
        `Failed to get courses for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  async getIntakesForUniversity(universityId: number) {
    try {
      this.logger.log(`Fetching intakes for university: ${universityId}`);
      return await this.universityClientService.getIntakesByUniversity(
        universityId
      );
    } catch (error) {
      this.logger.error(
        `Failed to get intakes for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  async getProgramLevelsForUniversity(universityId: number) {
    try {
      this.logger.log(
        `Fetching program levels for university: ${universityId}`
      );
      return await this.universityClientService.getProgramLevelsByUniversity(
        universityId
      );
    } catch (error) {
      this.logger.error(
        `Failed to get program levels for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  async getFieldsOfStudyForUniversity(universityId: number) {
    try {
      this.logger.log(
        `Fetching fields of study for university: ${universityId}`
      );
      return await this.universityClientService.getFieldsOfStudyByUniversity(
        universityId
      );
    } catch (error) {
      this.logger.error(
        `Failed to get fields of study for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  async getCampusesForUniversity(universityId: number) {
    try {
      this.logger.log(`Fetching campuses for university: ${universityId}`);
      return await this.universityClientService.getCampusesByUniversity(
        universityId
      );
    } catch (error) {
      this.logger.error(
        `Failed to get campuses for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  async getFeesForUniversity(universityId: number) {
    try {
      this.logger.log(`Fetching fees for university: ${universityId}`);
      return await this.universityClientService.getFeesByUniversity(
        universityId
      );
    } catch (error) {
      this.logger.error(
        `Failed to get fees for university ${universityId}:`,
        error
      );
      throw error;
    }
  }

  // Enhanced enrollment method that validates against university requirements
  async enrollInCourseWithValidation(
    studentId: string,
    courseId: string,
    semester: string,
    universityId: number
  ): Promise<Enrollment> {
    try {
      this.logger.log(
        `Enrolling student ${studentId} in course ${courseId} with validation`
      );

      // Get course details from university service
      const courseDetails = await this.universityClientService.getCourse(
        parseInt(courseId)
      );

      // Get student academic details
      const studentAcademic = await this.getStudentAcademic(studentId);

      // Validate student meets course requirements
      if (courseDetails && (courseDetails as any).course) {
        const course = (courseDetails as any).course;

        // Check GPA requirements
        if (course.minimumGpa && studentAcademic) {
          const studentGPA = parseFloat(course.minimumGpa);
          // Add GPA validation logic here
        }

        // Check language proficiency requirements
        if (course.testScores && studentAcademic.proficiencyRecords) {
          // Add language proficiency validation logic here
        }
      }

      // Proceed with enrollment if validation passes
      return await this.enrollInCourse(studentId, courseId, semester);
    } catch (error) {
      this.logger.error(
        `Failed to enroll student ${studentId} in course ${courseId} with validation:`,
        error
      );
      throw error;
    }
  }
}
