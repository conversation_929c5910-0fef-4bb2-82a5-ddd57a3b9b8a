import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
  Inject,
  forwardRef
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  Application,
  ApplicationStatus,
  PaymentStatus
} from './application.model';
import { BaseRepository } from '@apply-goal-backend/database';
import { ApplicationStageService } from './application-stage.service';
import {
  ApplicationDocumentRequirement,
  DocumentStatus
} from './application-document-requirement.model';
import { UniversityClientService } from '../university/university-client.service';
import { Student } from '../student/models/student.model';
import { StudentService } from '../student/student.service';
import { StudentDocumentsService } from '../student/services/student-documents.service';
import { StudentAcademicBackground } from '../student/models/student-academic.model';
import {
  ApplicationStage,
  ApplicationStageStatus
} from './application-stage.model';
import { ApplicationNote } from './application-note.model';
import {
  ApplicationProgressRecord,
  ProgressRecordStatus
} from './application-progress-record.model';

// Define DTOs locally for now since they're not exported from the shared dto library
export interface CreateApplicationDto {
  studentId: string;
  universityId: number;
  universityCountryId: number;
  universityCountryCampus: number;
  programId: number;
  intakeId: number;
  courseId: number;
  note?: string;
  status?: ApplicationStatus;
  paymentStatus?: PaymentStatus;
  applicationType?: string;
  // Enhanced fields
  applicationId?: string;
  currentStage?: string;
  overallProgress?: number;
  totalAmount?: number;
  paidAmount?: number;
  refundAmount?: number;
  deliveryMethod?: string;
  submissionDate?: Date;
  deadlineDate?: Date;
  // Payment-related fields
  paymentInvoiceUrl?: string;
  paymentMethod?: string;
  // ApplyGoal acceptance fields
  applicationAcceptedByApplyGoal?: boolean;
  applicationAcceptedByApplyGoalAt?: Date;
  applicationAcceptedByApplyGoalUserId?: number;
}

export interface UpdateApplicationDto {
  id?: number | bigint; // Add ID field to match proto message
  studentId?: string;
  universityId?: number;
  universityCountryId?: number;
  universityCountryCampus?: number;
  programId?: number;
  intakeId?: number;
  courseId?: number;
  note?: string;
  status?: ApplicationStatus;
  paymentStatus?: PaymentStatus;
  applicationType?: string;
  // Enhanced fields
  applicationId?: string;
  currentStage?: string;
  overallProgress?: number;
  totalAmount?: number;
  paidAmount?: number;
  refundAmount?: number;
  deliveryMethod?: string;
  submissionDate?: Date | string;
  deadlineDate?: Date | string;
  // Payment-related fields
  paymentInvoiceUrl?: string;
  paymentMethod?: string;
  // ApplyGoal acceptance fields
  applicationAcceptedByApplyGoal?: boolean;
  applicationAcceptedByApplyGoalAt?: Date | string;
  applicationAcceptedByApplyGoalUserId?: number;
  // Document generation flags
  isCalGenerated?: boolean;
  calUrl?: string;
  isAdmGenerated?: boolean;
  admUrl?: string;
  isApGenerated?: boolean;
  apUrl?: string;
  // Immigration document fields
  savisId?: number;
  i20Url?: string;
  i94Url?: string;
  i797cUrl?: string;
}

@Injectable()
export class ApplicationService {
  private applicationRepository: BaseRepository<Application>;
  private readonly logger = new Logger(ApplicationService.name);

  constructor(
    @InjectModel(Application)
    private applicationModel: typeof Application,
    @InjectModel(ApplicationDocumentRequirement)
    private applicationDocumentRequirementModel: typeof ApplicationDocumentRequirement,

    @InjectModel(ApplicationStage)
    private applicationStageModel: typeof ApplicationStage,
    @InjectModel(ApplicationNote)
    private applicationNoteModel: typeof ApplicationNote,
    @InjectModel(ApplicationProgressRecord)
    private applicationProgressRecordModel: typeof ApplicationProgressRecord,
    @InjectModel(Student)
    private studentModel: typeof Student,
    private readonly applicationStageService: ApplicationStageService,
    private readonly universityClientService: UniversityClientService,
    // Add this line to inject StudentAcademicBackground model
    @InjectModel(StudentAcademicBackground)
    private studentAcademicModel: typeof StudentAcademicBackground,
    // @Inject(forwardRef(() => StudentService))
    // private readonly studentService: StudentService,
    @Inject(forwardRef(() => StudentDocumentsService))
    private readonly studentDocumentsService: StudentDocumentsService
  ) {
    this.applicationRepository = new BaseRepository<Application>(
      applicationModel
    );
  }

  // async createApplication(
  //   createApplicationDto: CreateApplicationDto
  // ): Promise<Application> {
  //   try {
  //     this.logger.log(
  //       `Creating application for student: ${createApplicationDto.studentId}`
  //     );
  //     this.logger.log(
  //       'Incoming create data:',
  //       JSON.stringify(createApplicationDto, null, 2)
  //     );

  //     const application = await this.applicationRepository.create({
  //       ...createApplicationDto,
  //       appliedAt: new Date(),
  //       updatedAt: new Date(),
  //       deadlineDate: createApplicationDto.deadlineDate
  //         ? new Date(createApplicationDto.deadlineDate)
  //         : null,
  //       status: createApplicationDto.status || ApplicationStatus.APPLIED,
  //       currentStage:
  //         createApplicationDto.currentStage || 'collecting_documents',
  //       overallProgress: createApplicationDto.overallProgress || 0,
  //       paidAmount: createApplicationDto.paidAmount || 0,
  //       refundAmount: createApplicationDto.refundAmount || 0,
  //       deliveryMethod: createApplicationDto.deliveryMethod || 'online'
  //     });

  //     this.logger.log(
  //       `Application created successfully with ID: ${application.id}`
  //     );

  //     // Initialize application stages
  //     // await this.applicationStageService.initializeApplicationStages(Number(application.id));

  //     // Initialize document requirements
  //     await this.initializeDocumentRequirements(
  //       application,
  //       createApplicationDto
  //     );

  //     this.logger.log(
  //       `Application stages and document requirements initialized for application: ${application.id}`
  //     );

  //     return application;
  //   } catch (error) {
  //     this.logger.error(`Failed to create application: ${error.message}`);
  //     throw new ConflictException(
  //       `Failed to create application: ${error.message}`
  //     );
  //   }
  // }

  async createApplication(
    createApplicationDto: CreateApplicationDto
  ): Promise<Application> {
    try {
      this.logger.log(
        `Creating application for student: ${createApplicationDto.studentId}`
      );
      this.logger.log(
        'Incoming create data:',
        JSON.stringify(createApplicationDto, null, 2)
      );

      // Helper function to safely convert dates
      const safeDate = (dateInput: any): Date | null => {
        if (
          !dateInput ||
          dateInput === '' ||
          dateInput === null ||
          dateInput === undefined
        ) {
          return null;
        }

        if (typeof dateInput === 'string') {
          const date = new Date(dateInput);
          return isNaN(date.getTime()) ? null : date;
        }

        if (dateInput instanceof Date) {
          return isNaN(dateInput.getTime()) ? null : dateInput;
        }

        return null;
      };

      const application = await this.applicationRepository.create({
        ...createApplicationDto,
        // Date fields - properly validated
        appliedAt: new Date(),
        updatedAt: new Date(),
        deadlineDate: safeDate(createApplicationDto.deadlineDate),
        submissionDate: safeDate(createApplicationDto.submissionDate),
        applicationAcceptedByApplyGoalAt: safeDate(
          createApplicationDto.applicationAcceptedByApplyGoalAt
        ),

        // Default values with fallbacks
        status: createApplicationDto.status || ApplicationStatus.APPLIED,
        currentStage:
          createApplicationDto.currentStage || 'collecting_documents',
        overallProgress: createApplicationDto.overallProgress || 0,
        paidAmount: createApplicationDto.paidAmount || 0,
        refundAmount: createApplicationDto.refundAmount || 0,
        deliveryMethod: createApplicationDto.deliveryMethod || 'online',

        // Handle other potential empty strings
        applicationId: createApplicationDto.applicationId || null,
        paymentInvoiceUrl: createApplicationDto.paymentInvoiceUrl || null,
        paymentMethod: createApplicationDto.paymentMethod || null,
        note: createApplicationDto.note || '',

        // Ensure boolean fields
        applicationAcceptedByApplyGoal:
          createApplicationDto.applicationAcceptedByApplyGoal || false,

        // Handle numeric fields that might be 0
        applicationAcceptedByApplyGoalUserId:
          createApplicationDto.applicationAcceptedByApplyGoalUserId || null
      });

      this.logger.log(
        `Application created successfully with ID: ${application.id}`
      );

      // Initialize application stages
      // await this.applicationStageService.initializeApplicationStages(Number(application.id));

      // Initialize document requirements
      await this.initializeDocumentRequirements(
        application,
        createApplicationDto
      );

      this.logger.log(
        `Application stages and document requirements initialized for application: ${application.id}`
      );

      return application;
    } catch (error) {
      this.logger.error(`Failed to create application: ${error.message}`);
      this.logger.error('Error stack:', error.stack);

      // Log the specific data that caused the issue for debugging
      this.logger.error('Problematic data:', {
        submissionDate: createApplicationDto.submissionDate,
        deadlineDate: createApplicationDto.deadlineDate,
        applicationAcceptedByApplyGoalAt:
          createApplicationDto.applicationAcceptedByApplyGoalAt
      });

      throw new ConflictException(
        `Failed to create application: ${error.message}`
      );
    }
  }

  async findOne(id: number | bigint): Promise<Application> {
    this.logger.log(`Finding application with ID: ${id}, Type: ${typeof id}`);

    const application = await this.applicationRepository.findById(id);

    this.logger.log(`findById result: ${application ? 'Found' : 'Not found'}`);
    if (application) {
      this.logger.log(
        `Application ID from database: ${
          application.id
        }, Type: ${typeof application.id}`
      );
    }

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  /**
   * Find application with all related data needed for CAL document generation
   */
  async findOneWithRelatedData(id: number | bigint): Promise<any> {
    this.logger.log(`Finding application with related data for ID: ${id}`);

    // First, get the application
    const application = await this.applicationRepository.findById(id);
    this.logger.log(
      `Application found: ${JSON.stringify(application, null, 2)}`
    );

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    const applicaitonRequirementsWithGrouping =
      await this.getApplicationRequirementsWithGrouping(id);

    // Fetch related data separately
    const relatedData: any = {
      ...application.toJSON(),
      student: null,
      studentPersonalInfo: null,
      applicationRequirement: applicaitonRequirementsWithGrouping,
      course: null,
      program: null,
      intake: null,
      campus: null,
      university: null,
      fees: []
    };

    try {
      // Fetch student data
      const studentModel = this.applicationModel.sequelize.models.Student;
      if (studentModel) {
        const student = await studentModel.findOne({
          where: { studentId: application.studentId }
        });
        if (student) {
          relatedData.student = student.toJSON();

          // Fetch student personal information
          const studentPersonalInfoModel =
            this.applicationModel.sequelize.models.StudentPersonalInfo;
          if (studentPersonalInfoModel) {
            const personalInfo = await studentPersonalInfoModel.findOne({
              where: { studentId: (student as any).id }
            });
            if (personalInfo) {
              relatedData.studentPersonalInfo = personalInfo.toJSON();
            }
          }
        }
      }

      // Fetch comprehensive university data using the new comprehensive API
      try {
        if (
          application.universityId &&
          application.universityCountryCampus &&
          application.programId &&
          application.intakeId &&
          application.courseId
        ) {
          this.logger.log(
            'Fetching comprehensive university data for application'
          );

          const comprehensiveRequest = {
            universityId: application.universityId,
            campusId: application.universityCountryCampus,
            programId: application.programId,
            intakeId: application.intakeId,
            courseId: application.courseId
          };

          const comprehensiveResponse =
            await this.universityClientService.getComprehensiveUniversityInformation(
              comprehensiveRequest
            );
          this.logger.log(
            'Comprehensive university data response:',
            comprehensiveResponse
          );

          if (comprehensiveResponse && (comprehensiveResponse as any).data) {
            const data = (comprehensiveResponse as any).data;

            relatedData.university = data.university || null;
            relatedData.campus = data.campus || null;
            relatedData.program = data.programLevel || null;
            relatedData.intake = data.intake || null;
            relatedData.course = data.course || null;

            // Filter fees to match the intake name
            let filteredFee = null;
            if (
              data.fees &&
              data.fees.length > 0 &&
              data.intake &&
              data.intake.name
            ) {
              const intakeName = data.intake.name; // e.g., "Fall 1"

              // Find fee that contains the intake name in its title
              const matchingFee = data.fees.find(
                (fee: any) =>
                  fee.feeTitle &&
                  fee.feeTitle.toLowerCase().includes(intakeName.toLowerCase())
              );

              if (matchingFee) {
                filteredFee = matchingFee;
                this.logger.log(
                  `Found matching fee for intake "${intakeName}": ${matchingFee.feeTitle}`
                );
              } else {
                this.logger.warn(
                  `No matching fee found for intake "${intakeName}"`
                );
              }
            }

            relatedData.fees = filteredFee ? [filteredFee] : [];

            this.logger.log(
              'Successfully fetched comprehensive university data'
            );
          }
        }
      } catch (error) {
        this.logger.warn(
          `Error fetching comprehensive university data: ${error.message}`
        );
      }
    } catch (error) {
      this.logger.warn(`Error fetching related data: ${error.message}`);
      // Continue with application data even if related data fails
    }

    this.logger.log(`Found application with related data for ID: ${id}`);
    return relatedData;
  }

  async findByStudentId(studentId: string): Promise<Application[]> {
    const applications = await this.applicationRepository.findAll({
      where: { studentId }
    });

    return applications;
  }

  async update(
    id: number | bigint,
    updateApplicationDto: UpdateApplicationDto
  ): Promise<Application> {
    this.logger.log(`Updating application with ID: ${id}`);

    // Validate input
    if (!updateApplicationDto || typeof updateApplicationDto !== 'object') {
      throw new Error('Invalid update data provided');
    }

    // Find existing application to ensure it exists
    const existingApplication = await this.findOne(Number(id));
    if (!existingApplication) {
      throw new Error(`Application with ID ${id} not found`);
    }

    // Prepare update data - only include fields that are actually provided
    const updateData: any = {};

    // Add updatedAt timestamp
    updateData.updatedAt = new Date();

    // Process each field from the DTO
    Object.keys(updateApplicationDto).forEach((key) => {
      const value = (updateApplicationDto as any)[key];

      // Only include fields that have actual values (not undefined, null, or empty strings)
      if (value !== undefined && value !== null && value !== '') {
        // Handle date fields - convert strings to Date objects
        if (
          [
            'submissionDate',
            'deadlineDate',
            'applicationAcceptedByApplyGoalAt'
          ].includes(key)
        ) {
          if (typeof value === 'string') {
            updateData[key] = new Date(value);
          } else if (value instanceof Date) {
            updateData[key] = value;
          }
        } else {
          // For all other fields, include the value as-is
          updateData[key] = value;
        }
      }
    });

    this.logger.log(`Update data prepared:`, updateData);

    try {
      this.logger.log(`Starting update process for application ${id}`);
      this.logger.log(`Update data:`, JSON.stringify(updateData, null, 2));

      // Use the BaseRepository updateById method with proper type handling
      const [affectedCount] = await this.applicationRepository.updateById(
        Number(id),
        updateData
      );

      this.logger.log(`Update completed. Affected rows: ${affectedCount}`);

      if (affectedCount === 0) {
        throw new Error('No rows were updated');
      }

      // Return the updated application
      const updatedApplication = await this.findOne(Number(id));
      if (!updatedApplication) {
        throw new Error('Failed to retrieve updated application');
      }

      this.logger.log(
        `Application updated successfully. ID: ${id}, Affected rows: ${affectedCount}`
      );
      return updatedApplication;
    } catch (error) {
      this.logger.error(`Error updating application ${id}:`, error);
      this.logger.error(`Error stack:`, error.stack);
      throw new Error(`Failed to update application: ${error.message}`);
    }
  }

  async remove(id: number | bigint): Promise<void> {
    const application = await this.findOne(Number(id));
    await this.applicationRepository.delete(Number(id));
  }

  async findAll({
    page = 1,
    limit = 10,
    studentId,
    status,
    paymentStatus,
    applicationType,
    currentStage,
    applicationId,
    universityId,
    programId,
    intakeId,
    courseId,
    campusId,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  }: {
    page?: number;
    limit?: number;
    studentId?: string;
    status?: ApplicationStatus;
    paymentStatus?: PaymentStatus;
    applicationType?: string;
    currentStage?: string;
    applicationId?: string;
    universityId?: number;
    programId?: number;
    intakeId?: number;
    courseId?: number;
    campusId?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ rows: Application[]; count: number }> {
    const where: any = {};

    if (studentId) {
      where.studentId = studentId;
    }

    if (status) {
      where.status = status;
    }

    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }

    if (applicationType) {
      where.applicationType = applicationType;
    }

    if (currentStage) {
      where.currentStage = currentStage;
    }

    if (applicationId) {
      where.applicationId = applicationId;
    }

    if (universityId) where.universityId = universityId;
    if (programId) where.programId = programId;
    if (intakeId) where.intakeId = intakeId;
    if (courseId) where.courseId = courseId;
    if (campusId) where.universityCountryCampus = campusId;

    const result = await this.applicationRepository.findAndCountAll({
      where,
      limit,
      offset: (page - 1) * limit,
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    return result;
  }

  /**
   * Find all applications with mapped data (actual names instead of IDs)
   */
  async findAllWithMappedData({
    page = 1,
    limit = 10,
    studentId,
    status,
    paymentStatus,
    applicationType,
    currentStage,
    applicationId,
    universityId,
    programId,
    intakeId,
    courseId,
    campusId,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  }: {
    page?: number;
    limit?: number;
    studentId?: string;
    status?: ApplicationStatus;
    paymentStatus?: PaymentStatus;
    applicationType?: string;
    currentStage?: string;
    applicationId?: string;
    universityId?: number;
    programId?: number;
    intakeId?: number;
    courseId?: number;
    campusId?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ rows: any[]; count: number }> {
    try {
      // First get the basic application data
      const result = await this.findAll({
        page,
        limit,
        studentId,
        status,
        paymentStatus,
        applicationType,
        currentStage,
        applicationId,
        universityId,
        programId,
        intakeId,
        courseId,
        campusId,
        sortBy,
        sortOrder
      });

      // Map the applications with actual names
      const mappedRows = await Promise.all(
        result.rows.map(async (app) => {
          const mappedApp = {
            id: app.id,
            studentId: app.studentId,
            universityId: app.universityId,
            universityCountryId: app.universityCountryId,
            universityCountryCampus: app.universityCountryCampus,
            programId: app.programId,
            intakeId: app.intakeId,
            courseId: app.courseId,
            note: app.note,
            status: app.status,
            paymentStatus: app.paymentStatus,
            applicationType: app.applicationType,
            // Enhanced fields
            applicationId: app.applicationId,
            currentStage: app.currentStage,
            overallProgress: app.overallProgress,
            totalAmount: app.totalAmount,
            paidAmount: app.paidAmount,
            refundAmount: app.refundAmount,
            deliveryMethod: app.deliveryMethod,
            submissionDate: this.safeDateToISOString(app.submissionDate),
            deadlineDate: this.safeDateToISOString(app.deadlineDate),
            appliedAt: this.safeDateToISOString(app.appliedAt),
            updatedAt: this.safeDateToISOString(app.updatedAt),
            createdAt: this.safeDateToISOString(app.createdAt),
            // ApplyGoal acceptance fields
            applicationAcceptedByApplyGoal: app.applicationAcceptedByApplyGoal,
            applicationAcceptedByApplyGoalAt: this.safeDateToISOString(
              app.applicationAcceptedByApplyGoalAt
            ),
            applicationAcceptedByApplyGoalUserId:
              app.applicationAcceptedByApplyGoalUserId,
            // Immigration document fields
            savisId: app.savisId,
            i20Url: app.i20Url,
            i94Url: app.i94Url,
            i797cUrl: app.i797cUrl,
            // Mapped names (will be populated below)
            universityName: '',
            countryName: '',
            campusName: '',
            programLevelName: '',
            intakeName: '',
            courseName: ''
          };

          try {
            // Get university details
            if (app.universityId) {
              const universityResponse =
                await this.universityClientService.getUniversity(
                  app.universityId
                );
              if (
                universityResponse &&
                (universityResponse as any).status === 200
              ) {
                mappedApp.universityName =
                  (universityResponse as any).data?.title || '';
              }
            }

            // Get country details - using the university service structure
            if (app.universityCountryId) {
              // For now, we'll use a placeholder since getCountry method doesn't exist
              mappedApp.countryName = `Country ID: ${app.universityCountryId}`;
            }

            // Get campus details - using the university service structure
            if (app.universityCountryCampus) {
              // For now, we'll use a placeholder since getCampus method doesn't exist
              mappedApp.campusName = `Campus ID: ${app.universityCountryCampus}`;
            }

            // Get program level details - using the university service structure
            if (app.programId) {
              // For now, we'll use a placeholder since getProgramLevel method doesn't exist
              mappedApp.programLevelName = `Program Level ID: ${app.programId}`;
            }

            // Get intake details - using the university service structure
            if (app.intakeId) {
              // For now, we'll use a placeholder since getIntake method doesn't exist
              mappedApp.intakeName = `Intake ID: ${app.intakeId}`;
            }

            // Get course details
            if (app.courseId) {
              const courseResponse =
                await this.universityClientService.getCourse(app.courseId);
              if (courseResponse && (courseResponse as any).status === 200) {
                mappedApp.courseName =
                  (courseResponse as any).data?.title || '';
              }
            }
          } catch (error) {
            this.logger.warn(
              `Error fetching mapped data for application ${app.id}: ${error.message}`
            );
            // Continue with empty names if mapping fails
          }

          return mappedApp;
        })
      );

      return {
        rows: mappedRows,
        count: result.count
      };
    } catch (error) {
      this.logger.error(`Error in findAllWithMappedData: ${error.message}`);
      throw error;
    }
  }

  /**
   * Safe date to ISO string conversion
   */
  private safeDateToISOString(date: any): string | null {
    if (!date) {
      return null;
    }

    // If it's already a string, return as is
    if (typeof date === 'string') {
      return date;
    }

    // If it's a Date object, convert to ISO string
    if (date instanceof Date) {
      return date.toISOString();
    }

    // If it's a number (timestamp), convert to Date then ISO string
    if (typeof date === 'number') {
      return new Date(date).toISOString();
    }

    // For any other type, try to convert to Date
    try {
      const dateObj = new Date(date);
      if (!isNaN(dateObj.getTime())) {
        return dateObj.toISOString();
      }
    } catch (error) {
      this.logger.warn(`Failed to convert date: ${date}`);
    }

    return null;
  }

  async createApplicationRequirements(
    applicationId: number | bigint,
    items: Array<{
      documentTitle: string;
      documentName: string;
      url?: string;
      documentDescription?: string;
      isRequired?: boolean;
      allowedFormats?: string[];
      requiredByDate?: string;
    }>,
    studentId?: string
  ) {
    // Check application existence
    const appStringId = applicationId.toString();
    const application = await this.applicationRepository.findById(appStringId);
    if (!application) {
      return {
        status: 404,
        message: `Application with ID ${applicationId} not found`,
        data: []
      };
    }

    // Validate optional studentId
    if (
      studentId &&
      application.studentId &&
      studentId !== application.studentId
    ) {
      return {
        status: 400,
        message: `Provided studentId (${studentId}) does not match application's studentId (${application.studentId})`,
        data: []
      };
    }

    const effectiveStudentId = studentId || application.studentId || null;

    const requirementsToCreate = items.map((item) => ({
      applicationId,
      studentId: effectiveStudentId,
      documentTitle: item.documentTitle,
      documentName: item.documentName,
      url: item.url || null,
      documentDescription: item.documentDescription || '',
      isRequired: item.isRequired ?? true,
      allowedFormats:
        item.allowedFormats && item.allowedFormats.length > 0
          ? item.allowedFormats
          : ['pdf', 'jpg', 'png'],
      documentStatus: 'requested',
      requiredByDate: item.requiredByDate ? new Date(item.requiredByDate) : null
    }));

    const created = await this.applicationDocumentRequirementModel.bulkCreate(
      requirementsToCreate
    );

    return {
      status: 200,
      message: 'Application requirements created successfully',
      data: created.map((req) => ({
        id: req.id,
        applicationId: req.applicationId,
        studentId: req.studentId,
        documentTitle: req.documentTitle,
        documentName: req.documentName,
        url: req.url,
        documentDescription: req.documentDescription,
        isRequired: req.isRequired,
        allowedFormats: req.allowedFormats || [],
        documentStatus: req.documentStatus,
        requiredByDate: req.requiredByDate
          ? new Date(req.requiredByDate).toISOString()
          : null,
        uploadedAt: req.uploadedAt ? req.uploadedAt.toISOString() : null,
        verifiedAt: req.verifiedAt ? req.verifiedAt.toISOString() : null,
        verifiedBy: req.verifiedBy || null,
        acceptedByApplyGoal: this.convertToGrpcBoolValue(
          req.acceptedByApplyGoal
        ),
        acceptedByApplyGoalUserId: req.acceptedByApplyGoalUserId || null,
        acceptedByUniversity: req.acceptedByUniversity || false,
        acceptedByUniversityUserId: req.acceptedByUniversityUserId || null,
        rejectionReason: req.rejectionReason || null,
        createdAt: req.createdAt.toISOString(),
        updatedAt: req.updatedAt.toISOString()
      }))
    };
  }

  async getApplicationProgress(id: number | bigint): Promise<{
    application: Application;
    progress: {
      totalStages: number;
      completedStages: number;
      overallProgress: number;
      currentStage: string;
      stages: any[];
    };
  }> {
    const application = await this.findOne(id);
    const progress = await this.applicationStageService.getApplicationProgress(
      Number(application.id)
    );

    return {
      application,
      progress
    };
  }

  /**
   * Initialize document requirements for a new application
   */
  // private async initializeDocumentRequirements(
  //   application: Application,
  //   createApplicationDto: CreateApplicationDto
  // ) {
  //   try {
  //     this.logger.log(
  //       `Initializing document requirements for application ${application.id}`
  //     );

  //     // Create basic document requirements without trying to fetch student data
  //     // These will be updated when documents are actually uploaded
  //     const basicRequirements = [
  //       {
  //         applicationId: Number(application.id),
  //         studentId: createApplicationDto.studentId,
  //         documentTitle: 'Profile Documents',
  //         documentName: 'Profile Photo',
  //         url: null,
  //         documentDescription: 'Student profile photograph',
  //         isRequired: true,
  //         allowedFormats: ['pdf', 'jpg', 'png'],
  //         documentStatus: DocumentStatus.REQUESTED,
  //         requiredByDate: null,
  //         uploadedAt: null,
  //         verifiedAt: null,
  //         verifiedBy: null,
  //         acceptedByApplyGoal: false,
  //         acceptedByApplyGoalUserId: null,
  //         acceptedByUniversity: false,
  //         acceptedByUniversityUserId: null,
  //         rejectionReason: null
  //       },
  //       {
  //         applicationId: Number(application.id),
  //         studentId: createApplicationDto.studentId,
  //         documentTitle: 'Profile Documents',
  //         documentName: 'Passport Copy',
  //         url: null,
  //         documentDescription: 'Valid passport copy',
  //         isRequired: true,
  //         allowedFormats: ['pdf', 'jpg', 'png'],
  //         documentStatus: DocumentStatus.REQUESTED,
  //         requiredByDate: null,
  //         uploadedAt: null,
  //         verifiedAt: null,
  //         verifiedBy: null,
  //         acceptedByApplyGoal: false,
  //         acceptedByApplyGoalUserId: null,
  //         acceptedByUniversity: false,
  //         acceptedByUniversityUserId: null,
  //         rejectionReason: null
  //       }
  //     ];

  //     // Create the basic requirements
  //     await this.applicationDocumentRequirementModel.bulkCreate(
  //       basicRequirements
  //     );

  //     this.logger.log(
  //       `Basic document requirements initialized for application ${application.id}`
  //     );
  //   } catch (error) {
  //     this.logger.error(
  //       `Failed to initialize document requirements for application ${application.id}: ${error.message}`
  //     );
  //     // Don't throw error to avoid blocking application creation
  //   }
  // }

  async getApplicationRequirements(applicationId: number | bigint) {
    try {
      this.logger.log(
        `Getting application requirements and stages for application: ${applicationId}`
      );

      // Get application requirements
      const requirements =
        await this.applicationDocumentRequirementModel.findAll({
          where: { applicationId },
          order: [['createdAt', 'ASC']]
        });

      // Get application stages sorted by stageOrder
      const stages = await this.applicationStageModel.findAll({
        where: { applicationId },
        order: [['stageOrder', 'ASC']]
      });

      console.log('+++++++++++++++++++++++=', requirements);
      console.log('Application stages:', stages);

      const applicationRequirements = requirements.map((req) => ({
        id: req.id,
        applicationId: req.applicationId,
        documentTitle: req.documentTitle,
        documentName: req.documentName,
        url: req.url,
        documentDescription: req.documentDescription,
        isRequired: req.isRequired,
        allowedFormats: req.allowedFormats || [],
        documentStatus: req.documentStatus,
        requiredByDate: req.requiredByDate
          ? new Date(req.requiredByDate).toISOString()
          : null,
        uploadedAt: req.uploadedAt ? req.uploadedAt.toISOString() : null,
        verifiedAt: req.verifiedAt ? req.verifiedAt.toISOString() : null,
        verifiedBy: req.verifiedBy || null,
        acceptedByApplyGoal: this.convertToGrpcBoolValue(
          req.acceptedByApplyGoal
        ),
        acceptedByApplyGoalUserId: req.acceptedByApplyGoalUserId || null,
        acceptedByUniversity: req.acceptedByUniversity || false,
        acceptedByUniversityUserId: req.acceptedByUniversityUserId || null,
        rejectionReason: req.rejectionReason || null,
        createdAt: req.createdAt.toISOString(),
        updatedAt: req.updatedAt.toISOString()
      }));

      const applicationStages = stages.map((stage) => ({
        id: stage.id,
        applicationId: stage.applicationId,
        stageName: stage.stageName,
        stageOrder: stage.stageOrder,
        status: stage.status,
        progressPercentage: stage.progressPercentage,
        startDate: stage.startDate ? stage.startDate.toISOString() : null,
        completedDate: stage.completedDate
          ? stage.completedDate.toISOString()
          : null,
        requirementsMet: stage.requirementsMet,
        totalRequirements: stage.totalRequirements,
        createdAt: stage.createdAt.toISOString(),
        updatedAt: stage.updatedAt.toISOString()
      }));

      return {
        status: 200,
        message: 'Application requirements and stages retrieved successfully',
        data: {
          applicationRequirements,
          applicationStages
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to get application requirements and stages: ${error.message}`
      );
      throw error;
    }
  }
  private async getApplicationRequirementsWithGrouping(
    applicationId: number | bigint
  ): Promise<Record<string, any[]>> {
    try {
      this.logger.log(
        `Fetching application requirements for application: ${applicationId}`
      );

      // Get application requirements
      const requirements =
        await this.applicationDocumentRequirementModel.findAll({
          where: { applicationId },
          order: [['createdAt', 'ASC']]
        });

      this.logger.log(`Found ${requirements.length} application requirements`);

      // ✅ Get application data to find studentId
      const application = await this.applicationRepository.findById(applicationId);
      let studentProfileUrl = null;

      if (application) {
        try {
          // ✅ Fetch student profile document data directly using application.studentId
          const studentDocumentModel = this.applicationModel.sequelize.models.StudentDocument;
          if (studentDocumentModel) {
            const profileDocument = await studentDocumentModel.findOne({
              where: {
                studentId: application.studentId, // Use application.studentId directly (e.g., "ST2025003")
                isActive: true,
                field: 'profile'
              },
              order: [['createdAt', 'DESC']],
              raw: true
            });

            if (profileDocument && (profileDocument as any).url) {
              studentProfileUrl = (profileDocument as any).url;
              this.logger.log(`Found student profile document URL for application ${applicationId}: ${studentProfileUrl}`);
            } else {
              this.logger.log(`No profile document found for student ID: ${application.studentId}`);
            }
          }
        } catch (error) {
          this.logger.warn(`Error fetching student profile document: ${error.message}`);
        }
      }

      // ✅ NEW: Group requirements by documentTitle
      const groupedRequirements = requirements.reduce((groups, req) => {
        const documentTitle = req.documentTitle || 'other';

        if (!groups[documentTitle]) {
          groups[documentTitle] = [];
        }

        // Transform requirement data
        const requirementData = {
          id: req.id,
          applicationId: req.applicationId,
          studentId: req.studentId,
          documentTitle: req.documentTitle,
          documentName: req.documentName,
          url: req.url,
          documentDescription: req.documentDescription,
          isRequired: req.isRequired,
          allowedFormats: req.allowedFormats || [],
          documentStatus: req.documentStatus,
          requiredByDate: req.requiredByDate
            ? req.requiredByDate.toISOString()
            : null,
          uploadedAt: req.uploadedAt ? req.uploadedAt.toISOString() : null,
          verifiedAt: req.verifiedAt ? req.verifiedAt.toISOString() : null,
          verifiedBy: req.verifiedBy || null,
          acceptedByApplyGoal: req.acceptedByApplyGoal,
          acceptedByApplyGoalUserId: req.acceptedByApplyGoalUserId || null,
          acceptedByUniversity: req.acceptedByUniversity || false,
          acceptedByUniversityUserId: req.acceptedByUniversityUserId || null,
          rejectionReason: req.rejectionReason || null,
          createdAt: req.createdAt.toISOString(),
          updatedAt: req.updatedAt.toISOString()
        };

        groups[documentTitle].push(requirementData);
        return groups;
      }, {} as Record<string, any[]>);

      // ✅ Add student profile document to identity group if URL exists
      if (studentProfileUrl) {
        // Ensure identity group exists
        if (!groupedRequirements.identity) {
          groupedRequirements.identity = [];
        }

        // Check if profile requirement already exists
        const existingProfileReq = groupedRequirements.identity.find(
          (req: any) => req.documentName && req.documentName.toLowerCase() === 'profile'
        );

        if (existingProfileReq) {
          // Update existing profile requirement with URL
          existingProfileReq.url = studentProfileUrl;
          this.logger.log(`Updated existing profile requirement with URL: ${studentProfileUrl}`);
        } else {
          // Add new profile requirement to identity group
          const profileRequirement = {
            id: null,
            applicationId: applicationId,
            studentId: application.studentId, // Use the correct studentId from application
            documentTitle: 'identity',
            documentName: 'profile',
            url: studentProfileUrl,
            documentDescription: 'Student Profile Photo',
            isRequired: true,
            allowedFormats: ['jpg', 'jpeg', 'png'],
            documentStatus: 'uploaded',
            requiredByDate: null,
            uploadedAt: new Date().toISOString(),
            verifiedAt: null,
            verifiedBy: null,
            acceptedByApplyGoal: false,
            acceptedByApplyGoalUserId: null,
            acceptedByUniversity: false,
            acceptedByUniversityUserId: null,
            rejectionReason: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          groupedRequirements.identity.push(profileRequirement);
          this.logger.log(`Added profile requirement to identity group with URL: ${studentProfileUrl}`);
        }
      }

      this.logger.log(
        `Grouped requirements by documentTitle:`,
        Object.keys(groupedRequirements)
      );
      this.logger.debug("Docuemnt Requirmeent  final  list ============================>",groupedRequirements);

      return groupedRequirements;
    } catch (error) {
      this.logger.error(
        `Error fetching application requirements: ${error.message}`
      );
      return {};
    }
  }
  async updateApplicationStage(
    applicationId: number | bigint,
    stageId: number | bigint,
    status: string
  ) {
    try {
      this.logger.log(
        `Updating application stage ${stageId} for application ${applicationId} to status: ${status}`
      );

      // Validate status
      const validStatuses = ['pending', 'in_progress', 'completed', 'failed'];
      if (!validStatuses.includes(status)) {
        return {
          status: 400,
          message: `Invalid status. Must be one of: ${validStatuses.join(
            ', '
          )}`,
          data: null,
          error: { details: 'Invalid status value' }
        };
      }

      // Find the stage
      const stage = await this.applicationStageModel.findOne({
        where: {
          id: stageId,
          applicationId: applicationId
        }
      });

      if (!stage) {
        return {
          status: 404,
          message: 'Application stage not found',
          data: null,
          error: { details: 'Stage not found' }
        };
      }

      // Update the stage
      const updateData: any = { status };

      // If status is completed, set completedDate
      if (status === 'completed') {
        updateData.completedDate = new Date();
        updateData.progressPercentage = 100;
      }

      // If status is in_progress, set startDate if not already set
      if (status === 'in_progress' && !stage.startDate) {
        updateData.startDate = new Date();
      }

      await stage.update(updateData);

      // Return the updated stage
      return {
        status: 200,
        message: 'Application stage updated successfully',
        data: {
          id: stage.id,
          applicationId: stage.applicationId,
          stageName: stage.stageName,
          stageOrder: stage.stageOrder,
          status: stage.status,
          progressPercentage: stage.progressPercentage,
          startDate: stage.startDate ? stage.startDate.toISOString() : null,
          completedDate: stage.completedDate
            ? stage.completedDate.toISOString()
            : null,
          requirementsMet: stage.requirementsMet,
          totalRequirements: stage.totalRequirements,
          createdAt: stage.createdAt.toISOString(),
          updatedAt: stage.updatedAt.toISOString()
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to update application stage: ${error.message}`);
      return {
        status: 500,
        message: 'Failed to update application stage',
        data: null,
        error: { details: error.message }
      };
    }
  }

  async createApplicationNote(
    applicationId: number | bigint,
    noteData: {
      noteType: string;
      title?: string;
      content: string;
      createdBy: number;
      isPrivate?: boolean;
      userInfo?: any;
      replyId?: number | null;
    }
  ) {
    try {
      this.logger.log(
        `Creating application note for application: ${applicationId}`
      );

      // Validate noteType
      const validNoteTypes = ['internal', 'student', 'university', 'applygoal'];
      if (!validNoteTypes.includes(noteData.noteType)) {
        return {
          status: 400,
          message: `Invalid noteType. Must be one of: ${validNoteTypes.join(
            ', '
          )}`,
          data: null,
          error: { details: 'Invalid noteType value' }
        };
      }

      // Check if application exists
      const application = await this.applicationModel.findByPk(applicationId);
      if (!application) {
        return {
          status: 404,
          message: 'Application not found',
          data: null,
          error: { details: 'Application not found' }
        };
      }

      // If replyId is provided, validate that the parent note exists
      if (noteData.replyId) {
        const parentNote = await this.applicationNoteModel.findByPk(
          noteData.replyId
        );
        if (parentNote) {
          this.logger.log(
            `Found parent note for reply: ${JSON.stringify(parentNote)}`
          );
        }
        if (!parentNote) {
          return {
            status: 404,
            message: 'Parent note not found',
            data: null,
            error: {
              details: 'Parent note with the specified replyId does not exist'
            }
          };
        }

        // Ensure the parent note belongs to the same application
        if (Number(parentNote.applicationId) !== Number(applicationId)) {
          return {
            status: 400,
            message: 'Parent note belongs to a different application',
            data: null,
            error: {
              details:
                'Reply can only be created for notes in the same application'
            }
          };
        }
      }

      // Create the note
      const note = await this.applicationNoteModel.create({
        applicationId: applicationId,
        noteType: noteData.noteType,
        title: noteData.title || '',
        content: noteData.content,
        createdBy: noteData.createdBy,
        isPrivate: noteData.isPrivate || false,
        userInfo: noteData.userInfo || null,
        replyId: noteData.replyId || null
      });

      // Track the note creation
      await this.trackApplicationProgress(
        applicationId,
        'note_create',
        `Note Created: ${noteData.title || 'Untitled'}`,
        `${noteData.noteType} note created: ${noteData.content.substring(
          0,
          100
        )}${noteData.content.length > 100 ? '...' : ''}`,
        {
          status: 'completed',
          createdBy: noteData.createdBy,
          applicationStage: 'Communication',
          applicationStep: `${noteData.noteType} Note`
        }
      );

      return {
        status: 201,
        message: 'Application note created successfully',
        data: {
          id: note.id,
          applicationId: note.applicationId,
          noteType: note.noteType,
          title: note.title,
          content: note.content,
          createdBy: note.createdBy,
          isPrivate: note.isPrivate,
          userInfo: note.userInfo,
          replyId: note.replyId,
          createdAt: note.createdAt.toISOString(),
          updatedAt: note.updatedAt.toISOString()
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to create application note: ${error.message}`);
      return {
        status: 500,
        message: 'Failed to create application note',
        data: null,
        error: { details: error.message }
      };
    }
  }

  async getApplicationNotes(applicationId: number | bigint, noteType?: string) {
    try {
      this.logger.log(
        `Getting application notes for application: ${applicationId}${
          noteType ? `, noteType: ${noteType}` : ''
        }`
      );

      // Build where clause
      const whereClause: any = { applicationId };
      if (noteType) {
        // Validate noteType if provided
        const validNoteTypes = ['internal', 'student', 'university'];
        if (!validNoteTypes.includes(noteType)) {
          return {
            status: 400,
            message: `Invalid noteType. Must be one of: ${validNoteTypes.join(
              ', '
            )}`,
            data: null,
            error: { details: 'Invalid noteType value' }
          };
        }
        whereClause.noteType = noteType;
      }

      // Get all notes with their parent note information
      const notes = await this.applicationNoteModel.findAll({
        where: whereClause,
        include: [
          {
            model: this.applicationNoteModel,
            as: 'parentNote',
            required: false,
            attributes: ['id', 'title', 'content', 'createdBy', 'createdAt']
          }
        ],
        order: [['createdAt', 'DESC']]
      });
      this.logger.log('notes from model', JSON.stringify(notes));
      // Format notes and organize them hierarchically
      const formattedNotes = notes.map((note) => ({
        id: note.id,
        applicationId: note.applicationId,
        noteType: note.noteType,
        title: note.title,
        content: note.content,
        createdBy: note.createdBy,
        isPrivate: note.isPrivate,
        userInfo: note.userInfo,
        replyId: note.replyId,
        parentNote: note.parentNote
          ? {
              id: note.parentNote.id,
              title: note.parentNote.title,
              content:
                note.parentNote.content.substring(0, 100) +
                (note.parentNote.content.length > 100 ? '...' : ''),
              createdBy: note.parentNote.createdBy,
              createdAt: note.parentNote.createdAt.toISOString()
            }
          : null,
        createdAt: note.createdAt.toISOString(),
        updatedAt: note.updatedAt.toISOString()
      }));

      return {
        status: 200,
        message: 'Application notes retrieved successfully',
        data: {
          notes: formattedNotes,
          total: formattedNotes.length
        },
        error: null
      };
    } catch (error) {
      this.logger.error(`Failed to get application notes: ${error.message}`);
      return {
        status: 500,
        message: 'Failed to get application notes',
        data: null,
        error: { details: error.message }
      };
    }
  }

  // ==================== APPLICATION PROGRESS RECORDS ====================

  async getApplicationProgressRecords(
    applicationId: number | bigint,
    recordType?: string
  ) {
    try {
      this.logger.log(
        `Getting application progress records for application: ${applicationId}`
      );

      // Build where clause with filters
      const whereClause: any = { applicationId };
      if (recordType) whereClause.recordType = recordType;

      // Get progress records
      const records = await this.applicationProgressRecordModel.findAll({
        where: whereClause,
        order: [
          ['recordDate', 'DESC'],
          ['createdAt', 'DESC']
        ]
      });

      const formattedRecords = records.map((record) => ({
        id: record.id,
        applicationId: record.applicationId,
        recordType: record.recordType,
        title: record.title,
        description: record.description,
        status: record.status,
        recordDate: record.recordDate ? record.recordDate.toISOString() : null,
        amount: record.amount || 0,
        currency: record.currency || 'USD',
        proofLinks: this.formatJsonArray(record.proofLinks),
        attachments: this.formatJsonArray(record.attachments),
        createdBy: record.createdBy,
        createdAt: record.createdAt.toISOString(),
        updatedAt: record.updatedAt.toISOString()
      }));

      return {
        status: 200,
        message: 'Application progress records retrieved successfully',
        data: {
          records: formattedRecords,
          total: formattedRecords.length
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to get application progress records: ${error.message}`
      );
      return {
        status: 500,
        message: 'Failed to get application progress records',
        data: null,
        error: { details: error.message }
      };
    }
  }

  async createApplicationProgressRecord(
    applicationId: number | bigint,
    recordData: {
      recordType: string;
      title: string;
      description: string;
      status?: string;
      recordDate?: Date | string;
      amount?: number;
      currency?: string;
      proofLinks?: any[];
      attachments?: any[];
      createdBy: number;
      applicationStage?: string;
      applicationStep?: string;
    }
  ) {
    try {
      this.logger.log(
        `Creating application progress record for application: ${applicationId}, type: ${recordData.recordType}`
      );

      // Validate status
      const validStatuses = ['completed', 'in_progress', 'failed'];
      const status = recordData.status || 'completed';
      if (!validStatuses.includes(status)) {
        return {
          status: 400,
          message: `Invalid status. Must be one of: ${validStatuses.join(
            ', '
          )}`,
          data: null,
          error: { details: 'Invalid status value' }
        };
      }

      // Check if application exists
      const application = await this.applicationModel.findByPk(applicationId);
      if (!application) {
        return {
          status: 404,
          message: 'Application not found',
          data: null,
          error: { details: 'Application not found' }
        };
      }

      // Parse recordDate
      let recordDate = new Date();
      if (recordData.recordDate) {
        recordDate =
          typeof recordData.recordDate === 'string'
            ? new Date(recordData.recordDate)
            : recordData.recordDate;
      }

      // Create enhanced description with stage/step info
      let enhancedDescription = recordData.description;
      if (recordData.applicationStage || recordData.applicationStep) {
        const stageInfo = [];
        if (recordData.applicationStage)
          stageInfo.push(`Stage: ${recordData.applicationStage}`);
        if (recordData.applicationStep)
          stageInfo.push(`Step: ${recordData.applicationStep}`);
        enhancedDescription = `${recordData.description} [${stageInfo.join(
          ', '
        )}]`;
      }

      // Format arrays as JSON with metadata
      const proofLinks = this.formatJsonArrayWithMetadata(
        recordData.proofLinks
      );
      const attachments = this.formatJsonArrayWithMetadata(
        recordData.attachments
      );

      // Create the progress record
      const record = await this.applicationProgressRecordModel.create({
        applicationId: applicationId,
        recordType: recordData.recordType,
        title: recordData.title,
        description: enhancedDescription,
        status: status,
        recordDate: recordDate,
        amount: recordData.amount || null,
        currency: recordData.currency || 'USD',
        proofLinks: proofLinks,
        attachments: attachments,
        createdBy: recordData.createdBy
      });

      return {
        status: 201,
        message: 'Application progress record created successfully',
        data: {
          id: record.id,
          applicationId: record.applicationId,
          recordType: record.recordType,
          title: record.title,
          description: record.description,
          status: record.status,
          recordDate: record.recordDate
            ? record.recordDate.toISOString()
            : null,
          amount: record.amount || 0,
          currency: record.currency,
          proofLinks: this.formatJsonArray(record.proofLinks),
          attachments: this.formatJsonArray(record.attachments),
          createdBy: record.createdBy,
          createdAt: record.createdAt.toISOString(),
          updatedAt: record.updatedAt.toISOString()
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to create application progress record: ${error.message}`
      );
      return {
        status: 500,
        message: 'Failed to create application progress record',
        data: null,
        error: { details: error.message }
      };
    }
  }

  // Universal tracking method - can be called from anywhere
  async trackApplicationProgress(
    applicationId: number | bigint,
    recordType: string,
    title: string,
    description: string,
    options: {
      status?: 'completed' | 'in_progress' | 'failed';
      amount?: number;
      currency?: string;
      proofLinks?: any[];
      attachments?: any[];
      createdBy: number;
      applicationStage?: string;
      applicationStep?: string;
    }
  ) {
    try {
      this.logger.log(
        `Tracking application progress: ${applicationId} - ${recordType} - ${title}`
      );

      return await this.createApplicationProgressRecord(applicationId, {
        recordType,
        title,
        description,
        status: options.status || 'completed',
        recordDate: new Date(),
        amount: options.amount,
        currency: options.currency,
        proofLinks: options.proofLinks,
        attachments: options.attachments,
        createdBy: options.createdBy,
        applicationStage: options.applicationStage,
        applicationStep: options.applicationStep
      });
    } catch (error) {
      this.logger.error(
        `Failed to track application progress: ${error.message}`
      );
      // Don't throw error to avoid breaking the main process
      return {
        status: 500,
        message: 'Failed to track application progress',
        data: null,
        error: { details: error.message }
      };
    }
  }

  // Helper methods
  private formatJsonArray(data: any): any[] {
    if (!data) return [];
    if (Array.isArray(data)) return data;
    try {
      const parsed = JSON.parse(data);
      return Array.isArray(parsed) ? parsed : [parsed];
    } catch {
      return [data];
    }
  }

  private formatJsonArrayWithMetadata(data: any[]): any[] {
    if (!data || !Array.isArray(data)) return [];
    return data.map((item) => {
      if (typeof item === 'string') {
        return {
          url: item,
          uploadedAt: new Date().toISOString(),
          type: this.getFileType(item)
        };
      }
      return item;
    });
  }

  private getFileType(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase();
    const typeMap: { [key: string]: string } = {
      pdf: 'document',
      doc: 'document',
      docx: 'document',
      jpg: 'image',
      jpeg: 'image',
      png: 'image',
      gif: 'image'
    };
    return typeMap[extension || ''] || 'file';
  }

  // Helper method to convert boolean | null to gRPC BoolValue format
  private convertToGrpcBoolValue(
    value: boolean | null
  ): { value: boolean } | null {
    if (value === null || value === undefined) {
      return null;
    }
    return { value: value };
  }

  async updateDocumentRequirementStatus(
    requirementId: number | bigint,
    updateData: {
      applicationId?: number | bigint;
      acceptedByApplyGoal?: boolean | null;
      acceptedByUniversity?: boolean;
      acceptedByApplyGoalUserId?: number;
      acceptedByUniversityUserId?: number;
      rejectionReason?: string;
      status?: string;
      url?: string;
    }
  ) {
    try {
      this.logger.log(`Updating document requirement status: ${requirementId}`);

      // Convert requirementId to number for Sequelize
      const numericRequirementId =
        typeof requirementId === 'bigint'
          ? Number(requirementId)
          : requirementId;

      const requirement =
        await this.applicationDocumentRequirementModel.findByPk(
          numericRequirementId
        );
      if (!requirement) {
        throw new NotFoundException('Document requirement not found');
      }

      if (
        updateData.applicationId &&
        requirement.applicationId !== updateData.applicationId
      ) {
        throw new NotFoundException(
          'Requirement does not belong to the specified application'
        );
      }

      const updateFields: any = { ...updateData };

      if (updateData.acceptedByApplyGoal !== undefined) {
        updateFields.acceptedByApplyGoalAt = updateData.acceptedByApplyGoal
          ? new Date()
          : null;
      }

      if (updateData.acceptedByUniversity !== undefined) {
        updateFields.acceptedByUniversityAt = updateData.acceptedByUniversity
          ? new Date()
          : null;
      }
      if (updateData.status) {
        updateFields.documentStatus = updateData.status;
      }

      await requirement.update(updateFields);

      // Track the requirement update
      await this.trackApplicationProgress(
        requirement.applicationId,
        'requirement_update',
        `Requirement Updated: ${requirement.documentTitle}`,
        `Document requirement status updated for ${requirement.documentTitle} - ${requirement.documentName}`,
        {
          status: 'completed',
          createdBy:
            updateData.acceptedByApplyGoalUserId ||
            updateData.acceptedByUniversityUserId ||
            1,
          applicationStage: 'Document Review',
          applicationStep: 'Requirement Status Update'
        }
      );

      return {
        status: 200,
        message: 'Document requirement status updated successfully',
        data: {
          id: requirement.id,
          applicationId: requirement.applicationId,
          documentTitle: requirement.documentTitle,
          documentName: requirement.documentName,
          url: requirement.url,
          documentDescription: requirement.documentDescription,
          isRequired: requirement.isRequired,
          allowedFormats: requirement.allowedFormats || [],
          documentStatus: requirement.documentStatus,
          requiredByDate: requirement.requiredByDate
            ? new Date(requirement.requiredByDate).toISOString()
            : null,
          uploadedAt: requirement.uploadedAt
            ? requirement.uploadedAt.toISOString()
            : null,
          verifiedAt: requirement.verifiedAt
            ? requirement.verifiedAt.toISOString()
            : null,
          verifiedBy: requirement.verifiedBy || null,
          acceptedByApplyGoal: this.convertToGrpcBoolValue(
            requirement.acceptedByApplyGoal
          ),
          acceptedByApplyGoalUserId:
            requirement.acceptedByApplyGoalUserId || null,
          acceptedByUniversity: requirement.acceptedByUniversity || false,
          acceptedByUniversityUserId:
            requirement.acceptedByUniversityUserId || null,
          rejectionReason: requirement.rejectionReason || null,
          createdAt: requirement.createdAt.toISOString(),
          updatedAt: requirement.updatedAt.toISOString()
        },
        error: null
      };
    } catch (error) {
      this.logger.error(
        `Failed to update document requirement status: ${error.message}`
      );
      throw error;
    }
  }

  private async initializeDocumentRequirements(
    application: Application,
    createApplicationDto: CreateApplicationDto
  ) {
    try {
      this.logger.log(
        `Initializing document requirements for application: ${application.id}`
      );

      // 1) Get course requirements safely
      let proficiencyRequirements: any[] = [];
      let applicationSteps: any[] = []; // Add this line
      let lastAcademiceLevel: any;
      try {
        const resp = await this.universityClientService.getCourseRequirements({
          courseId: createApplicationDto.courseId,
          programLevelId: createApplicationDto.programId
        });

        console.log(
          '+++++++++++++++++++++++++++++++++++++>CourseInformationRecords+++++++++++++>',
          resp
        );

        if (resp && resp.status === 200 && resp.data) {
          proficiencyRequirements = Array.isArray(
            resp.data.proficiencyRequirements
          )
            ? resp.data.proficiencyRequirements
            : [];

          // Extract application steps from course requirements
          applicationSteps = Array.isArray(resp.data.applicationSteps)
            ? resp.data.applicationSteps
            : [];

          lastAcademiceLevel = resp.data.course?.lastAcademic;
        } else {
          this.logger.warn(
            `Failed to get course requirements for courseId: ${createApplicationDto.courseId}`
          );
        }
      } catch (e) {
        this.logger.warn(
          `Error getting course requirements for courseId: ${createApplicationDto.courseId} — proceeding with defaults`
        );
        proficiencyRequirements = [];
        applicationSteps = [];
      }

      // Initialize application stages from course requirements
      if (applicationSteps.length > 0) {
        try {
          // Call the method from ApplicationStageService (not ApplicationService)
          const stages =
            await this.initializeApplicationStagesFromCourseRequirements(
              Number(application.id),
              applicationSteps
            );
          console.log(
            `Initialized ${stages.length} application stages from course requirements`
          );
        } catch (stageError) {
          this.logger.error(
            'Failed to initialize application stages:',
            stageError
          );
          // Continue with document requirements even if stage initialization fails
        }
      } else {
        // Fallback to default stages if no application steps found
        try {
          await this.applicationStageService.initializeApplicationStages(
            Number(application.id)
          );
          console.log('Initialized default application stages');
        } catch (stageError) {
          this.logger.error(
            'Failed to initialize default application stages:',
            stageError
          );
        }
      }

      console.log(
        '+++++++++++++++++++++++++++++++++++++>',
        proficiencyRequirements
      );

      // 2) Get student profile slices safely
      const studentAcademic = await this.getStudentAcademic(
        createApplicationDto.studentId
      );

      console.log('+++++++++++++++++++++++++++++++++++++>', studentAcademic);
      // const studentAcademic = null;
      const academicRecords = Array.isArray(studentAcademic?.academicRecords)
        ? studentAcademic.academicRecords
        : [];
      const proficiencyRecords = Array.isArray(
        studentAcademic?.proficiencyRecords
      )
        ? studentAcademic.proficiencyRecords
        : [];
      const publicationRecords = Array.isArray(
        studentAcademic?.publicationRecords
      )
        ? studentAcademic.publicationRecords
        : [];
      const otherActivities = Array.isArray(studentAcademic?.otherActivities)
        ? studentAcademic.otherActivities
        : [];

      console.log(
        '+++++++++++++++++++++++++++++++++++++>Academice Record',
        academicRecords,
        'proficiencyRecords=========>',
        proficiencyRecords,
        'publicationRecords===================>',
        publicationRecords,
        'otherActivities======================>',
        otherActivities
      );

      // 3) Merge missing proficiency requirements from course into local proficiencyRecords
      const existingProficiencyNames = proficiencyRecords
        .map((r: any) => (r?.nameOfExam || '').toString().toLowerCase())
        .filter(Boolean);

      for (const rec of proficiencyRequirements) {
        const testName = (rec?.test || '').toString().toLowerCase();
        if (!testName) continue;

        if (!existingProficiencyNames.includes(testName)) {
          let parsedScore: any = {};
          try {
            parsedScore = rec?.testScore ? JSON.parse(rec.testScore) : {};
          } catch {
            parsedScore = {};
          }
          proficiencyRecords.push({
            nameOfExam: rec?.test || '',
            score: parsedScore,
            examDate: '',
            expiryDate: '',
            note: ''
          });
        }
      }

      // 4) Student uploaded documents map (section::field => url)
      const studentDocuments =
        await this.studentDocumentsService.getStudentDocuments(
          createApplicationDto.studentId,
          { includeInactive: false }
        );

      console.log(
        '+++++++++++++++++++++++++++++++++++++>studentDocuments',
        studentDocuments
      );

      const docMap = new Map<string, string>();
      for (const d of studentDocuments || []) {
        const section = (d?.section ?? '').toString().trim().toLowerCase();
        const field = (d?.field ?? '').toString().trim().toLowerCase();
        if (!section || !field) continue;
        if (d?.url) docMap.set(`${section}::${field}`, d.url);
      }
      const identityDocuments = [
        'passport',
        'visa',
        'resume',
        'letter of recommendation',
        'bank statement',
        'sponsor nid'
      ];
      // 5) Build requirements array exactly per your model
      const requirements: any[] = [];
      const baseDefaults = {
        isRequired: true,
        allowedFormats: ['pdf', 'jpg', 'png'] as string[],
        requiredByDate: null as Date | null,
        verifiedAt: null as Date | null,
        verifiedBy: null as number | null,
        acceptedByApplyGoal: null as boolean | null,
        acceptedByApplyGoalUserId: null as number | null,
        acceptedByUniversity: false,
        acceptedByUniversityUserId: null as number | null,
        rejectionReason: null as string | null
      };
      // ✅ NEW: Add Academic Documents Based on lastAcademic Level
      if (lastAcademiceLevel) {
        const academicDocuments =
          this.getRequiredAcademicDocuments(lastAcademiceLevel);

        for (const academicDoc of academicDocuments) {
          const doc = docMap.get(`academic::${academicDoc.toLowerCase()}`);
          const hasFile = !!doc;

          requirements.push({
            applicationId: Number(application.id),
            studentId: createApplicationDto.studentId,
            documentTitle: 'academic',
            documentName: academicDoc,
            url: doc || null,
            documentDescription: `Academic transcript or certificate for ${academicDoc.toUpperCase()}`,
            documentStatus: hasFile
              ? DocumentStatus.UPLOADED
              : DocumentStatus.REQUESTED,
            uploadedAt: hasFile ? new Date() : null,
            ...baseDefaults
          });
        }
      }

      //  ✅ Academic
      for (const academic of academicRecords) {
        const nameRaw = (academic as any)?.nameOfExam || '';
        const tag = nameRaw.toString().trim().toLowerCase();
        if (!tag) continue;

        const doc = docMap.get(`academic::${tag}`);
        const hasFile = !!doc;

        requirements.push({
          applicationId: Number(application.id),
          documentTitle: 'academic',
          documentName: nameRaw,
          url: doc || null,
          documentDescription: `Transcript or certificate for ${nameRaw
            .toString()
            .toUpperCase()} (${
            (academic as any).institute || 'Unknown Institute'
          }, ${(academic as any).board || 'Unknown Board'}, ${
            (academic as any).passingYear || 'Unknown Year'
          })`,
          documentStatus: hasFile
            ? DocumentStatus.UPLOADED
            : DocumentStatus.REQUESTED,
          uploadedAt: hasFile ? new Date() : null,
          ...baseDefaults
        });
      }
      // ✅ NEW: Identity Documents
      for (const identityDoc of identityDocuments) {
        const doc = docMap.get(`identity::${identityDoc.toLowerCase()}`);
        const hasFile = !!doc;

        requirements.push({
          applicationId: Number(application.id),
          studentId: createApplicationDto.studentId,
          documentTitle: 'identity',
          documentName: identityDoc,
          url: doc || null,
          documentDescription: `Identity document: ${identityDoc}`,
          documentStatus: hasFile
            ? DocumentStatus.UPLOADED
            : DocumentStatus.REQUESTED,
          uploadedAt: hasFile ? new Date() : null,
          ...baseDefaults
        });
      }
      // Proficiency
      for (const prof of proficiencyRecords) {
        const nameRaw = (prof as any)?.nameOfExam || '';
        const tag = nameRaw.toString().trim().toLowerCase();
        if (!tag) continue;

        const doc = docMap.get(`proficiency::${tag}`);
        const hasFile = !!doc;

        requirements.push({
          applicationId: Number(application.id),
          studentId: createApplicationDto.studentId,
          documentTitle: 'proficiency',
          documentName: nameRaw,
          url: doc || null,
          documentDescription: `Upload a valid ${nameRaw
            .toString()
            .toUpperCase()} score report.`,
          documentStatus: hasFile
            ? DocumentStatus.UPLOADED
            : DocumentStatus.REQUESTED,
          uploadedAt: hasFile ? new Date() : null,
          ...baseDefaults
        });
      }

      // Publications
      for (const pub of publicationRecords) {
        const subjRaw = (pub as any)?.subject || '';
        const tag = subjRaw.toString().trim().toLowerCase();
        if (!tag) continue;

        const doc = docMap.get(`publication::${tag}`);
        const chosenUrl = doc || (pub as any)?.link || null;
        const hasFile = !!chosenUrl;

        requirements.push({
          applicationId: Number(application.id),
          studentId: createApplicationDto.studentId,
          documentTitle: 'publication',
          documentName: subjRaw,
          url: chosenUrl,
          documentDescription: `Publication on ${subjRaw} in ${
            (pub as any).journal || 'Unknown Journal'
          }`,
          documentStatus: hasFile
            ? DocumentStatus.UPLOADED
            : DocumentStatus.REQUESTED,
          uploadedAt: hasFile ? new Date() : null,
          ...baseDefaults
        });
      }

      // Other activities
      for (const activity of otherActivities) {
        const subjRaw = (activity as any)?.subject || '';
        const tag = subjRaw.toString().trim().toLowerCase();
        if (!tag) continue;

        const doc = docMap.get(`other::${tag}`);
        const chosenUrl =
          doc ||
          (activity as any)?.certificationLink ||
          (activity as any)?.certificateLink ||
          null;
        const hasFile = !!chosenUrl;

        requirements.push({
          applicationId: Number(application.id),
          studentId: createApplicationDto.studentId,
          documentTitle: 'other',
          documentName: subjRaw,
          url: chosenUrl,
          documentDescription: `Certificate or proof for ${subjRaw}`,
          documentStatus: hasFile
            ? DocumentStatus.UPLOADED
            : DocumentStatus.REQUESTED,
          uploadedAt: hasFile ? new Date() : null,
          ...baseDefaults
        });
      }

      // 6) Upsert into DB (match by title+documentName)
      if (requirements.length > 0) {
        const existing = await this.applicationDocumentRequirementModel.findAll(
          {
            where: { applicationId: Number(application.id) }
          }
        );

        for (const r of requirements) {
          const found = existing.find(
            (e: any) =>
              e.documentTitle === r.documentTitle &&
              e.documentName === r.documentName
          );

          if (found) {
            await found.update({
              documentStatus: r.documentStatus,
              url: r.url,
              uploadedAt: r.uploadedAt,
              documentDescription: r.documentDescription,
              updatedAt: new Date()
            });
          } else {
            await this.applicationDocumentRequirementModel.create(r);
          }
        }

        this.logger.log(
          `Processed ${requirements.length} document requirements for application: ${application.id}`
        );
      } else {
        this.logger.log('No document requirements to create');
      }

      this.logger.log(
        `Document requirements initialized successfully for application: ${application.id}`
      );
    } catch (error: any) {
      this.logger.error(
        `Failed to initialize document requirements: ${error?.message}`,
        error?.stack
      );
    }
  }
  // ✅ NEW: Helper method to get required academic documents based on lastAcademic level
  private getRequiredAcademicDocuments(lastAcademic: string): string[] {
    const academicMapping = {
      HSC: ['ssc', 'hsc'],
      "Bachelor's Program": ['ssc', 'hsc', "bachelor's program"],
      "Master's Program": [
        'ssc',
        'hsc',
        "bachelor's program",
        "master's program"
      ],
      'PhD Program': [
        'ssc',
        'hsc',
        "bachelor's program",
        "master's program",
        'phd program'
      ]
    };

    return academicMapping[lastAcademic] || ['ssc', 'hsc']; // Default fallback
  }

  async getStudentAcademic(
    studentId: string
  ): Promise<StudentAcademicBackground> {
    try {
      const student = await this.studentModel.findOne({
        where: { studentId: studentId }
      });
      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const academicBackground = await this.studentAcademicModel.findOne({
        where: { studentId: student.id }
      });

      if (!academicBackground) {
        throw new NotFoundException('Academic background not found');
      }

      return academicBackground;
    } catch (error) {
      this.logger.error('Error getting student academic background:', error);
      throw error;
    }
  }

  // Add this new method to ApplicationStageService

  // Add this method after the existing initializeApplicationStages method

  async initializeApplicationStagesFromCourseRequirements(
    applicationId: number,
    applicationSteps: any[]
  ): Promise<ApplicationStage[]> {
    try {
      this.logger.log(
        `Initializing application stages from course requirements for application: ${applicationId}`
      );

      const stages = [];

      // Map application step titles to stage names
      const stageNameMapping: { [key: string]: string } = {
        'Collecting Documents': 'collecting_documents',
        Applied: 'applied',
        'Accepted / Rejected': 'accepted_rejected',
        'CAL Issued': 'cal_issued',
        'I-20 Issued': 'i20_issued',
        'Appointment Confirmation': 'appointment_confirmation',
        'VISA Processing': 'visa_processing',
        'Visa Decision': 'visa_decision',
        Enrollment: 'enrollment'
      };

      // Define which stages should be set to "in_progress" initially
      const inProgressStages = ['collecting_documents', 'applied'];
      this.logger.log(`In total stages: ${JSON.stringify(applicationSteps)}`);

      for (const step of applicationSteps) {
        // Map the step title to a stage name
        const stageName =
          stageNameMapping[step.title] ||
          step.title.toLowerCase().replace(/\s+/g, '_');
        this.logger.log(`Mapped stage name: ${stageName}`);

        // Determine initial status
        let initialStatus = ApplicationStageStatus.PENDING;
        let progressPercentage = 0;
        let startDate: Date | null = null;

        if (inProgressStages.includes(stageName)) {
          initialStatus = ApplicationStageStatus.COMPLETED;
          progressPercentage = 10; // Start with 10% progress for in-progress stages
          startDate = new Date();
        }
        this.logger.log(
          `Initial status: ${initialStatus}, progressPercentage: ${progressPercentage}, startDate: ${startDate}`
        );

        // Create the stage directly using the ApplicationStage model
        const stage = await this.applicationStageModel.create({
          applicationId: applicationId,
          stageName: stageName,
          stageOrder: step.weight || parseInt(step.id) || 1,
          status: initialStatus,
          progressPercentage: progressPercentage,
          startDate: startDate,
          completedDate: null, // Will be set when stage is completed
          requirementsMet: 0,
          totalRequirements: 0
        });

        stages.push(stage);

        this.logger.log(
          `Created stage: ${stageName} with status: ${initialStatus} for application: ${applicationId}`
        );
      }

      this.logger.log(
        `Successfully initialized ${stages.length} application stages from course requirements`
      );
      return stages;
    } catch (error) {
      this.logger.error(
        'Failed to initialize application stages from course requirements:',
        error
      );
      throw error;
    }
  }

  /**
   * Get dashboard data by campus ID
   * Returns counts for total applications, pending applications, CAL generated, and Admission Portfolio generated
   */
  async getDashboardDataByCampus(campusId: number): Promise<{
    totalApplications: number;
    pendingApplications: number;
    calGenerated: number;
    admissionPortfolioGenerated: number;
    campusId: number;
  }> {
    try {
      this.logger.log(`Getting dashboard data for campus ID: ${campusId}`);

      // Count total applications for the campus
      const totalApplicationsResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId
          },
          limit: 0
        });

      // Count pending applications for the campus
      const pendingApplicationsResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId,
            status: ApplicationStatus.PENDING
          },
          limit: 0
        });

      // Count CAL generated applications for the campus
      const calGeneratedResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId,
            isCalGenerated: true
          },
          limit: 0
        });

      // Count Admission Portfolio generated applications for the campus
      const admissionPortfolioGeneratedResult =
        await this.applicationRepository.findAndCountAll({
          where: {
            universityCountryCampus: campusId,
            isApGenerated: true
          },
          limit: 0
        });

      const dashboardData = {
        totalApplications: totalApplicationsResult.count,
        pendingApplications: pendingApplicationsResult.count,
        calGenerated: calGeneratedResult.count,
        admissionPortfolioGenerated: admissionPortfolioGeneratedResult.count,
        campusId
      };

      this.logger.log(
        `Dashboard data retrieved for campus ${campusId}:`,
        dashboardData
      );

      return dashboardData;
    } catch (error) {
      this.logger.error(
        `Failed to get dashboard data for campus ${campusId}:`,
        error
      );
      throw error;
    }
  }
}
