import {
  Injectable,
  NotFoundException,
  Logger,
  InternalServerErrorException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { firstValueFrom } from 'rxjs';
import { Transaction } from 'sequelize';
import { AgencyBranch } from './agency-branch.model';
import { Agency } from '../agency/agency.model';
import { AuditClientService } from '../audit.service';

@Injectable()
export class BranchService {
  private readonly logger = new Logger(BranchService.name);

  constructor(
    @InjectModel(AgencyBranch)
    private readonly branchModel: typeof AgencyBranch,
    @InjectModel(Agency)
    private readonly agencyModel: typeof Agency,
    private readonly auditService: AuditClientService
  ) {}

  async createAgencyBranch(data: any) {
    const transaction = await this.branchModel.sequelize.transaction();

    try {
      // Verify agency exists
      const agency = await this.agencyModel.findByPk(data.agencyId, {
        transaction
      });
      if (!agency) {
        await transaction.rollback();
        throw new NotFoundException(
          `Agency with ID "${data.agencyId}" not found`
        );
      }

      const branches = [];
      for (const branchData of data.data) {
        try {
          const branch = await this.branchModel.create(
            {
              agencyId: data.agencyId,
              branchName: branchData.branchName,
              address: branchData.address,
              country: branchData.country,
              state: branchData.state,
              city: branchData.city,
              postalCode: branchData.postalCode,
              managerContactNumber: branchData.managerContactNumber,
              email: branchData.email
            },
            { transaction }
          );
          branches.push(branch);
        } catch (branchError) {
          await transaction.rollback();
          throw new InternalServerErrorException(
            `Failed to create branch "${branchData.branchName}": ${branchError.message}`
          );
        }
      }

      // Commit the transaction
      await transaction.commit();

      // Log audit
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'CREATE_AGENCY_BRANCH',
          serviceName: 'agency-service',
          resourceType: 'AgencyBranch',
          resourceId: Number(branches[0]?.id || 0),
          description: `Created ${branches.length} agency branches for agency ${data.agencyId}`,
          metadata: {
            agencyId: data.agencyId,
            branchCount: branches.length.toString()
          },
          source: 'agency-service'
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return branches;
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn(
          'Transaction rollback failed (might already be finished)',
          rollbackError.message
        );
      }

      this.logger.error('Failed to create agency branch', error.stack);
      throw error;
    }
  }

  async getAgencyBranch(id: string) {
    try {
      const branch = await this.branchModel.findByPk(id, {
        include: [Agency]
      });

      if (!branch) {
        throw new NotFoundException('Agency branch not found');
      }

      return branch;
    } catch (error) {
      this.logger.error('Failed to get agency branch', error.stack);
      throw error;
    }
  }

  async updateAgencyBranch(data: any) {
    console.log("====================================> update data",data)
    const transaction = await this.branchModel.sequelize.transaction();

    try {
      const branch = await this.branchModel.findByPk(parseInt(data.id), {
        transaction
      });

      if (!branch) {
        await transaction.rollback();
        throw new NotFoundException(
          `Agency branch with ID "${data.id}" not found`
        );
      }

      await branch.update(
        {
          branchName: data.branchName,
          address: data.address,
          country: data.country,
          state: data.state,
          city: data.city,
          postalCode: data.postalCode,
          managerContactNumber: data.managerContactNumber,
          email: data.email
        },
        { transaction }
      );

      await transaction.commit();

      // ✅ Log audit
      try {
        await firstValueFrom(
          this.auditService.createAuditLog({
            userId: parseInt(data.userId) || 0,
            userRole: data.userRole || 'unknown',
            actions: 'UPDATE_AGENCY_BRANCH',
            serviceName: 'agency-service',
            resourceType: 'AgencyBranch',
            resourceId: Number(branch.id),
            description: `Updated agency branch "${branch.branchName}"`,
            metadata: {
              branchName: branch.branchName
            },
            source: 'agency-service'
          })
        );
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return branch;
    } catch (error) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn('Transaction rollback failed', rollbackError.message);
      }

      this.logger.error('Failed to update agency branch', error.stack);
      throw error;
    }
  }

  async deleteAgencyBranch(data: any) {
    try {
      const branch = await this.branchModel.findByPk(data.id);
      if (!branch) {
        throw new NotFoundException('Agency branch not found');
      }
     try {
        await firstValueFrom(
          this.auditService.createAuditLog({
            userId: parseInt(data.userId) || 0,
            userRole: data.userRole || 'unknown',
            actions: 'DELETE_AGENCY_BRANCH',
            serviceName: 'agency-service',
            resourceType: 'AgencyBranch',
            resourceId: Number(branch.id),
            description: `Updated agency branch "${branch.branchName}"`,
            metadata: {
              branchName: branch.branchName
            },
            source: 'agency-service'
          })
        );
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }
      await branch.destroy();

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency branch', error.stack);
      throw error;
    }
  }

  async listAgencyBranches(data: any) {
    try {
      const { agencyId, page = 1, pageSize = 10 } = data;
      const offset = (page - 1) * pageSize;

      // Verify agency exists
      const agency = await this.agencyModel.findByPk(agencyId);
      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      const { rows: branches, count: total } =
        await this.branchModel.findAndCountAll({
          where: { agencyId },
          limit: pageSize,
          offset,
          order: [['createdAt', 'DESC']]
        });

      return {
        data: branches,
        total,
        page,
        pageSize
      };
    } catch (error) {
      this.logger.error('Failed to list agency branches', error.stack);
      throw error;
    }
  }
}
