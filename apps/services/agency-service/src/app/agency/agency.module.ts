import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AgencyService } from './agency.service';
import { AgencyGrpcController } from './agency.grpc.controller';
import { Agency } from './agency.model';
import { AuditClientService } from '../audit.service';
import { AuthClientService } from '../auth.service';
import { AgencyBranch } from '../branch/agency-branch.model';

@Module({
  imports: [SequelizeModule.forFeature([Agency,AgencyBranch])],
  controllers: [AgencyGrpcController],
  providers: [AgencyService, AuditClientService, AuthClientService],
  exports: [AgencyService],
})
export class AgencyModule {}
