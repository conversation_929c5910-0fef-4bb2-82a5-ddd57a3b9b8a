import {
  Table,
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  HasMany
} from 'sequelize-typescript';
import { BaseModel } from '@apply-goal-backend/database';
import { AgencyBranch } from '../branch/agency-branch.model';
import { AgencyContact } from '../contact/agency-contact.model';

@Table({
  tableName: 'agencies',
  timestamps: true
})
export class Agency extends BaseModel {
  @Column({
    type: DataType.STRING
    // allowNull: false,
    // unique: true,
  })
  agencyName!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  agencyLogo!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false
  })
  address!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  agencySize!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true
  })
  about!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  primaryContactNumber!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  country!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  state!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  city!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  postalCode!: string;

  @Column({
    type: DataType.STRING
    // allowNull: false,
    // unique: true,
  })
  agencyRegistrationNumber!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  websiteUrl!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true
  })
  email!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: 'active' // ✅ Default to 'active'
  })
  status!: string;

  // Associations
  @HasMany(() => AgencyBranch)
  branches!: AgencyBranch[];

  @HasMany(() => AgencyContact)
  contacts!: AgencyContact[];
}
