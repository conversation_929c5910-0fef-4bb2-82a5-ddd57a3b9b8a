import { Controller, Logger } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { AgencyService } from './agency.service';

@Controller()
export class AgencyGrpcController {
  private readonly logger = new Logger(AgencyGrpcController.name);

  constructor(private readonly agencyService: AgencyService) {}

  @GrpcMethod('AgencyService', 'CreateAgency')
  async createAgency(data: any) {
    try {
      this.logger.log(`Creating agency: ${data.agencyName}`);
      const result = await this.agencyService.createAgency(data);

      return {
        status: 200,
        message: 'Agency created successfully',
        data: result,
        error: null
      };
    } catch (error) {
      this.logger.error('Failed to create agency', error.stack);

      // Return specific error status based on error type
      let status = 500;
      let code = 'INTERNAL_ERROR';

      if (error.status === 409) {
        status = 409;
        code = 'CONFLICT';
      } else if (error.status === 400) {
        status = 400;
        code = 'BAD_REQUEST';
      }

      return {
        status,
        message: error.message || 'Failed to create agency',
        data: null,
        error: {
          code,
          message: error.message,
          details: error.stack,
          validationErrors: []
        }
      };
    }
  }

  @GrpcMethod('AgencyService', 'GetAgency')
  async getAgency(data: any) {
    try {
      this.logger.log(`Getting agency with ID: ${data.id}`);
      const result = await this.agencyService.getAgency(data.id);

      return {
        status: 200,
        message: 'Agency retrieved successfully',
        data: result,
        error: null
      };
    } catch (error) {
      this.logger.error('Failed to get agency', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to get agency',
        data: null,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: []
        }
      };
    }
  }

  @GrpcMethod('AgencyService', 'UpdateAgency')
  async updateAgency(data: any) {
    try {
      this.logger.log(`Updating agency with ID: ${data.id}`);
      const result = await this.agencyService.updateAgency(data);

      return {
        status: 200,
        message: 'Agency updated successfully',
        data: result,
        error: null
      };
    } catch (error) {
      this.logger.error('Failed to update agency', error.stack);

      // Return specific error status based on error type
      let status = 500;
      let code = 'INTERNAL_ERROR';

      if (error.status === 404) {
        status = 404;
        code = 'NOT_FOUND';
      } else if (error.status === 409) {
        status = 409;
        code = 'CONFLICT';
      } else if (error.status === 400) {
        status = 400;
        code = 'BAD_REQUEST';
      }

      return {
        status,
        message: error.message || 'Failed to update agency',
        data: null,
        error: {
          code,
          message: error.message,
          details: error.stack,
          validationErrors: []
        }
      };
    }
  }

  @GrpcMethod('AgencyService', 'DeleteAgency')
  async deleteAgency(data: any) {
    try {
      this.logger.log(`Deleting agency with ID: ${data.id}`);
      await this.agencyService.deleteAgency(data.id);

      return {
        status: 200,
        message: 'Agency deleted successfully',
        error: null
      };
    } catch (error) {
      this.logger.error('Failed to delete agency', error.stack);
      return {
        status: error.status || 500,
        message: 'Failed to delete agency',
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: []
        }
      };
    }
  }

  @GrpcMethod('AgencyService', 'ListAgencies')
  async listAgencies(data: any) {
    try {
      this.logger.log('Listing agencies');
      const result = await this.agencyService.listAgencies(data);

      return {
        status: 200,
        message: 'Agencies retrieved successfully',
        data: result.data, // array of agencies
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        error: null
      };
    } catch (error) {
      this.logger.error('Failed to list agencies', error.stack);
      return {
        status: 500,
        message: 'Failed to list agencies',
        data: [],
        total: 0,
        page: 1,
        pageSize: 10,
        error: {
          code: 'INTERNAL_ERROR',
          message: error.message,
          details: error.stack,
          validationErrors: []
        }
      };
    }
  }
}
