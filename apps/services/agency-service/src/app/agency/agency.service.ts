import {
  Injectable,
  NotFoundException,
  Logger,
  ConflictException,
  InternalServerErrorException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { col, fn, literal, Op, Transaction } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { Agency } from './agency.model';
import { AuditClientService } from '../audit.service';
import { AuthClientService } from '../auth.service';
import { AgencyBranch } from '../branch/agency-branch.model';

@Injectable()
export class AgencyService {
  private readonly logger = new Logger(AgencyService.name);

  constructor(
    @InjectModel(Agency) private agencyModel: typeof Agency,
    @InjectModel(AgencyBranch) private branchModel: typeof AgencyBranch,
    private auditService: AuditClientService,
    private authService: AuthClientService
  ) {}

  async createAgency(data: any) {
    const transaction = await this.agencyModel.sequelize.transaction();

    try {
      // Step 1: Check for duplicates
      const duplicateAgency = await this.agencyModel.findOne({
        where: {
          [Op.or]: [
            { email: data.email },
            { agencyName: data.agencyName },
            { agencyRegistrationNumber: data.agencyRegistrationNumber }
          ]
        },
        transaction
      });

      if (duplicateAgency) {
        await transaction.rollback();

        const reason =
          duplicateAgency.email === data.email
            ? `Email "${data.email}" already exists`
            : duplicateAgency.agencyName === data.agencyName
            ? `Agency name "${data.agencyName}" already exists`
            : `Registration number "${data.agencyRegistrationNumber}" already exists`;

        throw new ConflictException(reason);
      }

      // Step 2: Create agency
      const agency = await this.agencyModel.create(
        {
          agencyName: data.agencyName,
          agencyLogo: data.agencyLogo,
          address: data.address,
          agencySize: data.agencySize,
          about: data.about,
          primaryContactNumber: data.primaryContactNumber,
          country: data.country,
          state: data.state,
          city: data.city,
          postalCode: data.postalCode,
          agencyRegistrationNumber: data.agencyRegistrationNumber,
          websiteUrl: data.websiteUrl,
          email: data.email
        },
        { transaction }
      );

      this.logger.log('Agency created in DB:', agency);

      // Step 3: Call auth service
      try {
        const userObservable = this.authService.createAgency({
          name: data.agencyName,
          address: data.address,
          country: data.country,
          phone: data.primaryContactNumber,
          email: data.email,
          ipAddress: data.ipAddress || '',
          userAgent: data.userAgent || ''
        });

        const userResult = await firstValueFrom(userObservable);

        if (!userResult?.success) {
          throw new Error(
            userResult?.message || 'User creation failed from authenticate'
          );
        }

        this.logger.log(`Auth service user created for agency: ${data.email}`);
      } catch (authError) {
        await transaction.rollback();
        this.logger.error('Auth service failed:', authError);

        throw new InternalServerErrorException(
          `Agency user creation failed: ${
            authError?.message || 'Auth service error'
          }`
        );
      }

      // Step 4: Commit transaction
      await transaction.commit();

      // Step 5: Log audit (non-blocking)
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'CREATE_AGENCY',
          serviceName: 'agency-service',
          resourceType: 'Agency',
          resourceId: Number(agency.id),
          description: `Created agency: ${data.agencyName}`,
          metadata: {
            agencyName: data.agencyName,
            email: data.email
          },
          source: 'agency-service'
        });

        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit:', auditError);
      }

      return agency;
    } catch (error) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn(
          'Transaction rollback failed (may have already occurred):',
          rollbackError.message
        );
      }

      this.logger.error(
        'Agency creation failed:',
        error.stack || error.message
      );
      throw error;
    }
  }

  async getAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(id, {
        include: ['branches', 'contacts']
      });

      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      return agency;
    } catch (error) {
      this.logger.error('Failed to get agency', error.stack);
      throw error;
    }
  }

  async updateAgency(data: any) {
    const transaction = await this.agencyModel.sequelize.transaction();

    try {
      const agency = await this.agencyModel.findByPk(data.id, { transaction });
      if (!agency) {
        await transaction.rollback();
        throw new NotFoundException(`Agency with ID "${data.id}" not found`);
      }

      // Check for conflicts if email or registration number is being updated
      if (data.email || data.agencyName) {
        const conflictWhere: any = {
          id: { [Op.ne]: data.id } // Exclude current agency
        };

        const orConditions = [];
        if (data.email && data.email !== agency.email) {
          orConditions.push({ email: data.email });
        }
        if (data.agencyName && data.agencyName !== agency.agencyName) {
          orConditions.push({ agencyName: data.agencyName });
        }

        if (orConditions.length > 0) {
          conflictWhere[Op.or] = orConditions;

          const existing = await this.agencyModel.findOne({
            where: conflictWhere,
            transaction
          });

          if (existing) {
            await transaction.rollback();
            throw new ConflictException(
              `Another agency already exists with the provided email or registration number`
            );
          }
        }
      }

      const updateData: any = {};
      if (data.agencyName) updateData.agencyName = data.agencyName;
      if (data.agencyLogo) updateData.agencyLogo = data.agencyLogo;
      if (data.address) updateData.address = data.address;
      if (data.agencySize) updateData.agencySize = data.agencySize;
      if (data.about) updateData.about = data.about;
      if (data.primaryContactNumber)
        updateData.primaryContactNumber = data.primaryContactNumber;
      if (data.country) updateData.country = data.country;
      if (data.state) updateData.state = data.state;
      if (data.city) updateData.city = data.city;
      if (data.postalCode) updateData.postalCode = data.postalCode;
      if (data.agencyRegistrationNumber)
        updateData.agencyRegistrationNumber = data.agencyRegistrationNumber;
      if (data.websiteUrl) updateData.websiteUrl = data.websiteUrl;
      if (data.email) updateData.email = data.email;

      await agency.update(updateData, { transaction });

      // Commit the transaction
      await transaction.commit();

      // Log audit
      try {
        const auditObservable = this.auditService.createAuditLog({
          userId: parseInt(data.userId) || 0,
          userRole: data.userRole || 'unknown',
          actions: 'UPDATE_AGENCY',
          serviceName: 'agency-service',
          resourceType: 'Agency',
          resourceId: Number(agency.id),
          description: `Updated agency: ${agency.agencyName}`,
          metadata: {
            agencyName: agency.agencyName
          },
          source: 'agency-service'
        });
        await firstValueFrom(auditObservable);
      } catch (auditError) {
        this.logger.warn('Failed to log audit', auditError);
      }

      return agency;
    } catch (error) {
      // Ensure transaction is rolled back if not already done
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        this.logger.warn(
          'Transaction rollback failed (might already be finished)',
          rollbackError.message
        );
      }

      this.logger.error('Failed to update agency', error.stack);
      throw error;
    }
  }

  async deleteAgency(id: string) {
    try {
      const agency = await this.agencyModel.findByPk(Number(id), {
        paranoid: false
      });

      if (!agency) {
        throw new NotFoundException('Agency not found');
      }

      await agency.destroy({ force: true }); // Soft delete
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to delete agency', error.stack);
      throw error;
    }
  }

  async listAgencies(data: any = {}) {
    try {
      const defaultQuery = {
        page: 1,
        pageSize: 10,
        search: null,
        country: null,
        status: null,
        fromDate: null,
        toDate: null,
        sortBy: 'createdAt'
      };

      const queryParams = {
        page: Math.max(1, parseInt(data?.page) || defaultQuery.page),
        pageSize: Math.min(
          100,
          Math.max(1, parseInt(data?.pageSize) || defaultQuery.pageSize)
        ),
        search:
          data?.search && typeof data.search === 'string' && data.search.trim()
            ? data.search.trim()
            : defaultQuery.search,
        country:
          data?.country &&
          typeof data.country === 'string' &&
          data.country.trim()
            ? data.country.trim()
            : defaultQuery.country,
        status:
          data?.status && typeof data.status === 'string' && data.status.trim()
            ? data.status.trim()
            : defaultQuery.status,
        fromDate: data?.fromDate || null,
        toDate: data?.toDate || null,
        sortBy: defaultQuery.sortBy
      };

      const validSortColumns = [
        'id',
        'createdAt',
        'updatedAt',
        'agencyName',
        'email',
        'country',
        'city',
        'status',
        'isActive',
        'agencySize',
        'postalCode'
      ];

      if (
        data?.sortBy &&
        typeof data.sortBy === 'string' &&
        validSortColumns.includes(data.sortBy.trim())
      ) {
        queryParams.sortBy = data.sortBy.trim();
      }

      const offset = (queryParams.page - 1) * queryParams.pageSize;

      const whereClause: any = {
        [Op.and]: []
      };

      // ✅ Search by id or agencyName
      if (queryParams.search) {
        const isNumeric = /^\d+$/.test(queryParams.search); // Check if search is numeric

        const searchConditions: any[] = [
          { agencyName: { [Op.iLike]: `%${queryParams.search}%` } }
        ];

        if (isNumeric) {
          searchConditions.push({ id: Number(queryParams.search) }); // exact match
        }

        whereClause[Op.and].push({ [Op.or]: searchConditions });
      }

      // ✅ Filter by country
      if (queryParams.country) {
        whereClause[Op.and].push({ country: queryParams.country });
      }

      // ✅ Filter by status
      if (queryParams.status) {
        whereClause[Op.and].push({ status: queryParams.status });
      }

      // ✅ Filter by date range
      if (queryParams.fromDate && queryParams.toDate) {
        whereClause[Op.and].push({
          createdAt: {
            [Op.between]: [
              new Date(queryParams.fromDate),
              new Date(queryParams.toDate)
            ]
          }
        });
      }
      // ✅ Get paginated agencies
      const agencies = await this.agencyModel.findAndCountAll({
        where: whereClause,
        limit: queryParams.pageSize,
        offset,
        order: [[queryParams.sortBy, 'DESC']],
        distinct: true
      });

      // ✅ Handle empty results
      if (!agencies.rows || agencies.rows.length === 0) {
        return {
          data: [],
          total: agencies.count || 0,
          page: queryParams.page,
          pageSize: queryParams.pageSize,
          totalPages: Math.ceil((agencies.count || 0) / queryParams.pageSize)
        };
      }

      // ✅ Attach branch counts
      const agenciesWithBranchCount = await Promise.all(
        agencies.rows.map(async (agency) => {
          try {
            const branchCount = await this.branchModel.count({
              where: {
                agencyId: agency.id,
                ...(this.branchModel.rawAttributes.deletedAt
                  ? { deletedAt: null }
                  : {})
              }
            });
            return {
              ...agency.toJSON(),
              branchCount: branchCount || 0,
              status: agency.status
            };
          } catch (branchError) {
            this.logger.warn(
              `Failed to get branch count for agency ${agency.id}:`,
              branchError.message
            );
            return {
              ...agency.toJSON(),
              branchCount: 0,
              keyAccountManager: null,
              disContinuationDate: null,
              newStudentEnrollment: 0,
              applicationSubmitted: 0,
              transferredProcessed: 0,
              performance: 0,
              status: agency.status
            };
          }
        })
      );

      const totalPages = Math.ceil(agencies.count / queryParams.pageSize);

      return {
        data: agenciesWithBranchCount,
        total: agencies.count,
        page: queryParams.page,
        pageSize: queryParams.pageSize,
        totalPages,
        hasNextPage: queryParams.page < totalPages,
        hasPreviousPage: queryParams.page > 1
      };
    } catch (error) {
      this.logger.error('Failed to list agencies', error.stack);
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: 10,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
        error: 'Failed to fetch agencies'
      };
    }
  }
}
