import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';
import { getDatabaseConfig } from '@apply-goal-backend/database';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MetricsController } from './metrics.controller';
import { AuditClientService } from './audit.service';
import { AuthClientService } from './auth.service';
import { MonitoringModule } from '@apply-goal-backend/monitoring';
import { HealthController } from './health.controller';
import { AgencyModule } from './agency/agency.module';
import { BranchModule } from './branch/branch.module';
import { ContactModule } from './contact/contact.module';
import { SeederModule } from '../seeders/seeder.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    SequelizeModule.forRoot({
      ...getDatabaseConfig('agency'),
      autoLoadModels: true,
      synchronize: true,
    }),
    MonitoringModule.forRoot({
      metrics: {
        serviceName: 'agency-service',
        serviceVersion: '1.0.0',
        port: parseInt(process.env.METRICS_PORT || '5001', 10),
        path: '/metrics',
        labels: {
          environment: process.env.NODE_ENV || 'development'
        }
      },
      tracing: {
        serviceName: 'agency-service',
        jaegerEndpoint: 'http://jaeger:4318/v1/traces'
      }
    }),
    AgencyModule,
    BranchModule,
    ContactModule,
    SeederModule,
  ],
  controllers: [AppController, MetricsController, HealthController],
  providers: [AppService, AuditClientService, AuthClientService],
})
export class AppModule {}
