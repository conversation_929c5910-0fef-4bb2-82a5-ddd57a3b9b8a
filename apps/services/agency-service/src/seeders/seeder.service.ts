import { Injectable, Logger } from '@nestjs/common';
import { AgencySeeder } from './agency.seeder';

@Injectable()
export class SeederService {
  private readonly logger = new Logger(SeederService.name);

  constructor(private readonly agencySeeder: AgencySeeder) {}

  async runAllSeeders() {
    try {
      this.logger.log('🌱 Starting agency database seeding process...');
      
      // Run agency seeder
      await this.agencySeeder.seed();
      
      this.logger.log('✅ All agency seeders completed successfully!');
    } catch (error) {
      this.logger.error('❌ Agency seeding failed:', error);
      throw error;
    }
  }
}
