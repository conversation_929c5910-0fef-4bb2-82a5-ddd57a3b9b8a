import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { AgencySeeder } from './agency.seeder';
import { SeederService } from './seeder.service';

// Import all models
import { Agency } from '../app/agency/agency.model';
import { AgencyBranch } from '../app/branch/agency-branch.model';
import { AgencyContact } from '../app/contact/agency-contact.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Agency,
      AgencyBranch,
      AgencyContact,
    ]),
  ],
  providers: [AgencySeeder, SeederService],
  exports: [AgencySeeder, SeederService],
})
export class SeederModule {}
