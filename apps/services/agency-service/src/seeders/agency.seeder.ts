import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Agency } from '../app/agency/agency.model';
import { AgencyBranch } from '../app/branch/agency-branch.model';
import { AgencyContact } from '../app/contact/agency-contact.model';

@Injectable()
export class AgencySeeder {
  private readonly logger = new Logger(AgencySeeder.name);

  constructor(
    @InjectModel(Agency) private agencyModel: typeof Agency,
    @InjectModel(AgencyBranch) private agencyBranchModel: typeof AgencyBranch,
    @InjectModel(AgencyContact) private agencyContactModel: typeof AgencyContact,
  ) {}

  async seed() {
    try {
      this.logger.log('Starting agency seeding process...');

      // Check if data already exists
      const existingAgency = await this.agencyModel.findOne();
      if (existingAgency) {
        this.logger.log('Agencies already exist, skipping seeding');
        return;
      }

      const transaction = await this.agencyModel.sequelize.transaction();

      try {
        // 1. Create Agencies
        const agencies = await this.createAgencies(transaction);
        this.logger.log(`Created ${agencies.length} agencies`);

        // 2. Create Agency Branches
        const branches = await this.createAgencyBranches(agencies, transaction);
        this.logger.log(`Created ${branches.length} agency branches`);

        // 3. Create Agency Contacts
        const contacts = await this.createAgencyContacts(agencies, transaction);
        this.logger.log(`Created ${contacts.length} agency contacts`);

        await transaction.commit();
        this.logger.log('Agency seeding completed successfully!');

      } catch (error) {
        await transaction.rollback();
        throw error;
      }

    } catch (error) {
      this.logger.error('Error during agency seeding:', error);
      throw error;
    }
  }

  private async createAgencies(transaction: any) {
    const agenciesData = [
      {
        agencyName: "Global Education Partners",
        agencyLogo: "https://example.com/logos/global-education-partners.jpg",
        address: "123 Education Street, Business District",
        agencySize: "Large",
        about: "Leading international education consultancy with over 15 years of experience helping students achieve their academic dreams worldwide.",
        primaryContactNumber: "******-0101",
        country: "United States",
        state: "New York",
        city: "New York",
        postalCode: "10001",
        agencyRegistrationNumber: "GEP-2024-001",
        websiteUrl: "https://www.globaleducationpartners.com",
        email: "<EMAIL>"
      },
      {
        agencyName: "StudyAbroad Excellence",
        agencyLogo: "https://example.com/logos/studyabroad-excellence.jpg",
        address: "456 University Avenue, Academic Quarter",
        agencySize: "Medium",
        about: "Specialized in connecting students with top universities across Europe, North America, and Australia.",
        primaryContactNumber: "+44-20-7946-0958",
        country: "United Kingdom",
        state: "England",
        city: "London",
        postalCode: "SW1A 1AA",
        agencyRegistrationNumber: "SAE-2024-002",
        websiteUrl: "https://www.studyabroadexcellence.co.uk",
        email: "<EMAIL>"
      },
      {
        agencyName: "EduConsult Asia",
        agencyLogo: "https://example.com/logos/educonsult-asia.jpg",
        address: "789 Knowledge Park, Tech Hub",
        agencySize: "Medium",
        about: "Premier education consultancy serving Asian students with personalized guidance for international education opportunities.",
        primaryContactNumber: "+65-6123-4567",
        country: "Singapore",
        state: "Central Region",
        city: "Singapore",
        postalCode: "018989",
        agencyRegistrationNumber: "ECA-2024-003",
        websiteUrl: "https://www.educonsultasia.com",
        email: "<EMAIL>"
      },
      {
        agencyName: "Future Scholars International",
        agencyLogo: "https://example.com/logos/future-scholars.jpg",
        address: "321 Scholar Lane, Education Hub",
        agencySize: "Small",
        about: "Boutique consultancy focused on providing personalized service for students seeking quality education abroad.",
        primaryContactNumber: "+61-2-9876-5432",
        country: "Australia",
        state: "New South Wales",
        city: "Sydney",
        postalCode: "2000",
        agencyRegistrationNumber: "FSI-2024-004",
        websiteUrl: "https://www.futurescholars.com.au",
        email: "<EMAIL>"
      },
   
    ];

    return await this.agencyModel.bulkCreate(agenciesData, { transaction });
  }

  private async createAgencyBranches(agencies: any[], transaction: any) {
    const branchesData = [];

    // Create multiple branches for each agency
    for (const agency of agencies) {
      if (agency.agencyName === "Global Education Partners") {
        branchesData.push(
          {
            agencyId: agency.id,
            branchName: "Manhattan Branch",
            address: "789 Broadway, Manhattan",
            country: "United States",
            state: "New York",
            city: "New York",
            postalCode: "10003",
            managerContactNumber: "******-0102",
            email: "<EMAIL>"
          },
          {
            agencyId: agency.id,
            branchName: "Brooklyn Branch",
            address: "456 Atlantic Avenue, Brooklyn",
            country: "United States",
            state: "New York",
            city: "Brooklyn",
            postalCode: "11217",
            managerContactNumber: "******-0103",
            email: "<EMAIL>"
          }
        );
      } else if (agency.agencyName === "StudyAbroad Excellence") {
        branchesData.push(
          {
            agencyId: agency.id,
            branchName: "Central London Branch",
            address: "123 Oxford Street, Central London",
            country: "United Kingdom",
            state: "England",
            city: "London",
            postalCode: "W1C 1DE",
            managerContactNumber: "+44-20-7946-0959",
            email: "<EMAIL>"
          },
          {
            agencyId: agency.id,
            branchName: "Birmingham Branch",
            address: "789 High Street, Birmingham",
            country: "United Kingdom",
            state: "England",
            city: "Birmingham",
            postalCode: "B1 1AA",
            managerContactNumber: "+44-************",
            email: "<EMAIL>"
          }
        );
      } else if (agency.agencyName === "EduConsult Asia") {
        branchesData.push(
          {
            agencyId: agency.id,
            branchName: "Orchard Branch",
            address: "456 Orchard Road, Orchard",
            country: "Singapore",
            state: "Central Region",
            city: "Singapore",
            postalCode: "238877",
            managerContactNumber: "+65-6123-4568",
            email: "<EMAIL>"
          }
        );
      } else if (agency.agencyName === "Future Scholars International") {
        branchesData.push(
          {
            agencyId: agency.id,
            branchName: "CBD Branch",
            address: "123 George Street, CBD",
            country: "Australia",
            state: "New South Wales",
            city: "Sydney",
            postalCode: "2000",
            managerContactNumber: "+61-2-9876-5433",
            email: "<EMAIL>"
          }
        );
      } else if (agency.agencyName === "Dream University Consultants") {
        branchesData.push(
          {
            agencyId: agency.id,
            branchName: "Downtown Toronto Branch",
            address: "789 Bay Street, Downtown",
            country: "Canada",
            state: "Ontario",
            city: "Toronto",
            postalCode: "M5G 1M5",
            managerContactNumber: "+1-416-555-0200",
            email: "<EMAIL>"
          },
          {
            agencyId: agency.id,
            branchName: "Mississauga Branch",
            address: "456 Square One Drive, Mississauga",
            country: "Canada",
            state: "Ontario",
            city: "Mississauga",
            postalCode: "L5B 1M2",
            managerContactNumber: "+1-905-555-0201",
            email: "<EMAIL>"
          }
        );
      }
    }

    return await this.agencyBranchModel.bulkCreate(branchesData, { transaction });
  }

  private async createAgencyContacts(agencies: any[], transaction: any) {
    const contactsData = [];

    // Create contacts for each agency
    for (const agency of agencies) {
      if (agency.agencyName === "Global Education Partners") {
        contactsData.push({
          agencyId: agency.id,
          ownerName: "Robert Johnson",
          ownerContactNumber: "******-0104",
          ownerAlternateContactNumber: "******-0105",
          ownerEmail: "<EMAIL>",
          primaryPersonName: "Sarah Williams",
          primaryPersonDesignation: "Director of Operations",
          primaryContactNumber: "******-0106",
          primaryAlternateContactNumber: "******-0107",
          primaryEmail: "<EMAIL>"
        });
      } else if (agency.agencyName === "StudyAbroad Excellence") {
        contactsData.push({
          agencyId: agency.id,
          ownerName: "James Thompson",
          ownerContactNumber: "+44-20-7946-0960",
          ownerAlternateContactNumber: "+44-20-7946-0961",
          ownerEmail: "<EMAIL>",
          primaryPersonName: "Emma Davis",
          primaryPersonDesignation: "Head of Student Services",
          primaryContactNumber: "+44-20-7946-0962",
          primaryAlternateContactNumber: "+44-20-7946-0963",
          primaryEmail: "<EMAIL>"
        });
      } else if (agency.agencyName === "EduConsult Asia") {
        contactsData.push({
          agencyId: agency.id,
          ownerName: "Li Wei Chen",
          ownerContactNumber: "+65-6123-4569",
          ownerAlternateContactNumber: "+65-6123-4570",
          ownerEmail: "<EMAIL>",
          primaryPersonName: "Priya Sharma",
          primaryPersonDesignation: "Senior Education Consultant",
          primaryContactNumber: "+65-6123-4571",
          primaryAlternateContactNumber: "+65-6123-4572",
          primaryEmail: "<EMAIL>"
        });
      } else if (agency.agencyName === "Future Scholars International") {
        contactsData.push({
          agencyId: agency.id,
          ownerName: "Michael O'Connor",
          ownerContactNumber: "+61-2-9876-5434",
          ownerAlternateContactNumber: "+61-2-9876-5435",
          ownerEmail: "<EMAIL>",
          primaryPersonName: "Jessica Brown",
          primaryPersonDesignation: "Student Advisor",
          primaryContactNumber: "+61-2-9876-5436",
          primaryAlternateContactNumber: "+61-2-9876-5437",
          primaryEmail: "<EMAIL>"
        });
      } else if (agency.agencyName === "Dream University Consultants") {
        contactsData.push({
          agencyId: agency.id,
          ownerName: "David Martinez",
          ownerContactNumber: "+1-416-555-0202",
          ownerAlternateContactNumber: "+1-416-555-0203",
          ownerEmail: "<EMAIL>",
          primaryPersonName: "Amanda Wilson",
          primaryPersonDesignation: "Client Relations Manager",
          primaryContactNumber: "+1-416-555-0204",
          primaryAlternateContactNumber: "+1-416-555-0205",
          primaryEmail: "<EMAIL>"
        });
      }
    }

    return await this.agencyContactModel.bulkCreate(contactsData, { transaction });
  }
}
