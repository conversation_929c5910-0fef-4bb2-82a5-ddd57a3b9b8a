import {
  Controller,
  Post,
  UploadedFiles,
  UseInterceptors,
  Query,
  Logger,
} from '@nestjs/common';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { UploadService, MulterFile } from '@apply-goal-backend/common'; // adjust import path as needed
import { Public } from '@apply-goal-backend/auth';
import { error } from 'console';

@Controller('upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post()
  @Public()
  @UseInterceptors(AnyFilesInterceptor())
  async uploadFiles(
    @UploadedFiles() files: MulterFile[],
    @Query('type') type: 'image' | 'file' = 'file'
  ) {
    try {
      if (!files || files.length === 0) {
        return {
          urls: [],
          message: 'No files uploaded',
          error: 'No files provided',
        };
      }

      const uploadPromises = files.map(async (file) => {
        try {
          const { url } = await this.uploadService.uploadFile(file, type);
          return {
            originalName: file.originalname,
            fieldname: file.fieldname,
            size: file.size,
            mimetype: file.mimetype,
            url: url,
            success: true,
            error: null,
          };
        } catch (uploadError) {
          Logger.error(`Upload error for file ${file.originalname}:`, uploadError);
          return {
            originalName: file.originalname,
            fieldname: file.fieldname,
            size: file.size,
            mimetype: file.mimetype,
            url: null,
            success: false,
            error: uploadError.message,
          };
        }
      });

      const results = await Promise.all(uploadPromises);
      const successfulUploads = results.filter(result => result.success);
      const failedUploads = results.filter(result => !result.success);

      return {
        urls: successfulUploads.map(result => result.url),
        results: results,
        message: `Upload completed. ${successfulUploads.length} successful, ${failedUploads.length} failed`,
        totalFiles: files.length,
        successfulUploads: successfulUploads.length,
        failedUploads: failedUploads.length,
        error: failedUploads.length > 0 ? `${failedUploads.length} files failed to upload` : null,
      };
    } catch (error) {
      Logger.error('Upload error:', error);
      return {
        urls: [],
        results: [],
        message: 'File upload failed',
        totalFiles: 0,
        successfulUploads: 0,
        failedUploads: files?.length || 0,
        error: `Upload error: ${error.message}`,
      };
    }
  }

  // Keep the single file upload for backward compatibility
  @Post('single')
  @Public()
  @UseInterceptors(AnyFilesInterceptor())
  async uploadFile(
    @UploadedFiles() files: MulterFile[],
    @Query('type') type: 'image' | 'file' = 'file'
  ) {
    try {
      if (!files || files.length === 0) {
        return {
          url: null,
          message: 'No file uploaded',
          error: 'No file provided',
        };
      }

      if (files.length > 1) {
        return {
          url: null,
          message: 'Multiple files provided, use /upload for multiple files',
          error: 'Use /upload endpoint for multiple files',
        };
      }

      const file = files[0];
      const { url } = await this.uploadService.uploadFile(file, type);
      
      if (!url) {
        Logger.error('File upload failed: URL is null');
        throw new Error('File upload failed: URL is null');
      }
      
      return {
        url: url,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
        message: 'File uploaded successfully',
        error: null,
      };
    } catch (error) {
      Logger.error('Upload error:', error);
      return {
        url: null,
        message: 'File upload failed',
        error: `Upload error: ${error.message}`,
      };
    }
  }
}
