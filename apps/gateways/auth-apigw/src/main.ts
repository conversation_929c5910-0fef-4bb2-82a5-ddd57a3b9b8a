/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import express from 'express';
import { healthRouter } from './routes/health.routes';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const defaultAllowedOrigins = [
    'http://crm.localhost',
    'http://crm.localhost:3502',
    'http://iaula.crm.localhost',
    'http://iaula.crm.localhost:3502',
    'http://iauoc.crm.localhost',
    'http://iauoc.crm.localhost:3502',
    'http://iausd.crm.localhost',
    'http://iausd.crm.localhost:3502',
    'http://iaulax.crm.localhost',
    'http://iaulax.crm.localhost:3502',
    'http://admin.localhost',
    'http://localhost',
    'http://localhost:3502',
    'https://crm.applygoal.com',
    'https://iaula.crm.applygoal.com',
    'https://iauoc.crm.applygoal.com',
    'https://iausd.crm.applygoal.com',
    'https://iaulax.crm.applygoal.com',
    'https://admin.applygoal.com',
  ];
  const allowedOrigins = (defaultAllowedOrigins.join(','))
    .split(',')
    .map((o) => o.trim())
    .filter(Boolean);

  Logger.log('allowed origins', allowedOrigins);

  app.enableCors({
    origin: (origin, callback) => {
      if (!origin) return callback(null, true);
      if (allowedOrigins.includes(origin)) return callback(null, true);
      return callback(new Error('Not allowed by CORS'), false);
    },
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'Origin',
      'X-Requested-With',
    ],
    credentials: true,
    preflightContinue: false, // Nest handles OPTIONS automatically
    optionsSuccessStatus: 204, // For legacy browsers
  });

  // Add express middleware for health routes
  app.use(express.json());
  app.use('/health', healthRouter);

  // Setup gRPC Microservice
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: '0.0.0.0:50052',
      maxReceiveMessageLength: 50 * 1024 * 1024,
      maxSendMessageLength: 50 * 1024 * 1024,
      loader: {
        // convert int64/uint64 → Number
        longs: Number,
        // you can also tweak enums, defaults, arrays, objects...
        enums: String,
        defaults: true,
        arrays: true,
        objects: true,
      },
    },
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix, {
    exclude: ['/api/metrics'], // Exclude metrics from global prefix to avoid double prefix
  });

  const port = process.env.PORT || 4006;

  // Start microservices
  await app.startAllMicroservices();

  await app.listen(port);
  Logger.log(
    `🚀 Application is running on: http://localhost:${port}/${globalPrefix}`
  );
  Logger.log(`📊 Metrics available at: http://localhost:${port}/api/metrics`);
}

bootstrap();
