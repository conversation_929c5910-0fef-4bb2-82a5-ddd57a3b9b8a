import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  Param,
  HttpStatus,
  HttpException,
  Logger
} from '@nestjs/common';
import {
  CreateProgramLevelIntakeFeeRequest,
  UpdateProgramLevelIntakeFeeRequest,
  DeleteProgramLevelIntakeFeeRequest,
  GetProgramLevelIntakeFeeRequest,
  ListProgramLevelIntakeFeeByUniversityRequest
} from './fee.interface';
import { FeeService } from './fee.service';
import { firstValueFrom } from 'rxjs';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('program-level-intake-fees')
export class FeeController {
  private readonly logger = new Logger(FeeController.name);

  constructor(private readonly feeService: FeeService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() payload: CreateProgramLevelIntakeFeeRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(this.feeService.createFee(payload));
    } catch (error) {
      this.logger.error('Create fee failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(@Query() query: ListProgramLevelIntakeFeeByUniversityRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const pageSize = Number(query.pageSize) > 0 ? Number(query.pageSize) : 10;

      return await firstValueFrom(
        this.feeService.listFees({
          universityId: query.universityId,
          page,
          pageSize
        })
      );
    } catch (error) {
      this.logger.error('List fees failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put(':id')
  @Put()
  async update(
    @Param('id') id: string,
    @Body() payload: UpdateProgramLevelIntakeFeeRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      if (!payload.universityId) {
        payload.universityId = Number(id);
      }
      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(
        this.feeService.updateFee({ ...payload, id })
      );
    } catch (error) {
      this.logger.error('Update fee failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get(':id')
  async get(@Param('id') id: string) {
    try {
      return await firstValueFrom(this.feeService.getFee({ id }));
    } catch (error) {
      this.logger.error('Get fee failed', error.stack);
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(':id')
  async delete(@Param('id') id: string, @CurrentUser('id') userId: number) {
    try {
      return await firstValueFrom(
        this.feeService.deleteFee({ id, userId: String(userId) })
      );
    } catch (error) {
      this.logger.error('Delete fee failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
