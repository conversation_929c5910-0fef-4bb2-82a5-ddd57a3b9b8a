import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  CreateUniversityGeneralRequest,
  UpdateUniversityGeneralRequest,
  DeleteUniversityGeneralRequest,
  GetUniversityGeneralRequest
} from './university-general.interface';
import { UniversityGeneralService } from './university-general.service';
import { firstValueFrom } from 'rxjs';
import {
  Permissions,
  Public
} from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('university-general')
export class UniversityGeneralController {
  private readonly logger = new Logger(UniversityGeneralController.name);

  constructor(private readonly generalService: UniversityGeneralService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() body: CreateUniversityGeneralRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];
      
      return await firstValueFrom(this.generalService.create(body));
    } catch (error) {
      this.logger.error('Create university general info failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put()
  async update(
    @Body() body: UpdateUniversityGeneralRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];
      
      return await firstValueFrom(this.generalService.update(body));
    } catch (error) {
      this.logger.error('Update university general info failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete()
  async delete(
    @Query() query: DeleteUniversityGeneralRequest,
    @CurrentUser('id') userId: number
  ) {
    try {
      // Add userId
      query.userId = String(userId);
      
      return await firstValueFrom(this.generalService.delete(query));
    } catch (error) {
      this.logger.error('Delete university general info failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Get()
  async get(@Query() query: GetUniversityGeneralRequest) {
    try {
      return await firstValueFrom(this.generalService.get(query));
    } catch (error) {
      this.logger.error('Get university general info failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

}
