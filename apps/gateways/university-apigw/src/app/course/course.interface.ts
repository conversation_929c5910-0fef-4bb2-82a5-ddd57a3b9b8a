import { Observable } from 'rxjs';

export interface Course {
  id?: string;
  userId?: string;
  userRole?: string;
  universityId: number;
  courseTitle: string;
  fieldOfStudy: number; // Keep this aligned with proto's fieldOfStudy
  format: string;
  programLevelId: number;

  lastAcademic: string;
  minimumGpa: string;
  courseRank: string;
  acceptanceRate: string;
  lectureLanguage: string;
  additionalRequirements: string;

  testScores: TestScore[];
  feeCategories: FeeCategory[];

  createdAt?: string;
  updatedAt?: string;
}

export interface TestScore {
  id: number;
  test: string;
  testScore: string; // JSON string
  isNew?: boolean;   // ✅ Optional to match proto
}

export interface FeeCategory {
  id: number;
  universityId: number;
  feeTitle: string;
  programLevelId: number;
  intakeIds: number[];

  applicationYearStart: string;
  applicationYearEnd: string;
  tuitionFee: number;
  applicationFee: number;
  applicationFeeChargedByUniversity: number;
  applicationFeeChargedToStudent: number;
  paymentDueInDays: number;
  feeEffectiveDate: string;

  isActive: boolean;
  isRefundableToStudent: boolean;
  isVisibleToStudent: boolean;
  isNew?: boolean;   // ✅ Optional to match proto
}

export interface UpdateCourseRequest {
  id?: string;
  userId?: string;
  userRole?: string;
  courseTitle: string;
  fieldOfStudy: number; // Keep this aligned with proto's fieldOfStudy
  format: string;
  programLevelId: number;

  lastAcademic: string;
  minimumGpa: string;
  courseRank: string;
  acceptanceRate: string;
  lectureLanguage: string;
  additionalRequirements: string;

  testScores: TestScore[];
  feeCategories: FeeCategory[];

  createdAt?: string;
  updatedAt?: string;
}

export interface GetCourseRequest {
  id: string;
}

export interface DeleteCourseRequest {
  id: string;
  userId?: string;
  userRole?: string;
}

export interface ListCourseRequest {
  universityId: number;
  page?: number;
  pageSize?: number;
}

export interface CourseResponse {
  status: number;
  message: string;
  data: Course;
  error?: ErrorResponse;
}

export interface CourseListResponse {
  status: number;
  message: string;
  data: Course[];
  error?: ErrorResponse;
}

export interface DeleteCourseResponse {
  status: number;
  message: string;
  success: boolean;
  error?: ErrorResponse;
}

export interface ErrorResponse {
  code?: string;
  message?: string;
  details?: string;
}

export interface CourseGrpcService {
  createCourse(course: Course): Observable<CourseResponse>;
  updateCourse(request: UpdateCourseRequest): Observable<CourseResponse>;
  getCourse(request: GetCourseRequest): Observable<CourseResponse>;
  deleteCourse(request: DeleteCourseRequest): Observable<DeleteCourseResponse>;
  listCourse(request: ListCourseRequest): Observable<CourseListResponse>;
  GetCourseRequirement(request: any): Observable<any>;
}
