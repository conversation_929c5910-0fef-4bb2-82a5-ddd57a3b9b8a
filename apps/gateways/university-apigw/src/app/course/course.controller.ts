import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  Param,
  HttpStatus,
  HttpException,
  Logger
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  UpdateCourseRequest,
  ListCourseRequest,
  Course,
  DeleteCourseRequest
} from './course.interface';
import { CourseService } from './course.service';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('courses')
export class CourseController {
  private readonly logger = new Logger(CourseController.name);

  constructor(private readonly courseService: CourseService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() payload: Course,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(this.courseService.createCourse(payload));
    } catch (error) {
      this.logger.error('Create course failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() payload: UpdateCourseRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      payload.id = id;
      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(
        this.courseService.updateCourse({ ...payload })
      );
    } catch (error) {
      this.logger.error('Update course failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Get('course-requirement')
  async getCourseRequirements(
    @Query('courseId') courseId: string,
    @Query('programLevelId') programLevelId: string,
    // @CurrentUser('id') userId: number,
    // @CurrentUser('roles') roles: string[]
  ) {
    try {
      this.logger.log(`Getting course requirements for courseId: ${courseId}, programLevelId: ${programLevelId}`);

      const request = {
        courseId: Number(courseId),
        programLevelId: Number(programLevelId),
        // userId: String(userId),
        // userRole: roles[0]
      };

      return await firstValueFrom(
        this.courseService.getCourseRequirement(request)
      );
    } catch (error) {
      this.logger.error('Get course requirements failed', error.stack);
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }



  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Get(':id')
  async get(@Param('id') id: string) {
    try {
      return await firstValueFrom(this.courseService.getCourse({ id }));
    } catch (error) {
      this.logger.error('Get course failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Get()
  async list(@Query() query: ListCourseRequest) {
    try {
      const page = Number(query.page) > 0 ? Number(query.page) : 1;
      const pageSize = Number(query.pageSize) > 0 ? Number(query.pageSize) : 10;
      return await firstValueFrom(
        this.courseService.listCourses({
          universityId: query.universityId,
          page,
          pageSize
        })
      );
    } catch (error) {
      this.logger.error('List courses failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(':id')
  async delete(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      const request: DeleteCourseRequest = {
        id: id,
        userId: String(userId),
        userRole: roles[0]
      };
      return await firstValueFrom(this.courseService.deleteCourse(request));
    } catch (error) {
      this.logger.error('Delete course failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
