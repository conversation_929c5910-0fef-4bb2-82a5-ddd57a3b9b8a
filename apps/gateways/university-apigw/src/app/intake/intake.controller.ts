import {
  Controller,
  Post,
  Put,
  Delete,
  Get,
  Body,
  Query,
  Logger,
  HttpException,
  HttpStatus,
  Param
} from '@nestjs/common';
import {
  CreateIntakeRequest,
  UpdateIntakeRequest,
  DeleteIntakeRequest,
  ListIntakesRequest
} from './intake.interface';
import { IntakeService } from './intake.service';
import { firstValueFrom } from 'rxjs';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('intakes')
export class IntakeController {
  private readonly logger = new Logger(IntakeController.name);

  constructor(private readonly intakeService: IntakeService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() body: CreateIntakeRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];

      return await firstValueFrom(this.intakeService.createIntake(body));
    } catch (error) {
      this.logger.error('Create intake failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put()
  async update(
    @Body() body: UpdateIntakeRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];

      return await firstValueFrom(this.intakeService.updateIntake(body));
    } catch (error) {
      this.logger.error('Update intake failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(':id')
  async remove(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      const request: DeleteIntakeRequest = {
        intakeId: +id,
        userId: String(userId),
        userRole: roles[0]
      };

      return await firstValueFrom(this.intakeService.deleteIntake(request));
    } catch (error) {
      this.logger.error('Delete intake failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(@Query() query: ListIntakesRequest) {
    try {
      return await firstValueFrom(this.intakeService.listIntakes(query));
    } catch (error) {
      this.logger.error('List intakes failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
