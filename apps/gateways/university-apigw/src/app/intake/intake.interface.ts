import { Observable } from 'rxjs';

// Request payloads
export interface IntakeDate {
  studentType: string;
  applicationOpen: string;
  applicationEnd: string;
  enrollmentDeadline: string;
  actions: string;
}

export interface IntakeItem {
  id?: number;
  name: string;
  startDate: string;
  endDate: string;
  classStartDate: string;
  applicationDates: IntakeDate[];
}

export interface CreateIntakeRequest {
  universityId: number;
  userId?: string;
  userRole?: string;
  intakes: IntakeItem[];
}

export interface UpdateIntakeRequest {
  universityId: number;
  userRole?: string;
  userId?: string;
  intakes: IntakeItem[];
}

export interface DeleteIntakeRequest {
  intakeId: number;
  userId?: string;
  userRole?: string;
}

export interface ListIntakesRequest {
  universityId: number;
  page?: number;
  limit?: number;
  userId?: string;
}

// Response objects
export interface Intake {
  id: number;
  universityId: number;
  name: string;
  startDate: string;
  endDate: string;
  classStartDate: string;
  applicationDates: IntakeDate[];
  createdAt: string;
  updatedAt: string;
}

export interface ErrorResponse {
  details: string;
}

export interface IntakeResponse {
  status: number;
  message: string;
  data: Intake | IntakeDate;
  error: ErrorResponse;
}

export interface DeleteIntakeResponse {
  status: number;
  message: string;
  success: boolean;
  error: ErrorResponse;
}

export interface Intakes{
  id: number;
  universityId: number;
  name: string;
  startDate: string;
  endDate: string;
  classStartDate: string;
  applicationDates: IntakeDate[];
  createdAt: string;
  updatedAt: string;
}


// For list all intakes
export interface ListIntakesResponse {
  status: number;
  message: string;
  data: Intakes[];            // <-- updated to match `repeated IntakeData data` in proto
  error?: ErrorResponse;
}

export interface CreateIntakesResponse {
  status: number;
  message: string;
  data: {
    intakes: Intake[];
  };
  error: ErrorResponse;
}

// gRPC Service Interface
export interface IntakeGrpcService {
  createIntake(data: CreateIntakeRequest): Observable<CreateIntakesResponse>;
  getIntake(data: { id: number }): Observable<IntakeResponse>;
  updateIntake(data: UpdateIntakeRequest): Observable<IntakeResponse>;
  deleteIntake(data: DeleteIntakeRequest): Observable<DeleteIntakeResponse>;
  listIntakesByUniversity(data: ListIntakesRequest): Observable<ListIntakesResponse>;
}
