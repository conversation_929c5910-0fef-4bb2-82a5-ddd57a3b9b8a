import { Observable } from 'rxjs';

// ================== Entity ==================

export interface UniversityContactDetails {
  id: string;
  contactName: string;
  designation: string;
  email: string;
  contactNumber: string;
  alternativeEmail: string;
  createdAt: string;
  updatedAt: string;
}

// ================== Request DTOs ==================

export interface CreateUniversityContactRequest {
  userId?: string;
  userRole?: string;
  universityId: string;
  contactName: string;
  designation: string;
  email: string;
  contactNumber: string;
  alternativeEmail: string;
}

export interface UpdateUniversityContactRequest {
  id: string;
  contactName: string;
  designation: string;
  email: string;
  contactNumber: string;
  alternativeEmail: string;
  userId?: string;
  userRole?: string;
}

export interface GetUniversityContactRequest {
  universityId: string;
}

export interface DeleteUniversityContactRequest {
  id: string;
}

export interface ListUniversityContactRequest {
  universityId: string;
  page: number;
  pageSize: number;
}

// ================== Error DTO ==================

export interface ErrorResponse {
  validationErrors?: any[];
  code?: string;
  message: string;
  details?: string;
}

// ================== Response DTOs ==================

export interface UniversityContactResponse {
  status: number;
  message: string;
  data: UniversityContactDetails;
  error: ErrorResponse | null;
}

export interface UniversityContactListResponse {
  status: number;
  message: string;
  data: UniversityContactDetails[];
  error: ErrorResponse | null;
}

export interface DeleteUniversityContactResponse {
  status: number;
  message: string;
  error: ErrorResponse | null;
}

// ================== gRPC Service Interface ==================

export interface UniversityContactGrpcService {
  createUniversityContact(
    data: CreateUniversityContactRequest
  ): Observable<UniversityContactResponse>;

  updateUniversityContact(
    data: UpdateUniversityContactRequest
  ): Observable<UniversityContactResponse>;

  getUniversityContact(
    data: GetUniversityContactRequest
  ): Observable<UniversityContactResponse>;

  listUniversityContact(
    data: ListUniversityContactRequest
  ): Observable<UniversityContactListResponse>;

  deleteUniversityContact(
    data: DeleteUniversityContactRequest
  ): Observable<DeleteUniversityContactResponse>;
}
