import { Injectable, Logger } from '@nestjs/common';
import {
  UniversityContactGrpcService,
  CreateUniversityContactRequest,
  UpdateUniversityContactRequest,
  DeleteUniversityContactRequest,
  GetUniversityContactRequest,
  ListUniversityContactRequest,
  UniversityContactResponse,
  UniversityContactListResponse,
  DeleteUniversityContactResponse
} from './university-contact.interface';
import { Observable } from 'rxjs';
import { UniversityClientService } from '../app.service';

@Injectable()
export class UniversityContactService {
  private readonly logger = new Logger(UniversityContactService.name);

  constructor(private readonly universityClient: UniversityClientService) {}

  private get grpcService(): UniversityContactGrpcService {
    return this.universityClient.getService<UniversityContactGrpcService>('UniversityService');
  }

  create(data: CreateUniversityContactRequest): Observable<UniversityContactResponse> {
    return this.grpcService.createUniversityContact(data);
  }

  update(data: UpdateUniversityContactRequest): Observable<UniversityContactResponse> {
    return this.grpcService.updateUniversityContact(data);
  }

  get(data: GetUniversityContactRequest): Observable<UniversityContactResponse> {
    return this.grpcService.getUniversityContact(data);
  }

  list(data: ListUniversityContactRequest): Observable<UniversityContactListResponse> {
    return this.grpcService.listUniversityContact(data);
  }

  delete(data: DeleteUniversityContactRequest): Observable<DeleteUniversityContactResponse> {
    return this.grpcService.deleteUniversityContact(data);
  }
}
