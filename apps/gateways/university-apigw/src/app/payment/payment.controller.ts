import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Query,
  HttpException,
  HttpStatus,
  Logger,
  Param
} from '@nestjs/common';
import {
  CreateBankDetailsRequest,
  UpdateBankDetailsRequest,
  // GetBankDetailsRequest,
  DeleteBankDetailsRequest,
  ListBankDetailsRequest
} from './payment.interface';
import { PaymentDetailsService } from './payment.service';
import { firstValueFrom } from 'rxjs';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';
@Controller('university-bank-details')
export class PaymentDetailsController {
  private readonly logger = new Logger(PaymentDetailsController.name);

  constructor(private readonly bankDetailsService: PaymentDetailsService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() body: CreateBankDetailsRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];

      return await firstValueFrom(this.bankDetailsService.create(body));
    } catch (error) {
      this.logger.error('Create bank details failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put(":id")
  async update(
    @Body() body: UpdateBankDetailsRequest,
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      body.id = id;
      body.userId = String(userId);
      body.userRole = roles[0];
      return await firstValueFrom(this.bankDetailsService.update(body));
    } catch (error) {
      this.logger.error('Update bank details failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }


  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  // @Delete("")
  // async delete(@Query() query: DeleteBankDetailsRequest) {
  //   try {
  //     return await firstValueFrom(this.bankDetailsService.delete(query));
  //   } catch (error) {
  //     this.logger.error('Delete bank details failed', error.stack);
  //     throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
  //   }
  // }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(@Query() query: ListBankDetailsRequest) {
    try {
      const queryWithDefaults = {
        universityId: query.universityId,
        page: query.page || 1, // Default to page 1
        pageSize: query.pageSize || 10 // Default to pageSize 10
      };
      return await firstValueFrom(
        this.bankDetailsService.list(queryWithDefaults)
      );
    } catch (error) {
      this.logger.error('List bank details failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
