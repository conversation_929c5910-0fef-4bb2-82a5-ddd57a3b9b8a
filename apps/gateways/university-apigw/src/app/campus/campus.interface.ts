import { Observable } from 'rxjs';

export interface Campus {
  id?: number;
  campusName: string;
  programLevelIds: number[];
  address: string;
  countryId: number;
  state: string;
  city: string;
  postalCode: string;
  contactNumber: string;
  email: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateCampusRequest
  extends Omit<Campus, 'id' | 'createdAt' | 'updatedAt'> {
  universityId: number; // ✅ Required now
  userId: string;
  userRole: string;
}

export interface UpdateCampusRequest extends Partial<CreateCampusRequest> {
  id: number;
  // universityId: number;
  userId: string;
  userRole: string;
}

export interface GetCampusRequest {
  id: number;
}

export interface DeleteCampusRequest {
  id: number;
  userId: string;
}

export interface ListCampusRequest {
  universityId: number;
  page?: number;
  pageSize?: number;
}

export interface ProgramLevelCourses {
  programLevelId: number;
  programLevelName: string;
  courses: CourseResponseForList[];
}

export interface CampusWithPrograms {
  id: number;
  campusName: string;
  address: string;
  countryId: number;
  state: string;
  city: string;
  postalCode: string;
  contactNumber: string;
  email: string;
  programCourses: ProgramLevelCourses[];
}

export interface CourseResponseForList {
  id: number;
  courseTitle: string;
  fieldOfStudy: number;
  format: string;
  programLevelId: number;
  lastAcademic: string;
  minimumGpa: string;
  courseRank: string;
  acceptanceRate: string;
  lectureLanguage: string;
  additionalRequirements: string;
  testScores: TestScoreResponse[];
  feeCategories: FeeCategoryResponse[];
  createdAt: string;
  updatedAt: string;
}

export interface TestScoreResponse {
  id: number;
  test: string;
  testScore: string;
}

export interface FeeCategoryResponse {
  id: number;
  feeTitle: string;
  applicationYearStart: string;
  applicationYearEnd: string;
  tuitionFee: number;
  applicationFee: number;
  applicationFeeChargedByUniversity: number;
  applicationFeeChargedToStudent: number;
  paymentDueInDays: number;
  feeEffectiveDate: string;
  isActive: boolean;
  isRefundableToStudent: boolean;
  isVisibleToStudent: boolean;
  programLevelIntake: {
    intake: Intake;
  };
}

export interface Intake {
  id: number;
  name: string;
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface ErrorResponse {
  code?: string;
  message?: string;
  details?: string;
}

export interface CampusResponse {
  status: number;
  message: string;
  data: Campus;
  error?: ErrorResponse;
}

export interface CampusListResponse {
  status: number;
  message: string;
  data: CampusWithPrograms[];
  error?: ErrorResponse;
}

export interface DeleteCampusResponse {
  status: number;
  message: string;
  success: boolean;
  error?: ErrorResponse;
}

export interface CampusGrpcService {
  createCampus(data: CreateCampusRequest): Observable<CampusResponse>;
  updateCampus(data: UpdateCampusRequest): Observable<CampusResponse>;
  getCampus(data: GetCampusRequest): Observable<CampusResponse>;
  deleteCampus(data: DeleteCampusRequest): Observable<DeleteCampusResponse>;
  getCampusList(data: ListCampusRequest): Observable<CampusListResponse>;
}
