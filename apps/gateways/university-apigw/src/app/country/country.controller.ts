// country.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  Logger,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { CountryService } from './country.service';
import { firstValueFrom } from 'rxjs';
import {
  CreateCountryRequest,
  UpdateCountryRequest
} from './country.interface';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('countries')
export class CountryController {
  private readonly logger = new Logger(CountryController.name);

  constructor(private readonly countryService: CountryService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() body: CreateCountryRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];

      return await firstValueFrom(this.countryService.createCountry(body));
    } catch (error) {
      this.logger.error('Create failed', error.stack);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put()
  async update(
    @Body() body: UpdateCountryRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];

      return await firstValueFrom(this.countryService.updateCountry(body));
    } catch (error) {
      this.logger.error('Update failed', error.stack);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(
    @Query('universityId') universityId: number
    // @Query('page') page = 1,
    // @Query('limit') limit = 10
  ) {
    try {
      return await firstValueFrom(
        this.countryService.listCountriesByUniversity(universityId)
      );
    } catch (error) {
      this.logger.error('List failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  // @Get(':id')
  // async get(@Param('id') id: number) {
  //   try {
  //     return await firstValueFrom(this.countryService.getCountry(id));
  //   } catch (error) {
  //     this.logger.error('Get failed', error.stack);
  //     throw new HttpException(error.message, HttpStatus.NOT_FOUND);
  //   }
  // }

  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  // @Delete(':id')
  // async delete(
  //   @Param('id') id: number,
  //   @Query('universityId') universityId: number,
  //   @CurrentUser('id') userId: number
  // ) {
  //   try {
  //     return await firstValueFrom(
  //       this.countryService.deleteCountry(id, String(universityId),userId)
  //     );
  //   } catch (error) {
  //     this.logger.error('Delete failed', error.stack);
  //     throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
  //   }
  // }
}
