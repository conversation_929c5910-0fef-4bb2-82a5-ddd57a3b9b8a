import { Observable } from 'rxjs';

export interface Timestamp {
  seconds: number;
  nanos: number;
}

export interface CreateUniversityRequest {
  userId: string;
  userRole: string;
  name: string;
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  about: string;
  type: string;
  website: string;
  foundedOn: Timestamp | string;
  institutionCode: string;
  logo?: string;
  email: string;
  primaryContactNumber: string;
}

export interface GetUniversityRequest {
  id: number;
}

export interface UpdateUniversityRequest {
  id: number;
  name?: string;
  address?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  about?: string;
  type?: string;
  website?: string;
  foundedOn?: Timestamp | string;
  institutionCode?: string;
  logo?: string;
  email?: string;
  primaryContactNumber?: string;
  userId?: string;
  userRole?: string;
}

export interface DeleteUniversityRequest {
  id: number;
  userId?: string; // ✅ NEW
}

export interface UniversityResponse {
  id: number;
  name: string;
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  about: string;
  type: string;
  website: string;
  foundedOn: string;
  institutionCode: string;
  logo: string;
  email: string;
  primaryContactNumber: string;
  createdAt: string;
  updatedAt: string;
}

export interface ListUniversitiesRequest {
  page?: number;
  limit?: number;
  isActive?: string;
  search?: string;
  type?: string;
  country?: string;
  state?: string;
  city?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ListUniversitiesResponse {
  status: number;
  message: string;
  data: {
    total: number;
    page: number;
    limit: number;
    universities: UniversityResponse[];
  };
  error: any;
}

export type UniversityListForStudentApplicationRequest = object

export interface UniversityListForStudentApplicationResponse {
  status: number;
  message: string;
  universities: UniversityItemForStudentApplication[]; // flattened according to proto
  error: any;
}

export interface UniversityItemForStudentApplication {
  id: number;
  title: string;
  countries: CountryItemForStudentApplication[];
}

export interface CountryItemForStudentApplication {
  id: number;
  title: string;
  campuses: CampusItemForStudentApplication[];
}

export interface CampusItemForStudentApplication {
  id: number;
  title: string;
  programLevels: ProgramLevelItemForStudentApplication[];
}

export interface ProgramLevelItemForStudentApplication {
  id: number;
  title: string;
  intakes: IntakeItemForStudentApplication[];
}

export interface IntakeItemForStudentApplication {
  id: number;
  title: string;
  startDate: string;
  endDate: string;
  classStartDate: string;
  applicationTypes: string;
  applicationProcessDuration: string;
  courses: CourseItemForStudentApplication[];
}

export interface CourseItemForStudentApplication {
  id: number;
  title: string;
  lastAcademic: string;
}
export interface UniversityGrpcService {
  createUniversity(
    data: CreateUniversityRequest
  ): Observable<UniversityResponse>;
  getUniversity(data: GetUniversityRequest): Observable<UniversityResponse>;
  updateUniversity(
    data: UpdateUniversityRequest
  ): Observable<UniversityResponse>;
  deleteUniversity(
    data: DeleteUniversityRequest
  ): Observable<{ success: boolean }>;
  listUniversities(
    data: ListUniversitiesRequest
  ): Observable<ListUniversitiesResponse>;

   universityListForStudentApplication(
    request: UniversityListForStudentApplicationRequest
  ): Observable<UniversityListForStudentApplicationResponse>;
}
