import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { Observable, firstValueFrom } from 'rxjs';
import { UniversityClientService } from '../app.service';
import {
  CreateUniversityRequest,
  UniversityResponse,
  UpdateUniversityRequest,
  ListUniversitiesResponse,
  UniversityGrpcService,
  ListUniversitiesRequest,
  UniversityListForStudentApplicationRequest,
  UniversityListForStudentApplicationResponse,
  ComprehensiveUniversityInformationForStudentRequest,
  ComprehensiveUniversityInformationForStudentResponse,
} from './university.interface';
import { AlumniGrpcService } from '../alumni/alumni.interface';
import { UniversityGeneralGrpcService } from '../general/university-general.interface';

@Injectable()
export class UniversityService {
  private readonly logger = new Logger(UniversityService.name);

  constructor(
    private readonly universityClientService: UniversityClientService
  ) {
    this.logger.log('University service initialized');
  }

  createUniversity(
    data: CreateUniversityRequest
  ): Observable<UniversityResponse> {
    this.logger.log(`Creating university: ${data.name}`);
    return this.universityClientService
      .getService<UniversityGrpcService>('UniversityService')
      .createUniversity(data);
  }


listUniversities(data: ListUniversitiesRequest): Observable<ListUniversitiesResponse> {
  this.logger.log(`Listing universities - page: ${data.page}, limit: ${data.limit}`);
  const response = this.universityClientService
    .getService<UniversityGrpcService>('UniversityService')
    .listUniversities(data);
  return response;
}


  // async getUniversity(id: number): Promise<any> {
  //   this.logger.log(`Getting university with ID: ${id}`);

  //   try {
  //     const universityService =
  //       this.universityClientService.getService<UniversityGrpcService>(
  //         'UniversityService'
  //       );
  //     const alumniService =
  //       this.universityClientService.getService<AlumniGrpcService>(
  //         'UniversityService'
  //       );

  //     // Get university core data
  //     const universityData = await firstValueFrom(
  //       universityService.getUniversity({ id })
  //     );

  //     // Get alumni list
  //     const alumniResponse = await firstValueFrom(
  //       alumniService.GetAlumniList({ universityId: id })
  //     );

  //     // ✅ Final response format
  //     return {
  //       status: 200,
  //       message: 'University information retrieved successfully',
  //       data: {
  //         ...universityData,
  //         alumni: alumniResponse.data || [],
  //       },
  //     };
  //   } catch (error) {
  //     this.logger.error(`Failed to get university with ID ${id}`, error.stack);
  //     throw new InternalServerErrorException(
  //       'Failed to fetch university information'
  //     );
  //   }
  // }




  async getUniversity(id: number): Promise<any> {
    this.logger.log(`Getting university with ID: ${id}`);

    try {
      const universityService =
        this.universityClientService.getService<UniversityGrpcService>(
          'UniversityService'
        );
      const alumniService =
        this.universityClientService.getService<AlumniGrpcService>(
          'UniversityService'
        );
      const generalInformationService =
        this.universityClientService.getService<UniversityGeneralGrpcService>(
          'UniversityService'
        );

      // Fetch both in parallel
      const [universityData, alumniResponse, generalInformation] =
        await Promise.all([
          firstValueFrom(universityService.getUniversity({ id })), // ✅ already the data
          firstValueFrom(alumniService.GetAlumniList({ universityId: id })),
          firstValueFrom(
            generalInformationService.getUniversityGeneral({
              universityId: String(id),
            })
          ),
        ]);

      // university data foramt
      const responseFormat: any = universityData;
      const universityResponse = responseFormat?.data;

      // generation information format

      const {
        id: _gId,
        universityId: _gUniversityId,
        createdAt: _gCreatedAt,
        updatedAt: _gUpdatedAt,
        ...cleanGeneralInformation
      } = generalInformation.data || {};
      return {
        status: 200,
        message: 'University information retrieved successfully',
        data: {
          ...universityResponse,
          generalInformation: cleanGeneralInformation,
          alumni: alumniResponse.data || [],
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get university with ID ${id}`, error.stack);
      throw new InternalServerErrorException(
        'Failed to fetch university information'
      );
    }
  }

  updateUniversity(
    id: number,
    data: Partial<UpdateUniversityRequest>
  ): Observable<UniversityResponse> {
    this.logger.log(`Updating university with ID: ${id}`);
    return this.universityClientService
      .getService<UniversityGrpcService>('UniversityService')
      .updateUniversity({ id, ...data });
  }

  deleteUniversity(id: number, userId: string): Observable<{ success: boolean }> {
    this.logger.log(`Deleting university with ID: ${id}`);
    return this.universityClientService
      .getService<UniversityGrpcService>('UniversityService')
      .deleteUniversity({ id, userId });
  }

  universityListForStudentApplication(
    request: UniversityListForStudentApplicationRequest
  ): Observable<UniversityListForStudentApplicationResponse> {
    this.logger.log('Fetching university list for student application');
    return this.universityClientService
      .getService<UniversityGrpcService>('UniversityService')
      .universityListForStudentApplication(request);
  }

  /**
   * Get comprehensive university information for student service
   * Returns all related university information with intelligent fallback logic
   */
  comprehensiveUniversityInformationForStudent(
    request: ComprehensiveUniversityInformationForStudentRequest
  ): Observable<ComprehensiveUniversityInformationForStudentResponse> {
    this.logger.log(
      `Fetching comprehensive university information for university ${request.universityId}`
    );
    
    if (request.campusId) {
      this.logger.debug(`Including campus data for ID: ${request.campusId}`);
    }
    if (request.programId) {
      this.logger.debug(`Including program data for ID: ${request.programId}`);
    }
    if (request.intakeId) {
      this.logger.debug(`Including intake data for ID: ${request.intakeId}`);
    }
    if (request.courseId) {
      this.logger.debug(`Including course data for ID: ${request.courseId}`);
    }
    
    return this.universityClientService
      .getService<UniversityGrpcService>('UniversityService')
      .comprehensiveUniversityInformationForStudent(request);
  }

  /**
   * Async version of comprehensiveUniversityInformationForStudent for easier usage
   */
  async comprehensiveUniversityInformationForStudentAsync(
    request: ComprehensiveUniversityInformationForStudentRequest
  ): Promise<ComprehensiveUniversityInformationForStudentResponse> {
    this.logger.log(
      `Fetching comprehensive university information (async) for university ${request.universityId}`
    );
    
    try {
      const response = await firstValueFrom(
        this.comprehensiveUniversityInformationForStudent(request)
      );
      
      this.logger.log(
        `Comprehensive information retrieved successfully. University ID: ${response.data?.university?.id}`
      );
      
      return response;
    } catch (error) {
      this.logger.error(
        `Failed to fetch comprehensive university information: ${error.message}`
      );
      throw new InternalServerErrorException(
        'Failed to retrieve comprehensive university information'
      );
    }
  }
}
