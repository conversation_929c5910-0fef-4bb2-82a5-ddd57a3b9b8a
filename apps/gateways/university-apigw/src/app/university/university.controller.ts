import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  Logger,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { UniversityService } from './university.service';
import { firstValueFrom } from 'rxjs';
import {
  CreateUniversityRequest,
  UpdateUniversityRequest,
  UniversityListForStudentApplicationRequest,
  UniversityListForStudentApplicationResponse
} from './university.interface';
import { CurrentUser, Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';

@Controller('universities')
export class UniversityController {
  private readonly logger = new Logger(UniversityController.name);

  constructor(private readonly universityService: UniversityService) {}

  private handleGrpcError(
    operation: string,
    error: any,
    defaultStatus = HttpStatus.INTERNAL_SERVER_ERROR
  ) {
    this.logger.error(`Failed to ${operation}: ${error.message}`, error.stack);
    throw new HttpException(
      error.message || `Failed to ${operation}`,
      error.status || defaultStatus
    );
  }

  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async createUniversity(
    @Body() createUniversityDto: CreateUniversityRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      this.logger.log(`Creating university: ${createUniversityDto.name}`);
      createUniversityDto.userId = String(userId);
      createUniversityDto.userRole = roles[0];

      return await firstValueFrom(
        this.universityService.createUniversity(createUniversityDto)
      );
    } catch (error) {
      this.handleGrpcError('create university', error);
    }
  }

  //Get list of university with search fields.
  // @Permissions(UserPermissions.UM_COURSE_LIST_AND_PROGRAM_INFO)
  @Public()
  @Get()
  async listUniversities(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('isActive') isActive?: string,
    @Query('search') search?: string,
    @Query('type') type?: string,
    @Query('country') country?: string,
    @Query('state') state?: string,
    @Query('city') city?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    try {
      this.logger.log(`Listing universities - page: ${page}, limit: ${limit}`);
      const response = await firstValueFrom(
        this.universityService.listUniversities({
          page,
          limit,
          isActive,
          search,
          type,
          country,
          state,
          city,
          sortBy,
          sortOrder
        })
      );
      return response;
    } catch (error) {
      this.handleGrpcError('list universities', error);
    }
  }

  // @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Public()
  @Get(':id')
  async getUniversity(@Param('id') id: number) {
    try {
      this.logger.log(`Fetching university with ID: ${id}`);
      const response = this.universityService.getUniversity(id);
      // return await firstValueFrom(this.universityService.getUniversity(id));
      return response;
    } catch (error) {
      this.handleGrpcError('get university', error, HttpStatus.NOT_FOUND);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put(':id')
  async updateUniversity(
    @Param('id') id: number,
    @Body() updateUniversityDto: UpdateUniversityRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      this.logger.log(`Updating university with ID: ${id}`);

      // Add userId and userRole
      updateUniversityDto.userId = String(userId);
      updateUniversityDto.userRole = roles[0];

      // Format data with proper timestamp if foundedOn is provided as string
      const formattedData = {
        id,
        ...updateUniversityDto
      };
      return await firstValueFrom(
        this.universityService.updateUniversity(id, formattedData)
      );
    } catch (error) {
      this.handleGrpcError('update university', error);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(':id')
  async deleteUniversity(
    @Param('id') id: number,
    @CurrentUser('id') userId: number
  ) {
    try {
      this.logger.log(`Deleting university with ID: ${id}`);
      // Pass userId in the request
      return await firstValueFrom(
        this.universityService.deleteUniversity(id, String(userId))
      );
    } catch (error) {
      this.handleGrpcError('delete university', error);
    }
  }

  @Public()
  @Get('student-application/list')
  async getUniversityListForStudentApplication() {
    try {
      this.logger.log('Fetching university list for student application');
      const response = await firstValueFrom(
        this.universityService.universityListForStudentApplication({})
      );
      return response;
    } catch (error) {
      this.handleGrpcError('get university list for student application', error);
    }
  }
}
