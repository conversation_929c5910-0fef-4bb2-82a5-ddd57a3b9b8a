import {
  Controller,
  Post,
  Put,
  Delete,
  Get,
  Body,
  Query,
  Logger,
  HttpException,
  HttpStatus,
  Param
} from '@nestjs/common';
import {
  CreateProgramLevelRequest,
  UpdateProgramLevelRequest,
  DeleteProgramLevelRequest,
  GetProgramLevelRequest,
  ListProgramLevelsRequest
} from './programLevel.interface';
import { ProgramLevelService } from './programlevel.service';
import { firstValueFrom } from 'rxjs';
import { CurrentUser, Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
@Controller('program-levels')
export class ProgramLevelController {
  private readonly logger = new Logger(ProgramLevelController.name);

  constructor(private readonly programLevelService: ProgramLevelService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() body: CreateProgramLevelRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = Number(userId);
      body.userRole = roles[0];
      return await firstValueFrom(
        this.programLevelService.createProgramLevel(body)
      );
    } catch (error) {
      this.logger.error('Create program level failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put()
  async update(
    @Body() body: UpdateProgramLevelRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      body.userId = Number(userId);
      body.userRole = roles[0];
      return await firstValueFrom(
        this.programLevelService.updateProgramLevel(body)
      );
    } catch (error) {
      this.logger.error('Update program level failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(':id')
  async remove(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      const query: DeleteProgramLevelRequest = {
        id: Number(id),
        userId: String(userId),
        userRole: roles[0],
       
      };
      return await firstValueFrom(
        this.programLevelService.deleteProgramLevel(query)
      );
    } catch (error) {
      this.logger.error('Delete program level failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async list(@Query() query: ListProgramLevelsRequest) {
    try {
      return await firstValueFrom(
        this.programLevelService.listProgramLevels(query)
      );
    } catch (error) {
      this.logger.error('List program levels failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get('one')
  async getOne(@Query() query: GetProgramLevelRequest) {
    try {
      return await firstValueFrom(
        this.programLevelService.getProgramLevel(query)
      );
    } catch (error) {
      this.logger.error('Get program level failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
