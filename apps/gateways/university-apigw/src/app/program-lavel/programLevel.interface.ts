import { Observable } from 'rxjs';

export interface ProgramLevelTestScore {
  test: string;
  testScore: string;
}

export interface ApplicationStep {
  title: string;
  required: boolean;
  weight: number;
}

// ------------------------
// Request Types
// ------------------------

export interface CreateProgramLevelRequest {
  universityId: number;
  userId?: number;
  userRole?: string;
  programLevelName: string;
  durationNumber: number;
  durationType: string;
  intake: number[];
  testScore: ProgramLevelTestScore[];
  applicationStep: ApplicationStep[];
}

export interface UpdateProgramLevelRequest extends CreateProgramLevelRequest {
  id: number;
}
export interface DeleteProgramLevelRequest {
  id: number;
  userId?: string;
  userRole?: string;
}

export interface GetProgramLevelRequest {
  id: number;
}


export interface ListProgramLevelsRequest {
  universityId: number;
  page?: number;
  limit?: number;
  userId?: string;
}

// ------------------------
// Response Types
// ------------------------

export interface ErrorResponse {
  details: string;
}
// ========== ALSO UPDATE YOUR INTERFACE ==========
export interface StudentType {
  studentType: string;
  applicationOpen: string;
  applicationDeadline: string;
  enrollmentDeadline: string;
}
// Update your intake type in the interface
export interface IntakeInfo {
  id: number;
  name: string;
  startDate: string;
  endDate: string;
  studentTypes: StudentType[]; // <-- include this
}

export interface ProgramLevel {
  id: number;
  universityId: number;
  programLevelName: string;
  durationNumber: number;
  durationType: string;
  intake: IntakeInfo[];
  testScore: ProgramLevelTestScore[];
  applicationStep: ApplicationStep[];
  createdAt: string;
  updatedAt: string;
}

export interface ProgramLevelResponse {
  status: number;
  message: string;
  data?: ProgramLevel;
  error?: ErrorResponse;
}

export interface ProgramLevelListResponse {
  status: number;
  message: string;
  data?: ProgramLevel[];
  error?: ErrorResponse;
}

export interface DeleteProgramLevelResponse {
  status: number;
  message: string;
  success: boolean;
  error?: ErrorResponse;
}

// ------------------------
// gRPC Service Interface
// ------------------------

export interface ProgramLevelGrpcService {
  createProgramLevel(
    data: CreateProgramLevelRequest
  ): Observable<ProgramLevelResponse>;
  updateProgramLevel(
    data: UpdateProgramLevelRequest
  ): Observable<ProgramLevelResponse>;
  getProgramLevel(
    data: GetProgramLevelRequest
  ): Observable<ProgramLevelResponse>;
  deleteProgramLevel(
    data: DeleteProgramLevelRequest
  ): Observable<DeleteProgramLevelResponse>;
  listProgramLevelByUniversity(
    data: ListProgramLevelsRequest
  ): Observable<ProgramLevelListResponse>;
}
