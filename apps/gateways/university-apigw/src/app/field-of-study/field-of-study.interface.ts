import { Observable } from 'rxjs';

// ---------- Entity ----------

export interface FieldOfStudy {
  id: number;
  universityId: number;
  userId?: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

// ---------- Request DTOs ----------

export interface CreateFieldOfStudyRequest {
  universityId: number;
  userId?: string;
  userRole?: string;
  names: string[];  // Changed to array of strings to match proto repeated string names
}

export interface UpdateFieldOfStudyRequest {
  id: number;
  userId?: string;
  userRole?: string;
  name: string;  // Changed to array of strings to match proto repeated string names
}

export interface DeleteFieldOfStudyRequest {
  id: number;
  userId?: string;
  userRole?: string;
}

export interface GetFieldOfStudyRequest {
  id: number;
}

export interface ListFieldOfStudyRequest {
  universityId: number;
  page?: number;
  limit?: number;
}

// ---------- Response DTOs ----------

export interface ErrorResponse {
  details: string;
}

export interface FieldOfStudyResponse {
  status: number;
  message: string;
  data: FieldOfStudy;
  error?: ErrorResponse;
}

export interface ListFieldOfStudyResponse {
  status: number;
  message: string;
  data: FieldOfStudy[];
  error?: ErrorResponse;
}

export interface DeleteFieldOfStudyResponse {
  status: number;
  message: string;
  success: boolean;
  error?: ErrorResponse;
}

// ---------- gRPC Service Interface ----------

export interface FieldsOfStudyGrpcService {
  createFieldOfStudy(data: CreateFieldOfStudyRequest): Observable<ListFieldOfStudyResponse>;  // Changed return type since creating multiple
  updateFieldOfStudy(data: UpdateFieldOfStudyRequest): Observable<ListFieldOfStudyResponse>;  // Changed return type since updating multiple
  deleteFieldOfStudy(data: DeleteFieldOfStudyRequest): Observable<DeleteFieldOfStudyResponse>;
  getFieldOfStudy(data: GetFieldOfStudyRequest): Observable<FieldOfStudyResponse>;
  listFieldOfStudyByUniversity(data: ListFieldOfStudyRequest): Observable<ListFieldOfStudyResponse>;
}