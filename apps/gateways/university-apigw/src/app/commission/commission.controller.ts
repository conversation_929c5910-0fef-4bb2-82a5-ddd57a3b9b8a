// commission.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { CommissionService } from './commission.service';
import { firstValueFrom } from 'rxjs';
import {
  CreateCommissionRequest,
  UpdateCommissionRequest,
} from './commission.interface';
import { Permissions, Public } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';
import { CurrentUser } from '@apply-goal-backend/auth';

@Controller('commissions')
export class CommissionController {
  private readonly logger = new Logger(CommissionController.name);

  constructor(private readonly commissionService: CommissionService) {}
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Post()
  async create(
    @Body() body: CreateCommissionRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];
      
      return await firstValueFrom(this.commissionService.createCommission(body));
    } catch (error) {
      this.logger.error('Create commission failed', error.stack);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Get()
  async get(
    @Query('universityId') universityId: string,
  ) {
    try {
      return await firstValueFrom(
        this.commissionService.getCommission(universityId)
      );
    } catch (error) {
      this.logger.error('Get commission failed', error.stack);
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Put()
  async update(
    @Body() body: UpdateCommissionRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      // Add userId and userRole
      body.userId = String(userId);
      body.userRole = roles[0];
      
      return await firstValueFrom(
        this.commissionService.updateCommission(body)
      );
    } catch (error) {
      this.logger.error('Update commission failed', error.stack);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
  @Permissions(UserPermissions.UM_ADD_UPDATE_UNIVERSITY_PROFILES)
  @Delete(":id")
  async delete(
    @Param('id') commissionId: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {

      return await firstValueFrom(
        this.commissionService.deleteCommission(commissionId, String(userId), roles[0])
      );
    } catch (error) {
      this.logger.error('Delete commission failed', error.stack);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  // @Get()
  // async list(
  //   @Query('universityId') universityId: string,
  //   @Query('userId') userId: string,
  //   @Query('page') page = 1,
  //   @Query('pageSize') pageSize = 10
  // ) {
  //   try {
  //     return await firstValueFrom(this.commissionService.listCommissions(universityId, userId, page, pageSize));
  //   } catch (error) {
  //     this.logger.error('List commissions failed', error.stack);
  //     throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
  //   }
  // }
}
