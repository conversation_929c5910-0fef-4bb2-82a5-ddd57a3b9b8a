/* eslint-disable no-constant-binary-expression */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
  Client,
  ClientGrpc,
  RpcException,
  Transport
} from '@nestjs/microservices';
import { join } from 'path';
import { firstValueFrom, Observable, catchError, of } from 'rxjs';

// Student Service interfaces
interface StudentService {
  createStudent(data: any): Observable<any>;
  getStudent(data: any): Observable<any>;
  getStudentByEmail(data: any): Observable<any>;
  checkStudentExistsByEmail(data: any): Observable<any>;
  updateStudent(data: any): Observable<any>;
  deleteStudent(data: any): Observable<any>;
  listStudents(data: any): Observable<any>;
  enrollInCourse(data: any): Observable<any>;
  dropCourse(data: any): Observable<any>;
  getEnrollments(data: any): Observable<any>;
  updateGrades(data: any): Observable<any>;
  getAcademicProgress(data: any): Observable<any>;
  getTranscript(data: any): Observable<any>;
  createOrUpdateStudentAcademic(data: any): Observable<any>;
  getStudentAcademic(data: any): Observable<any>;
  createApplication(data: any): Observable<any>;
  getApplication(data: any): Observable<any>;
  getApplicationsByStudent(data: any): Observable<any>;
  updateApplication(data: any): Observable<any>;
  deleteApplication(data: any): Observable<any>;
  listApplications(data: any): Observable<any>;
  getApplicationProgress(data: any): Observable<any>;
  generateCalDocument(data: any): Observable<any>;
  updateStudentApplicationType(data: any): Observable<any>;
  getApplicationRequirements(data: any): Observable<any>;
  updateApplicationRequirement(data: any): Observable<any>;
  updateApplicationStage(data: any): Observable<any>;
  createApplicationRequirements(data: any): Observable<any>;
  createApplicationNote(data: any): Observable<any>;
  getApplicationNotes(data: any): Observable<any>;
  cleanupDuplicateStudents(data: any): Observable<any>;
  generateAdmDocument(data: any): Observable<any>;
  generateAdmissionPortfolio(data: any): Observable<any>;
  getDashboardDataByCampus(data: any): Observable<any>;
  getApplicationProgressRecords(data: any): Observable<any>;
  createApplicationProgressRecord(data: any): Observable<any>;


}

// Auth Service interfaces
interface AuthService {
  validateToken(data: any): Observable<any>;
  getUserById(data: any): Observable<any>;
  getUserByEmail(data: any): Observable<any>;
  getUserRoles(data: any): Observable<any>;
  register(data: any): Observable<any>;
}

// University Service interfaces
interface UniversityService {
  getUniversity(data: any): Observable<any>;
  listUniversities(data: any): Observable<any>;
  universityListForStudentApplication(data: any): Observable<any>;
  getCourses(data: any): Observable<any>;
  getPrograms(data: any): Observable<any>;
  getProgramLevelsByUniversity(data: any): Observable<any>;
  getFieldsOfStudyByUniversity(data: any): Observable<any>;
  listCourses(data: any): Observable<any>;
  getIntakesByUniversity(data: any): Observable<any>;
  getFeesByUniversity(data: any): Observable<any>;
  // New methods for student-specific data retrieval
  getCoursesByUniversityForStudent(data: any): Observable<any>;
  getIntakesByUniversityForStudent(data: any): Observable<any>;
  getProgramLevelsByUniversityForStudent(data: any): Observable<any>;
  getFieldsOfStudyByUniversityForStudent(data: any): Observable<any>;
  getFeesByUniversityForStudent(data: any): Observable<any>;
}

// Agency Service interfaces
interface AgencyService {
  getAgency(data: any): Observable<any>;
  listAgencies(data: any): Observable<any>;
  getAgencyStudents(data: any): Observable<any>;
}

@Injectable()
export class StudentClientService implements OnModuleInit {
  private readonly logger = new Logger(StudentClientService.name);

  private studentService: StudentService;
  private authService: AuthService;
  private universityService: UniversityService;
  private agencyService: AgencyService;

  // gRPC Clients
  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'students',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/students/students.proto'
      ),
      url: process.env.STUDENTS_SERVICE_URL || 'localhost:50058'
    }
  })
  private studentsClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'auth',
      protoPath: join(process.cwd(), 'libs/shared/dto/src/lib/auth/auth.proto'),
      url: 'auth-service:50052'//process.env.AUTH_SERVICE_URL || 'localhost:50051'
    }
  })
  private authClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'university',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/university/university.proto'
      ),
      url: process.env.UNIVERSITY_SERVICE_URL || 'localhost:50059'
    }
  })
  private universityClient: ClientGrpc;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'agency',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/agency/agency.proto'
      ),
      url: process.env.AGENCY_SERVICE_URL || 'localhost:50060'
    }
  })
  private agencyClient: ClientGrpc;

  onModuleInit() {
    this.studentService =
      this.studentsClient.getService<StudentService>('StudentService');
    this.authService = this.authClient.getService<AuthService>('AuthService');
    this.universityService =
      this.universityClient.getService<UniversityService>('UniversityService');
    this.agencyService =
      this.agencyClient.getService<AgencyService>('AgencyService');
  }

  // New Student Personal Info Methods
  async createStudentPersonalInfo(studentData: any): Promise<any> {
    try {
      let createdUserId: number | undefined;

      // Check if user exists by email, if not create one
      const userExists = await this.checkUserExists(studentData.email);

      if (!userExists) {
        try {
          // Create user first
          const userResult = await this.createUserForStudent(
            studentData,
            studentData.organizationId || '1'
          );

          // Capture the userId if user was created successfully
          if (userResult.success && userResult.userId) {
            createdUserId = userResult.userId;
            this.logger.log(
              `User created successfully with ID: ${createdUserId}`
            );
          }
        } catch (error) {
          // If user creation fails because user already exists, continue
          if (error.message?.includes('User already exists')) {
            this.logger.log(
              `User with email ${studentData.email} already exists, continuing with student creation`
            );
          } else {
            throw error;
          }
        }
      } else {
        this.logger.log(
          `User with email ${studentData.email} already exists, skipping user creation`
        );
        // Try to get the existing user's ID
        try {
          const existingUserResult = await this.createUserForStudent(
            studentData,
            studentData.organizationId || '1'
          );
          if (existingUserResult.userId) {
            createdUserId = existingUserResult.userId;
            this.logger.log(`Retrieved existing user ID: ${createdUserId}`);
          }
        } catch (error) {
          this.logger.warn(
            `Could not retrieve existing user ID: ${error.message}`
          );
        }
      }

      // Add userId to studentData if we have it
      if (createdUserId) {
        studentData.userId = createdUserId;
        this.logger.log(`Adding userId ${createdUserId} to student data`);
      }

      // Check if student already exists by email before creating
      try {
        const existingStudent = await firstValueFrom(
          this.studentService
            .getStudentByEmail({ email: studentData.email })
            .pipe(catchError(() => of(null)))
        );

        if (existingStudent && existingStudent.student) {
          this.logger.log(
            `Found existing student for email ${studentData.email}, updating instead of creating`
          );

          // Update the existing student with new data
          return await firstValueFrom(
            this.studentService.createStudent(studentData).pipe(
              catchError((error) => {
                this.logger.error('Error updating existing student:', error);
                throw new RpcException(error);
              })
            )
          );
        }
      } catch (error) {
        this.logger.warn(
          `Error checking for existing student: ${error.message}`
        );
      }

      // Pass the original studentData to the student service
      // The student service expects the original format, not the proto format
      return await firstValueFrom(
        this.studentService.createStudent(studentData).pipe(
          catchError((error) => {
            this.logger.error('Error creating student personal info:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to create student personal info:', error);
      throw error;
    }
  }

  async updateStudentPersonalInfo(
    id: string,
    studentData: any,
    organizationId: string
  ): Promise<any> {
    try {
      // Check if user exists by email, if not create one
      const userExists = await this.checkUserExists(studentData.email);

      if (!userExists) {
        try {
          // Create user first
          await this.createUserForStudent(studentData, organizationId);
        } catch (error) {
          // If user creation fails because user already exists, continue
          if (error.message?.includes('User already exists')) {
            this.logger.log(
              `User with email ${studentData.email} already exists, continuing with student update`
            );
          } else {
            throw error;
          }
        }
      } else {
        this.logger.log(
          `User with email ${studentData.email} already exists, skipping user creation`
        );
      }
      // Pass the original studentData to the student service
      // The student service expects the original format, not the proto format
      return await firstValueFrom(
        this.studentService.updateStudent({ id, ...studentData }).pipe(
          catchError((error) => {
            this.logger.error('Error updating student personal info:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to update student personal info:', error);
      throw error;
    }
  }

  // Helper method to check if user exists
  private async checkUserExists(email: string): Promise<boolean> {
    try {
      const result = await firstValueFrom(
        this.authService.getUserByEmail({ email }).pipe(
          catchError((error) => {
            // If user not found, return false
            if (error.code === 5 || error.message?.includes('not found')) {
              return Promise.resolve({ success: false, user: null });
            }
            throw error;
          })
        )
      );
      return result.success && result.user !== null;
    } catch (error) {
      this.logger.debug('User existence check failed:', email, error.message);
      return false;
    }
  }

  // Helper method to create user for student
  private async createUserForStudent(
    studentData: any,
    organizationId: string
  ): Promise<any> {
    try {
      const registerData = {
        name: `${studentData.firstName} ${studentData.lastName}`,
        email: studentData.email,
        password: 'TempPassword123!', // Temporary password
        phone: studentData.phone,
        nationality: '', // Can be derived from country if needed
        organizationName: '', // Will be handled by organizationId
        roleName: 'Student', // Default role for students
        departmentName: 'Students', // Default department
        ipAddress: '127.0.0.1',
        userAgent: 'Student Registration System'
      };

      const result = await firstValueFrom(
        this.authService.register(registerData).pipe(
          catchError((error) => {
            // If user already exists, handle gracefully
            if (error.message?.includes('User already exists')) {
              this.logger.log(
                `User with email ${studentData.email} already exists, continuing...`
              );
              return Promise.resolve({
                success: true,
                message: 'User already exists'
              });
            }
            this.logger.error('Error creating user for student:', error);
            throw new RpcException(error);
          })
        )
      );

      // Return the result with userId if available
      return result;
    } catch (error) {
      this.logger.error('Failed to create user for student:', error);
      throw error;
    }
  }

  // Helper method to convert Long objects to numbers and BoolValue to boolean | null
  private convertLongToNumber(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (
      typeof obj === 'object' &&
      obj.low !== undefined &&
      obj.high !== undefined
    ) {
      // This is a gRPC Long object
      return obj.low;
    }

    // Handle BoolValue conversion
    if (typeof obj === 'object' && 'value' in obj && typeof obj.value === 'boolean') {
      return obj.value;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.convertLongToNumber(item));
    }

    if (typeof obj === 'object') {
      const converted: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // Special handling for acceptedByApplyGoal field
        if (key === 'acceptedByApplyGoal' && value === null) {
          converted[key] = null;
        } else {
          converted[key] = this.convertLongToNumber(value);
        }
      }
      return converted;
    }

    return obj;
  }

  // Helper method to convert boolean | null to gRPC BoolValue format
  private convertToBoolValue(value: boolean | null | undefined): { value: boolean } | null {
    if (value === null || value === undefined) {
      return null;
    }
    return { value: value };
  }

  // Helper method to transform request data to proto format
  private transformToProtoFormat(studentData: any): any {
    return {
      name: `${studentData.firstName} ${studentData.lastName}`,
      first_name: studentData.firstName,
      native_name: studentData.nameInNative,
      email: studentData.email,
      date_of_birth: studentData.dateOfBirth,
      gender: studentData.gender,
      father_name: studentData.fatherName,
      mother_name: studentData.motherName,
      national_id: studentData.nid,
      passport_number: studentData.passport,
      marital_status: studentData.maritalStatus?.status || '',
      spouse_name: studentData.maritalStatus?.spouseName || '',
      spouse_passport_number: studentData.maritalStatus?.spousePassport || '',

      // Present Address
      present_address: studentData.presentAddress?.address || '',
      present_country: studentData.presentAddress?.country || '',
      present_state: studentData.presentAddress?.state || '',
      present_city: studentData.presentAddress?.city || '',
      present_postal_code: studentData.presentAddress?.postalCode || '',

      // Permanent Address
      permanent_address: studentData.permanentAddress?.address || '',
      permanent_country: studentData.permanentAddress?.country || '',
      permanent_state: studentData.permanentAddress?.state || '',
      permanent_city: studentData.permanentAddress?.city || '',
      permanent_postal_code: studentData.permanentAddress?.postalCode || '',

      // Sponsor & Guardian
      sponsor_name: studentData.sponsor?.name || '',
      relationship: studentData.sponsor?.relation || '',
      phone_number: studentData.phone,
      guardian_number: studentData.guardianPhone,

      // Preferences
      preferred_subjects: studentData.preferredSubject || [],
      preferred_countries: studentData.preferredCountry || [],

      // Social links
      social_links: (studentData.socialLinks || []).map((link: any) => ({
        title: link.platform,
        url: link.url
      })),

      reference: studentData.reference || '',
      note: studentData.note || '',

      // Organization and Agency fields
      organization_id:
        studentData.organizationId || studentData.organization_id,
      agency_id: studentData.agencyId || studentData.agency_id
    };
  }

  // -------------- academic section -----------------
  // #region
  // Academic Background Methods
  async createOrUpdateStudentAcademic(
    studentId: string,
    academicData: any
  ): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .createOrUpdateStudentAcademic({
            studentId,
            ...academicData
          })
          .pipe(
            catchError((error) => {
              this.logger.error(
                'Error creating/updating student academic info:',
                error
              );
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error(
        'Failed to create/update student academic info:',
        error
      );
      throw error;
    }
  }

  async getStudentAcademic(studentId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService.getStudentAcademic({ studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting student academic:', error);
            throw new RpcException(error);
          })
        )
      );

      return response;
    } catch (error) {
      this.logger.error('Failed to get student academic:', error);
      throw error;
    }
  }

  // Application Methods
  async createApplication(applicationData: any): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService.createApplication(applicationData).pipe(
          catchError((error) => {
            this.logger.error('Error creating application:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert Long objects to numbers
      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to create application:', error);
      throw error;
    }
  }

  async getApplication(applicationId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService.getApplication({ id: Number(applicationId) }).pipe(
          catchError((error) => {
            this.logger.error('Error getting application:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert Long objects to numbers
      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to get application:', error);
      throw error;
    }
  }

  async getApplicationsByStudent(studentId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService.getApplicationsByStudent({ studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting applications by student:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert Long objects to numbers
      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to get applications by student:', error);
      throw error;
    }
  }

  async updateApplication(
    applicationId: string,
    applicationData: any
  ): Promise<any> {
    this.logger.log(
      'updateApplication request',
      applicationId,
      applicationData,
      JSON.stringify(applicationData)
    );
    try {
      const response = await firstValueFrom(
        this.studentService
          .updateApplication({
            id: Number(applicationId),
            ...applicationData
          })
          .pipe(
            catchError((error) => {
              this.logger.error('Error updating application:', error);
              throw new RpcException(error);
            })
          )
      );

      // Convert Long objects to numbers
      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to update application:', error);
      throw error;
    }
  }

  async deleteApplication(applicationId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService
          .deleteApplication({ id: Number(applicationId) })
          .pipe(
            catchError((error) => {
              this.logger.error('Error deleting application:', error);
              throw new RpcException(error);
            })
          )
      );

      return response;
    } catch (error) {
      this.logger.error('Failed to delete application:', error);
      throw error;
    }
  }

  async listApplications(filters: any): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService.listApplications(filters).pipe(
          catchError((error) => {
            this.logger.error('gRPC listApplications error:', error);
            throw new RpcException(error);
          })
        )
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to list applications:', error);
      throw error;
    }
  }

  async getApplicationProgress(applicationId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService.getApplicationProgress({ id: applicationId }).pipe(
          catchError((error) => {
            this.logger.error('gRPC getApplicationProgress error:', error);
            throw new RpcException(error);
          })
        )
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to get application progress:', error);
      throw error;
    }
  }

  async createApplicationRequirements(
    applicationId: number,
    items: Array<{
      documentTitle: string;
      documentName: string;
      url?: string;
      documentDescription?: string;
      isRequired?: boolean;
      allowedFormats?: string[];
      requiredByDate?: string;
    }>,
    studentId?: string
  ): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService
          .createApplicationRequirements({ applicationId, items, studentId })
          .pipe(
            catchError((error) => {
              this.logger.error(
                'Error creating application requirements:',
                error
              );
              throw new RpcException(error);
            })
          )
      );
      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to create application requirements:', error);
      throw error;
    }
  }
  // #endregion

  // Student Service Methods (Legacy)
  async createStudent(studentData: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.createStudent(studentData).pipe(
          catchError((error) => {
            this.logger.error('Error creating student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to create student:', error);
      throw error;
    }
  }

  async getStudent(id: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getStudent({ id }).pipe(
          catchError((error) => {
            this.logger.error('Error getting student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get student:', error);
      throw error;
    }
  }

  async updateStudent(id: string, studentData: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.updateStudent({ id, student: studentData }).pipe(
          catchError((error) => {
            this.logger.error('Error updating student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to update student:', error);
      throw error;
    }
  }

  async deleteStudent(id: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.deleteStudent({ id }).pipe(
          catchError((error) => {
            this.logger.error('Error deleting student:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to delete student:', error);
      throw error;
    }
  }

  async listStudents(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.listStudents(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing students:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list students:', error);
      throw error;
    }
  }

  // Academic Operations
  async enrollInCourse(
    studentId: string,
    courseId: string,
    semester: string
  ): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .enrollInCourse({
            student_id: studentId,
            course_id: courseId,
            semester
          })
          .pipe(
            catchError((error) => {
              this.logger.error('Error enrolling in course:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to enroll in course:', error);
      throw error;
    }
  }

  async dropCourse(
    studentId: string,
    courseId: string,
    semester: string
  ): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .dropCourse({ student_id: studentId, course_id: courseId, semester })
          .pipe(
            catchError((error) => {
              this.logger.error('Error dropping course:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to drop course:', error);
      throw error;
    }
  }

  async getEnrollments(studentId: string, semester?: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .getEnrollments({ student_id: studentId, semester })
          .pipe(
            catchError((error) => {
              this.logger.error('Error getting enrollments:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to get enrollments:', error);
      throw error;
    }
  }

  async updateGrades(
    studentId: string,
    courseId: string,
    grade: string
  ): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService
          .updateGrades({ student_id: studentId, course_id: courseId, grade })
          .pipe(
            catchError((error) => {
              this.logger.error('Error updating grades:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to update grades:', error);
      throw error;
    }
  }

  async getAcademicProgress(studentId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getAcademicProgress({ student_id: studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting academic progress:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get academic progress:', error);
      throw error;
    }
  }

  async getTranscript(studentId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.studentService.getTranscript({ student_id: studentId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting transcript:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get transcript:', error);
      throw error;
    }
  }

  // Auth Service Methods
  async validateToken(token: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.authService.validateToken({ token }).pipe(
          catchError((error) => {
            this.logger.error('Error validating token:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to validate token:', error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.authService.getUserById({ id: userId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting user by ID:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get user by ID:', error);
      throw error;
    }
  }

  // University Service Methods
  async getUniversity(universityId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.universityService.getUniversity({ id: universityId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting university:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long ID to number
      if (response.data && response.data.id) {
        // eslint-disable-next-line no-constant-binary-expression
        response.data.id =
          response.data.id?.low ?? Number(response.data.id) ?? 0;
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to get university:', error);
      throw error;
    }
  }

  async listUniversities(filters: any): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.universityService.listUniversities(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing universities:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long IDs to numbers
      if (response.data && response.data.universities) {
        response.data.universities = response.data.universities.map(
          (university: any) => ({
            ...university,
            // eslint-disable-next-line no-constant-binary-expression
            id: university.id?.low ?? Number(university.id) ?? 0
          })
        );
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to list universities:', error);
      throw error;
    }
  }

  async universityListForStudentApplication(): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.universityService.universityListForStudentApplication({}).pipe(
          catchError((error) => {
            this.logger.error(
              'Error getting university list for student application:',
              error
            );
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long IDs to numbers in the nested structure
      if (response.universities) {
        response.universities = response.universities.map(
          (university: any) => ({
            ...university,
            id: university.id?.low ?? Number(university.id) ?? 0,
            countries:
              university.countries?.map((country: any) => ({
                ...country,
                id: country.id?.low ?? Number(country.id) ?? 0,
                campuses:
                  country.campuses?.map((campus: any) => ({
                    ...campus,
                    id: campus.id?.low ?? Number(campus.id) ?? 0,
                    programLevels:
                      campus.programLevels?.map((program: any) => ({
                        ...program,
                        id: program.id?.low ?? Number(program.id) ?? 0,
                        intakes:
                          program.intakes?.map((intake: any) => ({
                            ...intake,
                            id: intake.id?.low ?? Number(intake.id) ?? 0,
                            startDate: intake.startDate,
                            endDate: intake.endDate,
                            classStartDate: intake.classStartDate,
                            applicationTypes: intake.applicationTypes,
                            applicationProcessDuration:
                              intake.applicationProcessDuration,
                            courses:
                              intake.courses?.map((course: any) => ({
                                ...course,
                                id: course.id?.low ?? Number(course.id) ?? 0
                              })) || []
                          })) || []
                      })) || []
                  })) || []
              })) || []
          })
        );
      }

      return response;
    } catch (error) {
      this.logger.error(
        'Failed to get university list for student application:',
        error
      );
      throw error;
    }
  }

  // University Data Methods
  async getProgramLevelsByUniversity(universityId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.universityService.getProgramLevelsByUniversity({ universityId: parseInt(universityId) }).pipe(
          catchError((error) => {
            this.logger.error('Error getting program levels:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long IDs to numbers
      if (response.data) {
        response.data = response.data.map((level: any) => ({
          ...level,
          id: level.id?.low ?? Number(level.id) ?? 0
        }));
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to get program levels:', error);
      throw error;
    }
  }

  async getFieldsOfStudyByUniversity(universityId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.universityService.getFieldsOfStudyByUniversity({ universityId: parseInt(universityId) }).pipe(
          catchError((error) => {
            this.logger.error('Error getting fields of study:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long IDs to numbers
      if (response.data) {
        response.data = response.data.map((field: any) => ({
          ...field,
          id: field.id?.low ?? Number(field.id) ?? 0
        }));
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to get fields of study:', error);
      throw error;
    }
  }

  async listCoursesByUniversity(universityId: string, filters?: any): Promise<any> {
    try {
      const courseFilters = { ...filters, universityId: parseInt(universityId) };
      const response = await firstValueFrom(
        this.universityService.listCourses(courseFilters).pipe(
          catchError((error) => {
            this.logger.error('Error listing courses:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long IDs to numbers
      if (response.data) {
        response.data = response.data.map((course: any) => ({
          ...course,
          id: course.id?.low ?? Number(course.id) ?? 0,
          universityId: course.universityId?.low ?? Number(course.universityId) ?? 0,
          fieldOfStudy: course.fieldOfStudy?.low ?? Number(course.fieldOfStudy) ?? 0,
          programLevelId: course.programLevelId?.low ?? Number(course.programLevelId) ?? 0
        }));
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to list courses:', error);
      throw error;
    }
  }

  async getIntakesByUniversity(universityId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.universityService.getIntakesByUniversity({ universityId: parseInt(universityId) }).pipe(
          catchError((error) => {
            this.logger.error('Error getting intakes:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long IDs to numbers
      if (response.data) {
        response.data = response.data.map((intake: any) => ({
          ...intake,
          id: intake.id?.low ?? Number(intake.id) ?? 0
        }));
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to get intakes:', error);
      throw error;
    }
  }

  async getFeesByUniversity(universityId: string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.universityService.getFeesByUniversity({ universityId: parseInt(universityId) }).pipe(
          catchError((error) => {
            this.logger.error('Error getting fees:', error);
            throw new RpcException(error);
          })
        )
      );

      // Convert gRPC Long IDs to numbers
      if (response.data) {
          response.data = response.data.map((fee: any) => ({
            ...fee,
            programLevelId: fee.programLevelId?.low ?? Number(fee.programLevelId) ?? 0,
            intakeId: fee.intakeId?.low ?? Number(fee.intakeId) ?? 0
          }));
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to get fees:', error);
      throw error;
    }
  }

  async getComprehensiveUniversityData(universityId: string): Promise<any> {
    try {
      const [
        programLevels,
        fieldsOfStudy,
        courses,
        intakes,
        tuitionFees
      ] = await Promise.all([
        this.getProgramLevelsByUniversity(universityId),
        this.getFieldsOfStudyByUniversity(universityId),
        this.listCoursesByUniversity(universityId),
        this.getIntakesByUniversity(universityId),
        this.getFeesByUniversity(universityId)
      ]);

      return {
        programLevels: programLevels.data || [],
        fieldsOfStudy: fieldsOfStudy.data || [],
        courses: courses.data || [],
        intakes: intakes.data || [],
        tuitionFees: tuitionFees.data || []
      };
    } catch (error) {
      this.logger.error('Failed to get comprehensive university data:', error);
      throw error;
    }
  }

  // Get all course data (courses, intakes, tuition fees, program levels, fields of study)
  async getAllCourseData(universityId: number): Promise<any> {
    try {
      this.logger.log(`Fetching all course data for university ${universityId}`);

      // Fetch all data in parallel for better performance
      const [coursesResult, intakesResult, programLevelsResult, fieldsOfStudyResult, feesResult] = await Promise.all([
        firstValueFrom(
          this.universityService.getCoursesByUniversityForStudent({ universityId }).pipe(
            catchError((error) => {
              this.logger.error('Error fetching courses:', error);
              return of({ status: 500, data: [], error: error.message });
            })
          )
        ),
        firstValueFrom(
          this.universityService.getIntakesByUniversityForStudent({ universityId }).pipe(
            catchError((error) => {
              this.logger.error('Error fetching intakes:', error);
              return of({ status: 500, data: [], error: error.message });
            })
          )
        ),
        firstValueFrom(
          this.universityService.getProgramLevelsByUniversityForStudent({ universityId }).pipe(
            catchError((error) => {
              this.logger.error('Error fetching program levels:', error);
              return of({ status: 500, data: [], error: error.message });
            })
          )
        ),
        firstValueFrom(
          this.universityService.getFieldsOfStudyByUniversityForStudent({ universityId }).pipe(
            catchError((error) => {
              this.logger.error('Error fetching fields of study:', error);
              return of({ status: 500, data: [], error: error.message });
            })
          )
        ),
        firstValueFrom(
          this.universityService.getFeesByUniversityForStudent({ universityId }).pipe(
            catchError((error) => {
              this.logger.error('Error fetching fees:', error);
              return of({ status: 500, data: [], error: error.message });
            })
          )
        )
      ]);

      Logger.debug('course result', coursesResult);

      // Extract data from responses, handling potential errors
      const courses = coursesResult.status === 200 && coursesResult.data ? coursesResult.data : [];
      const intakes = intakesResult.status === 200 && intakesResult.data ? intakesResult.data : [];
      const programLevels = programLevelsResult.status === 200 && programLevelsResult.data ? programLevelsResult.data : [];
      const fieldsOfStudy = fieldsOfStudyResult.status === 200 && fieldsOfStudyResult.data ? fieldsOfStudyResult.data : [];
      const tuitionFees = feesResult.status === 200 && feesResult.data ? feesResult.data : [];

      // Check if any service calls failed
      const failedServices = [];
      if (coursesResult.status !== 200) failedServices.push('courses');
      if (intakesResult.status !== 200) failedServices.push('intakes');
      if (programLevelsResult.status !== 200) failedServices.push('program levels');
      if (fieldsOfStudyResult.status !== 200) failedServices.push('fields of study');
      if (feesResult.status !== 200) failedServices.push('fees');

      if (failedServices.length > 0) {
        this.logger.warn(`Some services failed: ${failedServices.join(', ')}`);
      }

      // Ensure all arrays are defined
      const safeCourses = Array.isArray(courses) ? courses : [];
      const safeIntakes = Array.isArray(intakes) ? intakes : [];
      const safeTuitionFees = Array.isArray(tuitionFees) ? tuitionFees : [];
      const safeProgramLevels = Array.isArray(programLevels) ? programLevels : [];
      const safeFieldsOfStudy = Array.isArray(fieldsOfStudy) ? fieldsOfStudy : [];

      return {
        success: true,
        message: 'Course data retrieved successfully',
        data: {
          courses: safeCourses,
          intakes: safeIntakes,
          tuitionFees: safeTuitionFees,
          programLevels: safeProgramLevels,
          fieldsOfStudy: safeFieldsOfStudy
        },
        metadata: {
          totalCourses: safeCourses.length,
          totalIntakes: safeIntakes.length,
          totalTuitionFees: safeTuitionFees.length,
          totalProgramLevels: safeProgramLevels.length,
          totalFieldsOfStudy: safeFieldsOfStudy.length,
          failedServices: failedServices.length > 0 ? failedServices : undefined
        }
      };
    } catch (error) {
      this.logger.error('Failed to get all course data:', error);
      throw new RpcException(error);
    }
  }

  // Get filtered courses based on multiple criteria
  async getFilteredCourses(filters: any): Promise<any> {
    try {
      this.logger.log(`Getting filtered courses with filters: ${JSON.stringify(filters)}`);

      // Use universityId from filters, fallback to default if not provided
      const universityId = filters.universityId || 8; // Default to university ID 8 if not specified
      
      // Get all course data for the specified university
      const allCourseData = await this.getAllCourseData(universityId);
      
      if (!allCourseData.success || !allCourseData.data) {
        throw new Error('Failed to retrieve course data');
      }

      const { courses, intakes, programLevels, fieldsOfStudy, tuitionFees } = allCourseData.data;
      
      // Ensure all arrays are defined and safe to use
      const safeCourses = Array.isArray(courses) ? courses : [];
      const safeIntakes = Array.isArray(intakes) ? intakes : [];
      const safeProgramLevels = Array.isArray(programLevels) ? programLevels : [];
      const safeFieldsOfStudy = Array.isArray(fieldsOfStudy) ? fieldsOfStudy : [];
      const safeTuitionFees = Array.isArray(tuitionFees) ? tuitionFees : [];
      
      // Apply filters
      let filteredCourses = safeCourses.filter(course => {
        // Filter by university ID (courses should already be filtered by university, but double-check)
        if (filters.universityId && course.universityId !== filters.universityId) {
          return false;
        }

        // Filter by course names
        if (filters.courseNames && filters.courseNames.length > 0) {
          const courseNameMatch = filters.courseNames.some((name: string) =>
            course.courseTitle.toLowerCase().includes(name.toLowerCase())
          );
          if (!courseNameMatch) return false;
        }

        // Filter by program levels
        if (filters.programLevels && filters.programLevels.length > 0) {
          const programLevel = safeProgramLevels.find(pl => pl.id === course.programLevelId);
          if (!programLevel || !filters.programLevels.includes(programLevel.programLevelName)) {
            return false;
          }
        }

        // Filter by fields of study
        if (filters.fieldsOfStudy && filters.fieldsOfStudy.length > 0) {
          const fieldOfStudy = safeFieldsOfStudy.find(fs => fs.id === course.fieldOfStudyId);
          if (!fieldOfStudy || !filters.fieldsOfStudy.includes(fieldOfStudy.name)) {
            return false;
          }
        }

        // Filter by formats
        if (filters.formats && filters.formats.length > 0) {
          if (!filters.formats.includes(course.format)) {
            return false;
          }
        }

        // Filter by lecture languages
        if (filters.lectureLanguages && filters.lectureLanguages.length > 0) {
          if (!course.lectureLanguage || !filters.lectureLanguages.includes(course.lectureLanguage)) {
            return false;
          }
        }

        // Filter by GPA range
        if (filters.minGpa && course.minimumGpa) {
          const courseGpa = parseFloat(course.minimumGpa);
          if (courseGpa < filters.minGpa) return false;
        }
        if (filters.maxGpa && course.minimumGpa) {
          const courseGpa = parseFloat(course.minimumGpa);
          if (courseGpa > filters.maxGpa) return false;
        }

        // Filter by active status
        if (filters.isActive !== undefined) {
          if (course.isActive !== filters.isActive) return false;
        }

        return true;
      });

      this.logger.log(`After basic filtering: ${filteredCourses.length} courses remaining`);

      // Filter by intakes (use course.intakeSessions when available)
      if (filters.intakes && filters.intakes.length > 0) {
        this.logger.log(`Filtering by intakes: ${filters.intakes.join(', ')}`);
        
        filteredCourses = filteredCourses.filter(course => {
          const sessions = Array.isArray((course as any).intakeSessions) ? (course as any).intakeSessions : [];
          if (sessions.length > 0) {
            // Prefer intakeSessions seeded per course
            return sessions.some((s: any) => {
              return filters.intakes.some((filterIntake: string) => {
                const normalizedFilter = filterIntake.replace('-', ' ').toLowerCase();
                const sessionName = (s.name || '').replace('-', ' ').toLowerCase();
                const intakeName = (s.intake?.name || '').replace('-', ' ').toLowerCase();
                return sessionName.includes(normalizedFilter) || intakeName.includes(normalizedFilter);
              });
            });
          }

          // Fallback to legacy separate intakes list (by course/programLevel)
          const courseIntakes = safeIntakes.filter(intake => 
            intake.courseId === course.id || intake.programLevelId === course.programLevelId
          );
          if (courseIntakes.length === 0) return false;

          return courseIntakes.some(intake => {
            return filters.intakes.some((filterIntake: string) => {
              const normalizedFilterIntake = filterIntake.replace('-', ' ').toLowerCase();
              const normalizedIntakeName = (intake.name || '').replace('-', ' ').toLowerCase();
              return normalizedIntakeName.includes(normalizedFilterIntake) ||
                     normalizedFilterIntake.includes(normalizedIntakeName);
            });
          });
        });

        this.logger.log(`After intake filtering: ${filteredCourses.length} courses remaining`);
      }

      // Filter by tuition fee ranges
      if (filters.tuitionFeeRanges && filters.tuitionFeeRanges.length > 0) {
        this.logger.log(`Filtering by tuition fee ranges: ${JSON.stringify(filters.tuitionFeeRanges)}`);
        
        filteredCourses = filteredCourses.filter(course => {
          // Prefer fees attached to the course response (from gRPC include)
          const embeddedFees = Array.isArray((course as any).tuitionFees) ? (course as any).tuitionFees : [];
          let courseFees = embeddedFees;

          if (courseFees.length === 0) {
            // Fallback to separate fees list
            courseFees = safeTuitionFees.filter(fee =>
              fee.courseId === course.id ||
              fee.programLevelId === course.programLevelId
            );
          }

          if (courseFees.length === 0) return false;

          // Check if any fee matches the filter ranges
          return filters.tuitionFeeRanges.some((range: any) => {
            return courseFees.some((fee: any) => {
              const tuition = Number(fee.tuitionFee ?? 0);
              if (range.minFee != null && tuition < range.minFee) return false;
              if (range.maxFee != null && tuition > range.maxFee) return false;
              
              // Filter by currency if specified
              if (range.currency && fee.currency && range.currency !== fee.currency) return false;
              
              // Filter by fee type if specified
              if (range.feeType) {
                switch (range.feeType) {
                  case 'tuition': if (!fee.tuitionFee) return false; break;
                  case 'credit': if (!fee.creditFee) return false; break;
                  case 'semester': if (!fee.semisterFee) return false; break;
                  case 'yearly': if (!fee.yearlyFee) return false; break;
                }
              }

              return true;
            });
          });
        });
      }

      // Add related data to courses
      const enrichedCourses = filteredCourses.map(course => {
        const university = allCourseData.data.universities?.find(u => u.id === course.universityId);
        const fieldOfStudy = safeFieldsOfStudy.find(fs => fs.id === course.fieldOfStudyId);
        const programLevel = safeProgramLevels.find(pl => pl.id === course.programLevelId);
        
        // Prefer embedded intake sessions from course payload
        const embeddedSessions = Array.isArray((course as any).intakeSessions) ? (course as any).intakeSessions : [];
        // Fallback: related intakes from separate list
        const relatedIntakes = embeddedSessions.length > 0 ? [] : safeIntakes.filter(intake => 
          intake.courseId === course.id || intake.programLevelId === course.programLevelId
        );
        
        // Prefer embedded tuition fees from course payload
        const embeddedFees = Array.isArray((course as any).tuitionFees) ? (course as any).tuitionFees : [];
        // Fallback: related tuition fees from separate list
        const relatedFees = embeddedFees.length > 0 ? [] : safeTuitionFees.filter(fee => 
          fee.courseId === course.id || 
          fee.programLevelId === course.programLevelId ||
          (fee.intakeId && relatedIntakes.some(intake => intake.id === fee.intakeId))
        );

        return {
          ...course,
          university: university ? {
            id: university.id,
            name: university.name,
            logo: university.logo
          } : undefined,
          fieldOfStudy: fieldOfStudy ? {
            id: fieldOfStudy.id,
            name: fieldOfStudy.name
          } : undefined,
          programLevel: programLevel ? {
            id: programLevel.id,
            programLevelName: programLevel.programLevelName,
            durationNumber: programLevel.durationNumber,
            durationType: programLevel.durationType
          } : undefined,
          // Expose sessions as intakes for backward compatibility
          intakes: embeddedSessions.length > 0
            ? embeddedSessions.map((s: any) => ({
                id: s.id,
                name: s.name,
                startDate: s.intake?.startDate,
                endDate: s.intake?.endDate,
                classStartDate: s.intake?.classStartDate,
              }))
            : relatedIntakes.map(intake => ({
                id: intake.id,
                name: intake.name,
                startDate: intake.startDate,
                endDate: intake.endDate,
                classStartDate: intake.classStartDate
              })),
          intakeSessions: embeddedSessions, // also expose raw sessions
          tuitionFees: embeddedFees.length > 0
            ? embeddedFees
            : relatedFees.map(fee => {
                const relatedIntake = relatedIntakes.find(intake => intake.id === fee.intakeId);
                return {
                  id: fee.id,
                  feeTitle: fee.feeTitle,
                  tuitionFee: fee.tuitionFee,
                  creditFee: fee.creditFee,
                  semisterFee: fee.semisterFee,
                  yearlyFee: fee.yearlyFee,
                  applicationFee: fee.applicationFee,
                  bankStatementAmount: fee.bankStatementAmount,
                  isActive: fee.isActive,
                  isRefundableToStudent: fee.isRefundableToStudent,
                  isVisibleToStudent: fee.isVisibleToStudent,
                  intake: relatedIntake ? {
                    id: relatedIntake.id,
                    name: relatedIntake.name,
                    startDate: relatedIntake.startDate,
                    endDate: relatedIntake.endDate,
                    classStartDate: relatedIntake.classStartDate
                  } : null
                };
              })
        };
      });

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedCourses = enrichedCourses.slice(startIndex, endIndex);

      return {
        success: true,
        message: 'Filtered courses retrieved successfully',
        data: {
          courses: paginatedCourses,
          total: enrichedCourses.length,
          page,
          limit,
          totalPages: Math.ceil(enrichedCourses.length / limit)
        },
        metadata: {
          appliedFilters: filters,
          totalFiltered: enrichedCourses.length,
          totalUnfiltered: safeCourses.length
        }
      };
    } catch (error) {
      this.logger.error('Failed to get filtered courses:', error);
      throw new RpcException(error);
    }
  }

  // Agency Service Methods
  async getAgency(agencyId: string): Promise<any> {
    try {
      return await firstValueFrom(
        this.agencyService.getAgency({ id: agencyId }).pipe(
          catchError((error) => {
            this.logger.error('Error getting agency:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to get agency:', error);
      throw error;
    }
  }

  async listAgencies(filters: any): Promise<any> {
    try {
      return await firstValueFrom(
        this.agencyService.listAgencies(filters).pipe(
          catchError((error) => {
            this.logger.error('Error listing agencies:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to list agencies:', error);
      throw error;
    }
  }

  // Helper method to check if student exists by email
  // private async checkStudentExistsByEmail(email: string): Promise<any> {
  //   try {
  //     const result = await firstValueFrom(
  //       this.studentService.checkStudentExistsByEmail({ email }).pipe(
  //         catchError((error) => {
  //           this.logger.debug('Student does not exist:', email);
  //           return Promise.resolve({ exists: false, student: null });
  //         })
  //       )
  //     );
  //     return result.exists ? result.student : null;
  //   } catch (error) {
  //     this.logger.debug('Student does not exist:', email);
  //     return null;
  //   }
  // }

  // ==================== APPLICATION DOCUMENT REQUIREMENTS ====================

  async getApplicationRequirements(applicationId: number): Promise<any> {
    try {
      this.logger.log(
        `Getting application requirements for application: ${applicationId}`
      );

      const response = await firstValueFrom(
        this.studentService.getApplicationRequirements({ applicationId })
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to get application requirements:', error);
      throw error;
    }
  }

  async updateApplicationRequirement(
    requirementId: number,
    updateData: {
      acceptedByApplyGoal?: boolean | null;
      acceptedByUniversity?: boolean;
      acceptedByApplyGoalUserId?: number;
      acceptedByUniversityUserId?: number;
      rejectionReason?: string;
      status?: string;
      applicationId?: number;
      url?: string;
    }
  ): Promise<any> {
    try {
      this.logger.log(`Updating document requirement status: ${requirementId}`);

      const response = await firstValueFrom(
        this.studentService.updateApplicationRequirement({
          requirementId,
          ...updateData,
          acceptedByApplyGoal: this.convertToBoolValue(updateData.acceptedByApplyGoal)
        })
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to update document requirement status:', error);
      throw error;
    }
  }

  async updateApplicationStage(
    applicationId: number,
    stageId: number,
    status: string
  ): Promise<any> {
    try {
      this.logger.log(`Updating application stage: ${stageId} to status: ${status}`);

      const response = await firstValueFrom(
        this.studentService.updateApplicationStage({
          applicationId,
          stageId,
          status
        })
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to update application stage:', error);
      throw error;
    }
  }

  async createApplicationNote(
    applicationId: number,
    noteData: {
      noteType: string;
      title?: string;
      content: string;
      createdBy: number;
      isPrivate?: boolean;
      userInfo?: any;
      replyId?: number;
    }
  ): Promise<any> {
    try {
      this.logger.log(`Creating application note for application: ${applicationId}`);

      const response = await firstValueFrom(
        this.studentService.createApplicationNote({
          applicationId,
          noteType: noteData.noteType,
          title: noteData.title || '',
          content: noteData.content,
          createdBy: noteData.createdBy,
          isPrivate: noteData.isPrivate || false,
          userInfo: noteData.userInfo ? JSON.stringify(noteData.userInfo) : null,
          replyId: noteData.replyId || null
        })
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to create application note:', error);
      throw error;
    }
  }

  async getApplicationNotes(
    applicationId: number,
    noteType?: string
  ): Promise<any> {
    try {
      this.logger.log(`Getting application notes for application: ${applicationId}`);

      const response = await firstValueFrom(
        this.studentService.getApplicationNotes({
          applicationId,
          noteType
        })
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to get application notes:', error);
      throw error;
    }
  }

  // ==================== APPLICATION PROGRESS RECORDS ====================

  async getApplicationProgressRecords(
    applicationId: number,
    recordType?: string
  ): Promise<any> {
    try {
      this.logger.log(`Getting application progress records for application: ${applicationId}`);

      const response = await firstValueFrom(
        this.studentService.getApplicationProgressRecords({
          applicationId,
          recordType
        })
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to get application progress records:', error);
      throw error;
    }
  }

  async createApplicationProgressRecord(
    applicationId: number,
    recordData: {
      recordType: string;
      title: string;
      description: string;
      status?: string;
      recordDate?: string;
      amount?: number;
      currency?: string;
      proofLinks?: any[];
      attachments?: any[];
      createdBy: number;
      applicationStage?: string;
      applicationStep?: string;
    }
  ): Promise<any> {
    try {
      this.logger.log(`Creating application progress record for application: ${applicationId}`);

      const response = await firstValueFrom(
        this.studentService.createApplicationProgressRecord({
          applicationId,
          recordType: recordData.recordType,
          title: recordData.title,
          description: recordData.description,
          status: recordData.status || 'completed',
          recordDate: recordData.recordDate || new Date().toISOString(),
          amount: recordData.amount || 0,
          currency: recordData.currency || 'USD',
          proofLinks: recordData.proofLinks ? JSON.stringify(recordData.proofLinks) : null,
          attachments: recordData.attachments ? JSON.stringify(recordData.attachments) : null,
          createdBy: recordData.createdBy,
          applicationStage: recordData.applicationStage,
          applicationStep: recordData.applicationStep
        })
      );

      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to create application progress record:', error);
      throw error;
    }
  }

  // Universal tracking method - can be called from anywhere in the gateway
  async trackApplicationProgress(
    applicationId: number,
    recordType: string,
    title: string,
    description: string,
    options: {
      status?: 'completed' | 'in_progress' | 'failed';
      amount?: number;
      currency?: string;
      proofLinks?: any[];
      attachments?: any[];
      createdBy: number;
      applicationStage?: string;
      applicationStep?: string;
    }
  ): Promise<any> {
    try {
      this.logger.log(`Tracking application progress: ${applicationId} - ${recordType} - ${title}`);

      return await this.createApplicationProgressRecord(applicationId, {
        recordType,
        title,
        description,
        status: options.status || 'completed',
        recordDate: new Date().toISOString(),
        amount: options.amount,
        currency: options.currency,
        proofLinks: options.proofLinks,
        attachments: options.attachments,
        createdBy: options.createdBy,
        applicationStage: options.applicationStage,
        applicationStep: options.applicationStep
      });
    } catch (error) {
      this.logger.error(`Failed to track application progress: ${error.message}`);
      // Don't throw error to avoid breaking the main process
      return null;
    }
  }

  async generateCalDocument(
    applicationId: string,
    options: {
      templateType?: 'pdf' | 'html';
      outputFormat?: 'pdf' | 'html';
      includeAllPages?: boolean;
      pageNumbers?: number[];
    } = {}
  ): Promise<any> {
    try {
      this.logger.log(
        `Generating CAL document for application ${applicationId} with options:`,
        options
      );

      const result = await firstValueFrom(
        this.studentService
          .generateCalDocument({
            applicationId: parseInt(applicationId, 10),
            templateType: options.templateType || 'html',
            outputFormat: options.outputFormat || 'pdf',
            includeAllPages: options.includeAllPages !== false,
            pageNumbers: options.pageNumbers
          })
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error generating CAL document: ${error.message}`
              );
              throw new RpcException({
                code: error.code || 13,
                message: error.message || 'Failed to generate CAL document'
              });
            })
          )
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to generate CAL document for application ${applicationId}:`,
        error
      );
      throw error;
    }
  }

  async updateStudentApplicationType(
    studentId: string,
    applicationType: string
  ): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.studentService
          .updateStudentApplicationType({ studentId, applicationType })
          .pipe(
            catchError((error) => {
              this.logger.error(
                'Error updating student applicationType:',
                error
              );
              throw new RpcException(error);
            })
          )
      );
      return this.convertLongToNumber(response);
    } catch (error) {
      this.logger.error('Failed to update student applicationType:', error);
      throw error;
    }
  }

  // Cleanup duplicate students
  async cleanupDuplicateStudents(): Promise<any> {
    try {
      this.logger.log('Calling cleanup duplicate students service...');
      return await firstValueFrom(
        this.studentService.cleanupDuplicateStudents({}).pipe(
          catchError((error) => {
            this.logger.error('Error cleaning up duplicate students:', error);
            throw new RpcException(error);
          })
        )
      );
    } catch (error) {
      this.logger.error('Failed to cleanup duplicate students:', error);
      throw error;
    }
  }

  // Generate admission package
  // async generateAdmissionPackage(
  //   studentId: string,
  //   applicationId: number,
  //   templateType: 'pdf' | 'html' = 'html'
  // ): Promise<any> {
  //   try {
  //     this.logger.log(
  //       `Calling generate admission package service for student ${studentId} and application ${applicationId} with template type ${templateType}`
  //     );
  //     return await firstValueFrom(
  //       this.studentService
  //         .generateAdmissionPackage({
  //           studentId,
  //           applicationId,
  //           templateType
  //         })
  //         .pipe(
  //           catchError((error) => {
  //             this.logger.error('Error generating admission package:', error);
  //             throw new RpcException(error);
  //           })
  //         )
  //     );
  //   } catch (error) {
  //     this.logger.error('Failed to generate admission package:', error);
  //     throw error;
  //   }
  // }

  // Generate ADM document
  async generateAdmDocument(
    studentId: string,
    applicationId: number,
    templateType: 'pdf' | 'html' = 'html'
  ): Promise<any> {
    try {
      this.logger.log(
        `Calling generate ADM document service for student ${studentId} and application ${applicationId} with template type ${templateType}`
      );
      return await firstValueFrom(
        this.studentService
          .generateAdmDocument({
            studentId,
            applicationId,
            templateType
          })
          .pipe(
            catchError((error) => {
              this.logger.error('Error generating ADM document:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to generate ADM document:', error);
      throw error;
    }
  }

  // Generate Admission Portfolio
  async generateAdmissionPortfolio(
    studentId: string,
    applicationId: number,
    templateType: 'pdf' | 'html' = 'html'
  ): Promise<any> {
    try {
      this.logger.log(
        `Calling generate admission portfolio service for student ${studentId} and application ${applicationId} with template type ${templateType}`
      );
      return await firstValueFrom(
        this.studentService
          .generateAdmissionPortfolio({
            studentId,
            applicationId,
            templateType
          })
          .pipe(
            catchError((error) => {
              this.logger.error('Error generating admission portfolio:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to generate admission portfolio:', error);
      throw error;
    }
  }

  // Get Dashboard Data by Campus
  async getDashboardDataByCampus(campusId: number): Promise<any> {
    try {
      this.logger.log(
        `Calling get dashboard data by campus service for campus ID: ${campusId}`
      );
      return await firstValueFrom(
        this.studentService
          .getDashboardDataByCampus({
            campusId
          })
          .pipe(
            catchError((error) => {
              this.logger.error('Error getting dashboard data by campus:', error);
              throw new RpcException(error);
            })
          )
      );
    } catch (error) {
      this.logger.error('Failed to get dashboard data by campus:', error);
      throw error;
    }
  }
}
