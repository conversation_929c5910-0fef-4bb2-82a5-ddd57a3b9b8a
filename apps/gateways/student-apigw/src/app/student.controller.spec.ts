import { Test, TestingModule } from '@nestjs/testing';
import { StudentController } from './student.controller';
import { StudentClientService } from './student.service';

describe('StudentController - CAL Document', () => {
  let controller: StudentController;
  let service: StudentClientService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StudentController],
      providers: [
        {
          provide: StudentClientService,
          useValue: {
            generateCalDocument: jest.fn(),
            getApplication: jest.fn(),
            getApplicationProgress: jest.fn()
          }
        }
      ],
    }).compile();

    controller = module.get<StudentController>(StudentController);
    service = module.get<StudentClientService>(StudentClientService);
  });

  describe('generateCalDocument', () => {
    it('should generate PDF document successfully', async () => {
      const mockResponse = {
        status: 200,
        message: 'CAL document generated successfully',
        data: {
          documentContent: Buffer.from('test pdf content'),
          filename: 'CAL-Document-123.pdf',
          contentType: 'application/pdf',
          fileSize: 12345,
          generatedAt: '2024-01-15T10:30:00Z'
        }
      };

      jest.spyOn(service, 'generateCalDocument').mockResolvedValue(mockResponse);

      const mockRes = {
        setHeader: jest.fn(),
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      await controller.generateCalDocument(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        mockRes as any,
        'pdf', // outputFormat
        'true', // includeAllPages
        undefined // pageNumbers
      );

      expect(service.generateCalDocument).toHaveBeenCalledWith('123', {
        outputFormat: 'pdf',
        includeAllPages: true,
        pageNumbers: undefined
      });

      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/pdf');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename="CAL-Document-123.pdf"');
      expect(mockRes.send).toHaveBeenCalledWith(mockResponse.data.documentContent);
    });

    it('should generate HTML document with specific pages', async () => {
      const mockResponse = {
        status: 200,
        message: 'CAL document generated successfully',
        data: {
          documentContent: '<html>test content</html>',
          filename: 'CAL-Document-123.html',
          contentType: 'text/html',
          fileSize: 5432,
          generatedAt: '2024-01-15T10:30:00Z'
        }
      };

      jest.spyOn(service, 'generateCalDocument').mockResolvedValue(mockResponse);

      const mockRes = {
        setHeader: jest.fn(),
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      await controller.generateCalDocument(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        mockRes as any,
        'html', // outputFormat
        'false', // includeAllPages
        '1,2' // pageNumbers
      );

      expect(service.generateCalDocument).toHaveBeenCalledWith('123', {
        outputFormat: 'html',
        includeAllPages: false,
        pageNumbers: [1, 2]
      });

      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'text/html');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename="CAL-Document-123.html"');
      expect(mockRes.send).toHaveBeenCalledWith(mockResponse.data.documentContent);
    });

    it('should handle service errors gracefully', async () => {
      const mockError = {
        status: 500,
        message: 'Failed to generate CAL document',
        data: null,
        error: {
          details: 'Template processing failed'
        }
      };

      jest.spyOn(service, 'generateCalDocument').mockResolvedValue(mockError);

      const mockRes = {
        setHeader: jest.fn(),
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      await controller.generateCalDocument(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        mockRes as any,
        'pdf', // outputFormat
        'true', // includeAllPages
        undefined // pageNumbers
      );

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Failed to generate CAL document',
        error: {
          details: 'Template processing failed'
        }
      });
    });

    it('should handle exceptions and return error response', async () => {
      jest.spyOn(service, 'generateCalDocument').mockRejectedValue(new Error('Service unavailable'));

      const mockRes = {
        setHeader: jest.fn(),
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      await controller.generateCalDocument(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        mockRes as any,
        'pdf', // outputFormat
        'true', // includeAllPages
        undefined // pageNumbers
      );

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Service unavailable',
        error: {
          details: 'Service unavailable'
        }
      });
    });
  });

  describe('getCalDocumentInfo', () => {
    it('should return CAL document information', async () => {
      const result = await controller.getCalDocumentInfo(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent' // userAgent
      );

      expect(result).toEqual({
        status: 'success',
        data: {
          applicationId: '123',
          availableFormats: ['pdf', 'html'],
          defaultFormat: 'pdf',
          supportsMultiplePages: true,
          availablePages: [1, 2, 3, 4],
          templateVariables: [
            'name', 'idNumber', 'passport', 'dateOfBirth', 'courseName',
            'intake', 'campusName', 'tuitionFee', 'courseStartDate', 
            'courseEndDate', 'issueDate'
          ],
          generationOptions: {
            outputFormat: 'pdf | html',
            includeAllPages: 'true | false',
            pageNumbers: 'comma-separated list (e.g., "1,2,3")'
          }
        },
        message: 'CAL document information retrieved successfully'
      });
    });

    it('should handle errors in getCalDocumentInfo', async () => {
      // Mock the logger to avoid console output during tests
      jest.spyOn(controller['logger'], 'error').mockImplementation(() => {});

      // This test would need to be expanded if we add more error handling logic
      const result = await controller.getCalDocumentInfo(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent' // userAgent
      );

      expect(result.status).toBe('success');
    });
  });

  describe('Parameter Parsing', () => {
    it('should parse includeAllPages correctly', async () => {
      const mockResponse = {
        status: 200,
        message: 'CAL document generated successfully',
        data: {
          documentContent: Buffer.from('test pdf content'),
          filename: 'CAL-Document-123.pdf',
          contentType: 'application/pdf',
          fileSize: 12345,
          generatedAt: '2024-01-15T10:30:00Z'
        }
      };

      jest.spyOn(service, 'generateCalDocument').mockResolvedValue(mockResponse);

      const mockRes = {
        setHeader: jest.fn(),
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Test with includeAllPages = 'false'
      await controller.generateCalDocument(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        mockRes as any,
        'pdf', // outputFormat
        'false', // includeAllPages
        undefined // pageNumbers
      );

      expect(service.generateCalDocument).toHaveBeenCalledWith('123', {
        outputFormat: 'pdf',
        includeAllPages: false,
        pageNumbers: undefined
      });
    });

    it('should parse pageNumbers correctly', async () => {
      const mockResponse = {
        status: 200,
        message: 'CAL document generated successfully',
        data: {
          documentContent: Buffer.from('test pdf content'),
          filename: 'CAL-Document-123.pdf',
          contentType: 'application/pdf',
          fileSize: 12345,
          generatedAt: '2024-01-15T10:30:00Z'
        }
      };

      jest.spyOn(service, 'generateCalDocument').mockResolvedValue(mockResponse);

      const mockRes = {
        setHeader: jest.fn(),
        send: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // Test with pageNumbers = '1,2,3'
      await controller.generateCalDocument(
        '123',
        1, // userId
        ['student'], // roles
        '127.0.0.1', // ipAddress
        'test-agent', // userAgent
        mockRes as any,
        'pdf', // outputFormat
        'true', // includeAllPages
        '1,2,3' // pageNumbers
      );

      expect(service.generateCalDocument).toHaveBeenCalledWith('123', {
        outputFormat: 'pdf',
        includeAllPages: true,
        pageNumbers: [1, 2, 3]
      });
    });
  });
}); 