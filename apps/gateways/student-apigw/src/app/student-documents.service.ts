import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Inject } from '@nestjs/common';
import { Observable, firstValueFrom } from 'rxjs';
import { MulterFile } from '@apply-goal-backend/common';
import {
  CreateStudentDocumentsDto,
  CreateStudentDocumentsResponseDto,
  GetStudentDocumentsQueryDto,
  UpdateDocumentVerificationDto,
  StudentDocumentResponseDto
} from './dto/student-documents.dto';

// gRPC service interface
interface StudentDocumentsGrpcService {
  uploadDocuments(data: any): Observable<any>;
  getStudentDocuments(data: any): Observable<any>;
  getDocument(data: any): Observable<any>;
  updateDocumentVerification(data: any): Observable<any>;
  deleteDocument(data: any): Observable<any>;
  getDocumentsSummary(data: any): Observable<any>;
}

@Injectable()
export class StudentDocumentsClientService implements OnModuleInit {
  private readonly logger = new Logger(StudentDocumentsClientService.name);
  private studentDocumentsService!: StudentDocumentsGrpcService;

  constructor(
    @Inject('STUDENTS_SERVICE') private readonly client: ClientGrpc
  ) {}

  onModuleInit() {
    this.studentDocumentsService =
      this.client.getService<StudentDocumentsGrpcService>(
        'StudentDocumentsService'
      );
  }

  /**
   * Upload documents for a student
   */
  async uploadDocuments(
    studentId: string,
    dto: CreateStudentDocumentsDto,
    files: { [fieldname: string]: MulterFile[] }
  ): Promise<any | CreateStudentDocumentsResponseDto> {
    this.logger.log(`Uploading documents for student ${studentId} via gRPC`);

    try {
      // Convert files to gRPC format
      const grpcFiles = [];
      for (const [fieldname, fileArray] of Object.entries(files)) {
        for (const file of fileArray) {
          grpcFiles.push({
            fieldname: file.fieldname,
            filename: file.originalname,
            mimetype: file.mimetype,
            data: file.buffer,
            size: file.size
          });
        }
      }

      const request = {
        studentId: studentId,
        sponsorName: dto.sponsorName,
        takeDependents: dto.takeDependents,
        academicSections: dto.academicSections,
        proficiencySections: dto.proficiencySections,
        sponsorMetadata: dto.sponsorMetadata,
        dependents: dto.dependents,
        children: dto.children,
        files: grpcFiles
      };
      console.log(
        'Request should be here+++++++++++++++++++++++++++++++=',
        request
      );

      const response = await firstValueFrom(
        this.studentDocumentsService.uploadDocuments(request)
      );
      // const response = [];
      console.log(
        'Response should be here+++++++++++++++++++++++++++++++=',
        response
      );
      return response;

      // return {
      //   success: response.success,
      //   message: response.message,
      //   documents: response.documents.map((doc: any) =>
      //     this.mapFromGrpcDocument(doc)
      //   ),
      //   totalUploaded: response.total_uploaded,
      //   failedUploads: response.failed_uploads,
      //   errors: response.errors
      // };
    } catch (error) {
      this.logger.error(`Failed to upload documents via gRPC:`, error);
      return {
        status: error.code || 500,
        message:
          error.details ||
          error.message ||
          'Unknown error occurred during gRPC upload.',
        data: null,
        error: {
          code: error.code,
          message: error.message,
          details: error.stack,
          validationErrors: []
        }
      };
    }
  }

  /**
   * Get student documents with optional filtering
   */
  async getStudentDocuments(
    studentId: string,
    query: GetStudentDocumentsQueryDto
  ): Promise<any> {
    this.logger.log(`Getting documents for student ${studentId} via gRPC`);

    try {
      const request = {
        studentId: studentId,
        section: query.section,
        field: query.field,
        verificationStatus: query.verificationStatus,
        includeInactive: query.includeInactive
      };

      const response = await firstValueFrom(
        this.studentDocumentsService.getStudentDocuments(request)
      );

      if (response && response.documents && response.documents.length > 0) {
        const data = response.documents.map((doc: any) =>
          this.mapFromGrpcDocument(doc)
        );
        return {
          status: 200,
          message: 'Documents retrieved successfully',
          data
        };
      } else {
        return {
          status: 200,
          message: 'No documents found',
          data: []
        };
      }
    } catch (error) {
      this.logger.error(`Failed to get documents via gRPC:`, error);
      throw error;
    }
  }

  /**
   * Get a specific document
   */
  async getDocument(
    studentId: number,
    documentId: number
  ): Promise<StudentDocumentResponseDto> {
    this.logger.log(
      `Getting document ${documentId} for student ${studentId} via gRPC`
    );

    try {
      const request = {
        student_id: studentId,
        document_id: documentId
      };

      const response = await firstValueFrom(
        this.studentDocumentsService.getDocument(request)
      );

      return this.mapFromGrpcDocument(response.document);
    } catch (error) {
      this.logger.error(`Failed to get document via gRPC:`, error);
      throw error;
    }
  }

  /**
   * Update document verification status
   */
  async updateDocumentVerification(
    documentId: number,
    updateDto: UpdateDocumentVerificationDto
  ): Promise<StudentDocumentResponseDto> {
    this.logger.log(
      `Updating verification for document ${documentId} via gRPC`
    );

    try {
      const request = {
        document_id: documentId,
        verification_status: updateDto.verificationStatus,
        notes: updateDto.notes,
        expiry_date: updateDto.expiryDate?.toISOString()
      };

      const response = await firstValueFrom(
        this.studentDocumentsService.updateDocumentVerification(request)
      );

      return this.mapFromGrpcDocument(response.document);
    } catch (error) {
      this.logger.error(
        `Failed to update document verification via gRPC:`,
        error
      );
      throw error;
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(
    studentId: number,
    documentId: number
  ): Promise<{ message: string }> {
    this.logger.log(
      `Deleting document ${documentId} for student ${studentId} via gRPC`
    );

    try {
      const request = {
        student_id: studentId,
        document_id: documentId
      };

      const response = await firstValueFrom(
        this.studentDocumentsService.deleteDocument(request)
      );

      return { message: response.message };
    } catch (error) {
      this.logger.error(`Failed to delete document via gRPC:`, error);
      throw error;
    }
  }

  /**
   * Get documents summary
   */
  async getDocumentsSummary(studentId: number): Promise<any> {
    this.logger.log(
      `Getting documents summary for student ${studentId} via gRPC`
    );

    try {
      const request = {
        student_id: studentId
      };

      const response = await firstValueFrom(
        this.studentDocumentsService.getDocumentsSummary(request)
      );

      return { sections: response.sections };
    } catch (error) {
      this.logger.error(`Failed to get documents summary via gRPC:`, error);
      throw error;
    }
  }

  /**
   * Map gRPC document response to DTO
   */
  private mapFromGrpcDocument(grpcDoc: any): StudentDocumentResponseDto {
    return {
      id: grpcDoc.id,
      student_id: grpcDoc.student_id,
      section: grpcDoc.section,
      field: grpcDoc.field,
      filename: grpcDoc.filename,
      url: grpcDoc.url,
      mimeType: grpcDoc.mime_type,
      fileSize: grpcDoc.file_size,
      originalName: grpcDoc.original_name,
      bucket: grpcDoc.bucket,
      objectKey: grpcDoc.object_key,
      metadata: grpcDoc.metadata
        ? {
            dependentIndex: grpcDoc.metadata.dependent_index,
            childIndex: grpcDoc.metadata.child_index,
            dependentName: grpcDoc.metadata.dependent_name,
            childName: grpcDoc.metadata.child_name,
            dependentPassport: grpcDoc.metadata.dependent_passport,
            childPassport: grpcDoc.metadata.child_passport,
            ...grpcDoc.metadata.additional_data
          }
        : undefined,
      created_at: new Date(grpcDoc.created_at),
      updated_at: new Date(grpcDoc.updated_at)
    };
  }
}
