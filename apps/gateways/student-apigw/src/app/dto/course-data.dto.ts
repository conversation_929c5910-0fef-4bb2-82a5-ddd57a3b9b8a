import { IsArray, IsBoolean, IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';

export class CourseDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  courseTitle!: string;

  @IsNumber()
  fieldOfStudyId!: number;

  @IsString()
  format!: string;

  @IsNumber()
  programLevelId!: number;

  @IsOptional()
  @IsString()
  lastAcademic?: string;

  @IsOptional()
  @IsString()
  minimumGpa?: string;

  @IsOptional()
  @IsString()
  courseRank?: string;

  @IsOptional()
  @IsString()
  acceptanceRate?: string;

  @IsOptional()
  @IsString()
  lectureLanguage?: string;

  @IsOptional()
  @IsString()
  additionalRequirements?: string;

  @IsDateString()
  createdAt!: string;

  @IsDateString()
  updatedAt!: string;

  @IsBoolean()
  isActive!: boolean;
}

export class IntakeDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  name!: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsString()
  endDate!: string;

  @IsString()
  classStartDate!: string;

  @IsOptional()
  @IsString()
  enrollementDate?: string;

  @IsArray()
  applicationTypes!: any[];

  @IsString()
  applicationProcessDuration!: string;

  @IsArray()
  studentTypes!: Array<{
    id: number;
    studentType: string;
    applicationOpen?: string;
    applicationDeadline?: string;
    enrollmentDeadline?: string;
  }>;

  @IsDateString()
  createdAt!: string;

  @IsDateString()
  updatedAt!: string;

  @IsBoolean()
  isActive!: boolean;
}

export class ProgramLevelDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  programLevelName!: string;

  @IsOptional()
  @IsNumber()
  credits?: number;

  @IsOptional()
  @IsNumber()
  durationNumber?: number;

  @IsOptional()
  @IsString()
  durationType?: string;

  @IsDateString()
  createdAt!: string;

  @IsDateString()
  updatedAt!: string;

  @IsBoolean()
  isActive!: boolean;
}

export class FieldOfStudyDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  name!: string;

  @IsString()
  status!: string;

  @IsDateString()
  createdAt!: string;

  @IsDateString()
  updatedAt!: string;

  @IsBoolean()
  isActive!: boolean;
}

export class TuitionFeeDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsNumber()
  campusId!: number;

  @IsNumber()
  programLevelIntakeId!: number;

  @IsString()
  feeTitle!: string;

  @IsNumber()
  tuitionFee!: number;

  @IsOptional()
  @IsNumber()
  creditFee?: number;

  @IsOptional()
  @IsNumber()
  semisterFee?: number;

  @IsOptional()
  @IsNumber()
  yearlyFee?: number;

  @IsOptional()
  @IsNumber()
  applicationFee?: number;

  @IsOptional()
  @IsNumber()
  bankStatementAmount?: number;

  @IsOptional()
  @IsNumber()
  applicationFeeChargedByUniversity?: number;

  @IsOptional()
  @IsNumber()
  applicationFeeChargedToStudent?: number;

  @IsOptional()
  @IsNumber()
  paymentDueInDays?: number;

  @IsOptional()
  @IsString()
  feeEffectiveDate?: string;

  @IsOptional()
  @IsString()
  applicationYearStart?: string;

  @IsOptional()
  @IsString()
  applicationYearEnd?: string;

  @IsBoolean()
  isActive!: boolean;

  @IsBoolean()
  isRefundableToStudent!: boolean;

  @IsBoolean()
  isVisibleToStudent!: boolean;

  @IsDateString()
  createdAt!: string;

  @IsDateString()
  updatedAt!: string;
}

export class CourseDataResponseDto {
  @IsArray()
  courses!: CourseDto[];

  @IsArray()
  intakes!: IntakeDto[];

  @IsArray()
  tuitionFees!: TuitionFeeDto[];

  @IsArray()
  programLevels!: ProgramLevelDto[];

  @IsArray()
  fieldsOfStudy!: FieldOfStudyDto[];
}

export class CourseDataApiResponseDto {
  @IsBoolean()
  success!: boolean;

  @IsString()
  message!: string;

  data!: CourseDataResponseDto;
} 