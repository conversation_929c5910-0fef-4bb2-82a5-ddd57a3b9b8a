import { Is<PERSON>rray, <PERSON><PERSON>ptional, IsString, IsN<PERSON>ber, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CourseFilterDto {
  @IsOptional()
  @IsNumber()
  universityId?: number; // Specific university ID to filter by

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  intakes?: string[]; // Array of intake names

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  courseNames?: string[]; // Array of course names or partial matches

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  programLevels?: string[]; // Array of program level names

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fieldsOfStudy?: string[]; // Array of field of study names

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TuitionFeeRangeDto)
  tuitionFeeRanges?: TuitionFeeRangeDto[]; // Array of tuition fee ranges

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  universities?: string[]; // Array of university names

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  formats?: string[]; // Array of course formats (Full-time, Part-time, etc.)

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  lectureLanguages?: string[]; // Array of lecture languages

  @IsOptional()
  @IsNumber()
  minGpa?: number; // Minimum GPA requirement

  @IsOptional()
  @IsNumber()
  maxGpa?: number; // Maximum GPA requirement

  @IsOptional()
  @IsBoolean()
  isActive?: boolean; // Filter by active status

  @IsOptional()
  @IsNumber()
  page?: number; // Pagination page number

  @IsOptional()
  @IsNumber()
  limit?: number; // Pagination limit
}

export class TuitionFeeRangeDto {
  @IsOptional()
  @IsNumber()
  minFee?: number; // Minimum tuition fee

  @IsOptional()
  @IsNumber()
  maxFee?: number; // Maximum tuition fee

  @IsOptional()
  @IsString()
  currency?: string; // Currency (USD, EUR, etc.)

  @IsOptional()
  @IsString()
  feeType?: string; // Fee type: 'tuition', 'credit', 'semester', 'yearly'
}

export class CourseFilterResponseDto {
  @IsArray()
  courses!: CourseDto[];

  @IsNumber()
  total!: number;

  @IsNumber()
  page!: number;

  @IsNumber()
  limit!: number;

  @IsNumber()
  totalPages!: number;
}

export class CourseDto {
  @IsNumber()
  id!: number;

  @IsNumber()
  universityId!: number;

  @IsString()
  courseTitle!: string;

  @IsNumber()
  fieldOfStudyId!: number;

  @IsString()
  format!: string;

  @IsNumber()
  programLevelId!: number;

  @IsOptional()
  @IsString()
  lastAcademic?: string;

  @IsOptional()
  @IsString()
  minimumGpa?: string;

  @IsOptional()
  @IsString()
  courseRank?: string;

  @IsOptional()
  @IsString()
  acceptanceRate?: string;

  @IsOptional()
  @IsString()
  lectureLanguage?: string;

  @IsOptional()
  @IsString()
  additionalRequirements?: string;

  @IsString()
  createdAt!: string;

  @IsString()
  updatedAt!: string;

  @IsBoolean()
  isActive!: boolean;

  // Related data
  @IsOptional()
  university?: {
    id: number;
    name: string;
    logo: string;
  };

  @IsOptional()
  fieldOfStudy?: {
    id: number;
    name: string;
  };

  @IsOptional()
  programLevel?: {
    id: number;
    programLevelName: string;
    durationNumber?: number;
    durationType?: string;
  };

  @IsOptional()
  intakes?: Array<{
    id: number;
    name: string;
    startDate: string;
    endDate: string;
    classStartDate: string;
  }>;

  @IsOptional()
  tuitionFees?: Array<{
    id: number;
    feeTitle: string;
    tuitionFee: number;
    applicationFee?: number;
    currency?: string;
  }>;
} 