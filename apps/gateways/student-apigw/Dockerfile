# Build stage
FROM node:18-alpine as builder

WORKDIR /app

# Install pnpm
RUN corepack enable && corepack prepare pnpm@8.15.1 --activate

# Set pnpm store directory
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml ./
COPY nx.json ./
COPY tsconfig*.json ./
COPY eslint.config.mjs ./
COPY pnpm-workspace.yaml ./

# Copy source code
COPY apps/gateways/student-apigw ./apps/gateways/student-apigw
COPY libs ./libs

# Install dependencies with pnpm (skip prepare script)
RUN pnpm install --frozen-lockfile --ignore-scripts

# Build the application
RUN pnpm nx build student-apigw --prod

# Production stage
FROM node:18-alpine

WORKDIR /app

# Install build tools for native modules
RUN apk add --no-cache python3 make g++

# Install pnpm
RUN corepack enable && corepack prepare pnpm@8.15.1 --activate

# Set pnpm store directory
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

# Copy built assets from builder
COPY --from=builder /app/dist/apps/gateways/student-apigw ./

# Copy package files for dependencies
COPY package*.json ./
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./

# Install all dependencies (including tslib) with pnpm (skip prepare script)
RUN pnpm install --frozen-lockfile --ignore-scripts

# Install curl for healthcheck
RUN apk --no-cache add curl

# Set environment variables
ENV NODE_ENV=production
ENV PORT=4007

# Expose the service port
EXPOSE 4007

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=30s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:4007/health || exit 1

# Start the service
CMD ["node", "main.js"]

