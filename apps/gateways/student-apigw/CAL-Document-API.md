# CAL Document API Endpoints

This document describes the CAL (Conditional Acceptance Letter) document generation endpoints available in the student-apigw.

## Base URL
```
https://api.student-apigw.applygoal.com
```

## Endpoints

### 1. Generate CAL Document

**Endpoint**: `GET /applications/{id}/cal-document`

**Description**: Generates a CAL document for a specific application and returns it as a downloadable file.

**Parameters**:
- `id` (path, required): Application ID
- `outputFormat` (query, optional): Output format - `pdf` or `html` (default: `pdf`)
- `includeAllPages` (query, optional): Include all pages - `true` or `false` (default: `true`)
- `pageNumbers` (query, optional): Specific page numbers to include (comma-separated, e.g., "1,2,3")

**Headers**:
- `Authorization`: Bearer token (required)
- `Content-Type`: Automatically set based on response

**Response**:
- **Success**: JSON response with MinIO URL (if <PERSON><PERSON> is available) or file download
- **Error**: JSON error response

**Example Requests**:

```bash
# Generate PDF with all pages (default) - Returns MinIO URL
curl -X GET "https://api.student-apigw.applygoal.com/applications/123/cal-document" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Generate HTML with specific pages - Returns MinIO URL
curl -X GET "https://api.student-apigw.applygoal.com/applications/123/cal-document?outputFormat=html&includeAllPages=false&pageNumbers=1,2" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Generate PDF with specific pages - Returns MinIO URL
curl -X GET "https://api.student-apigw.applygoal.com/applications/123/cal-document?outputFormat=pdf&pageNumbers=1,2,3" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Success Response (JSON)**:
```json
{
  "status": "success",
  "message": "CAL document generated and uploaded successfully",
  "data": {
    "filename": "CAL-Document-123.pdf",
    "contentType": "application/pdf",
    "fileSize": 123456,
    "minioUrl": "http://localhost:9000/applygoal-files/files/test-uuid.pdf",
    "objectKey": "files/test-uuid.pdf",
    "generatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Fallback Response (File Download)**:
```
Content-Type: application/pdf
Content-Disposition: attachment; filename="CAL-Document-123.pdf"
Content-Length: 123456
Cache-Control: no-cache
```

### 2. Get CAL Document Information

**Endpoint**: `GET /applications/{id}/cal-document/info`

**Description**: Returns information about CAL document generation options without generating the document.

**Parameters**:
- `id` (path, required): Application ID

**Headers**:
- `Authorization`: Bearer token (required)

**Response**:
```json
{
  "status": "success",
  "data": {
    "applicationId": "123",
    "availableFormats": ["pdf", "html"],
    "defaultFormat": "pdf",
    "supportsMultiplePages": true,
    "availablePages": [1, 2, 3, 4],
    "templateVariables": [
      "name", "idNumber", "passport", "dateOfBirth", "courseName",
      "intake", "campusName", "tuitionFee", "courseStartDate", 
      "courseEndDate", "issueDate"
    ],
    "generationOptions": {
      "outputFormat": "pdf | html",
      "includeAllPages": "true | false",
      "pageNumbers": "comma-separated list (e.g., \"1,2,3\")"
    }
  },
  "message": "CAL document information retrieved successfully"
}
```

**Example Request**:
```bash
curl -X GET "https://api.student-apigw.applygoal.com/applications/123/cal-document/info" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Template Variables

The CAL document templates support the following variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `name` | Student full name | "John Doe" |
| `idNumber` | Application ID number | "APP001" |
| `passport` | Student passport number | "AB123456" |
| `dateOfBirth` | Student date of birth | "1990-01-01" |
| `courseName` | Course/program name | "Computer Science" |
| `intake` | Intake name | "Fall 2024" |
| `campusName` | Campus name | "Los Angeles Campus" |
| `tuitionFee` | Tuition fee amount | "$25000" |
| `courseStartDate` | Course start date | "2024-09-01" |
| `courseEndDate` | Course end date | "2028-05-31" |
| `issueDate` | Document issue date | "January 15, 2024" |

## CAL Document Pages

The CAL document consists of multiple pages:

1. **Page 1**: Cover page with university branding and student information
2. **Page 2**: Main content with detailed application information
3. **Page 3**: Additional terms and conditions
4. **Page 4**: Additional information and signatures

## Error Responses

### 400 Bad Request
```json
{
  "status": "error",
  "message": "Invalid application ID",
  "error": {
    "details": "Application ID must be a valid number"
  }
}
```

### 404 Not Found
```json
{
  "status": "error",
  "message": "Application not found",
  "error": {
    "details": "Application with ID 123 not found"
  }
}
```

### 500 Internal Server Error
```json
{
  "status": "error",
  "message": "Failed to generate CAL document",
  "error": {
    "details": "Template processing failed"
  }
}
```

## Authentication

All endpoints require authentication using Bearer tokens:

```bash
Authorization: Bearer YOUR_JWT_TOKEN
```

## Rate Limiting

- **Generate Document**: 10 requests per minute per user
- **Get Document Info**: 60 requests per minute per user

## File Size Limits

- **PDF Documents**: Maximum 10MB
- **HTML Documents**: Maximum 5MB

## Caching

- Document generation is not cached
- Document info is cached for 5 minutes

## Usage Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

// Generate PDF document and get MinIO URL
async function generateCalDocument(applicationId, token) {
  try {
    const response = await axios.get(
      `https://api.student-apigw.applygoal.com/applications/${applicationId}/cal-document`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    if (response.data.status === 'success') {
      return {
        filename: response.data.data.filename,
        minioUrl: response.data.data.minioUrl,
        objectKey: response.data.data.objectKey,
        fileSize: response.data.data.fileSize,
        generatedAt: response.data.data.generatedAt
      };
    } else {
      throw new Error(response.data.message || 'Failed to generate document');
    }
  } catch (error) {
    console.error('Error generating CAL document:', error.response?.data || error.message);
    throw error;
  }
}

// Get document info
async function getCalDocumentInfo(applicationId, token) {
  try {
    const response = await axios.get(
      `https://api.student-apigw.applygoal.com/applications/${applicationId}/cal-document/info`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error getting CAL document info:', error.response?.data || error.message);
    throw error;
  }
}
```

### Python
```python
import requests

def generate_cal_document(application_id, token):
    """Generate CAL document for an application and get MinIO URL"""
    url = f"https://api.student-apigw.applygoal.com/applications/{application_id}/cal-document"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        if data['status'] == 'success':
            return {
                'filename': data['data']['filename'],
                'minioUrl': data['data']['minioUrl'],
                'objectKey': data['data']['objectKey'],
                'fileSize': data['data']['fileSize'],
                'generatedAt': data['data']['generatedAt']
            }
        else:
            raise Exception(data['message'] || 'Failed to generate document')
    else:
        raise Exception(f"Failed to generate document: {response.text}")

def get_cal_document_info(application_id, token):
    """Get CAL document information"""
    url = f"https://api.student-apigw.applygoal.com/applications/{application_id}/cal-document/info"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to get document info: {response.text}")
```

## Integration Notes

1. **MinIO Integration**: The generate endpoint now returns a JSON response with MinIO URL instead of file download
2. **Fallback Support**: If MinIO is unavailable, the endpoint falls back to file download
3. **Error Handling**: Always check for error responses in the JSON format
4. **Authentication**: Ensure valid JWT tokens are provided
5. **Page Selection**: Use pageNumbers parameter to include specific pages only
6. **Format Selection**: Choose between PDF (default) and HTML formats based on requirements
7. **MinIO URL**: Use the returned MinIO URL to access the generated document

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Users can only access documents for applications they have permission to view
3. **Rate Limiting**: Implemented to prevent abuse
4. **File Validation**: Generated documents are validated before delivery
5. **Audit Logging**: All document generation requests are logged for audit purposes
6. **MinIO Security**: Documents are stored securely in MinIO with proper access controls
7. **URL Security**: MinIO URLs are generated with proper authentication and authorization 