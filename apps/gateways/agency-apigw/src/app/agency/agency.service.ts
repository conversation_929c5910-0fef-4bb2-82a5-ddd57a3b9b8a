import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AgencyClientService } from '../app.service';
import {
  AgencyGrpcService,
  CreateAgencyRequest,
  CreateAgencyResponse,
  GetAgencyRequest,
  GetAgencyResponse,
  UpdateAgencyRequest,
  UpdateAgencyResponse,
  DeleteAgencyRequest,
  DeleteAgencyResponse,
  ListAgencyRequest,
  ListAgencyResponse
} from './agency.interface';

@Injectable()
export class AgencyService {
  private readonly logger = new Logger(AgencyService.name);

  constructor(private readonly agencyClient: AgencyClientService) {}

  private get grpcService(): AgencyGrpcService {
    return this.agencyClient.getService<AgencyGrpcService>('AgencyService');
  }

  createAgency(data: CreateAgencyRequest): Observable<CreateAgencyResponse> {
    this.logger.log(`Creating agency: ${data.agencyName}`);
    return this.grpcService.createAgency(data);
  }

  getAgency(data: GetAgencyRequest): Observable<GetAgencyResponse> {
    this.logger.log(`Getting agency with ID: ${data.id}`);
    return this.grpcService.getAgency(data);
  }

  updateAgency(data: UpdateAgencyRequest): Observable<UpdateAgencyResponse> {
    this.logger.log(`Updating agency with ID: ${data.id}`);
    return this.grpcService.updateAgency(data);
  }

  deleteAgency(data: DeleteAgencyRequest): Observable<DeleteAgencyResponse> {
    this.logger.log(`Deleting agency with ID: ${data.id}`);
    return this.grpcService.deleteAgency(data);
  }

  // ✅ Updated Service Method
  listAgencies(data: ListAgencyRequest): Observable<ListAgencyResponse> {
    // ✅ Log the full request for debugging
    this.logger.log(
      'Listing agencies with data:',
      JSON.stringify(data, null, 2)
    );

    // ✅ Validate data before sending to gRPC
    const validatedData: ListAgencyRequest = {
      page: data.page || 1,
      pageSize: data.pageSize || 10,
      ...data // Spread all other properties
    };

    // ✅ Remove any null/undefined values to avoid gRPC issues
    Object.keys(validatedData).forEach((key) => {
      if (
        validatedData[key] === null ||
        validatedData[key] === undefined ||
        validatedData[key] === ''
      ) {
        delete validatedData[key];
      }
    });

    this.logger.log('Sending to gRPC service:', validatedData);

    return this.grpcService.listAgencies(validatedData);
  }
}
