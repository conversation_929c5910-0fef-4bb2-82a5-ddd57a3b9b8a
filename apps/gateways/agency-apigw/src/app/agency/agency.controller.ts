import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  UseGuards
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CreateAgencyRequest,
  GetAgencyRequest,
  UpdateAgencyRequest,
  DeleteAgencyRequest,
  ListAgencyRequest
} from './agency.interface';
import { AgencyService } from './agency.service';
import { Permissions, CurrentUser } from '@apply-goal-backend/auth';
import { UserPermissions } from '@apply-goal-backend/common';

// @UseGuards(JwtAuthGuard)
@Controller('agencies')
export class AgencyController {
  private readonly logger = new Logger(AgencyController.name);

  constructor(private readonly agencyService: AgencyService) {}

  @Permissions(UserPermissions.AM_ONBOARD_NEW_AGENCY_PARTNERS)
  @Post()
  async createAgency(
    @Body() payload: CreateAgencyRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      this.logger.log(`Creating agency for user: ${userId}, roles: ${JSON.stringify(roles)}`);

      // Validate that we have userId and roles
      if (!userId) {
        throw new HttpException('User ID is required', HttpStatus.BAD_REQUEST);
      }
      if (!roles || roles.length === 0) {
        throw new HttpException('User roles are required', HttpStatus.BAD_REQUEST);
      }

      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(this.agencyService.createAgency(payload));
    } catch (error) {
      this.logger.error('Create agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AM_TRACK_AGENCY_PERFORMANCE)
  @Get(':id')
  async getAgency(@Param('id') id: string) {
    try {
      return await firstValueFrom(this.agencyService.getAgency({ id }));
    } catch (error) {
      this.logger.error('Get agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AM_ONBOARD_NEW_AGENCY_PARTNERS)
  @Put(':id')
  async updateAgency(
    @Param('id') id: string,
    @Body() payload: UpdateAgencyRequest,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[]
  ) {
    try {
      payload.id = id;
      payload.userId = String(userId);
      payload.userRole = roles[0];

      return await firstValueFrom(this.agencyService.updateAgency(payload));
    } catch (error) {
      this.logger.error('Update agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Permissions(UserPermissions.AM_ONBOARD_NEW_AGENCY_PARTNERS)
  @Delete(':id')
  async deleteAgency(@Param('id') id: string) {
    try {
      return await firstValueFrom(this.agencyService.deleteAgency({ id }));
    } catch (error) {
      this.logger.error('Delete agency failed', error.stack);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // ✅ Updated Controller Method
  @Permissions(UserPermissions.AM_TRACK_AGENCY_PERFORMANCE)
  @Get()
  async listAgencies(@Query() query: ListAgencyRequest) {
    try {
      // ✅ Validate and set default values for all parameters
      const queryParams: ListAgencyRequest = {
        page: Number(query.page) > 0 ? Number(query.page) : 1,
        pageSize:
          Number(query.pageSize) > 0
            ? Math.min(Number(query.pageSize), 100)
            : 10, // Cap at 100
        search: query.search?.trim() || undefined,
        country: query.country?.trim() || undefined,
        status: query.status?.trim() || undefined,
        fromDate: query.fromDate?.trim() || undefined,
        toDate: query.toDate?.trim() || undefined,
        sortBy: query.sortBy?.trim() || 'createdAt',

      };

      // ✅ Remove undefined values to keep the request clean
      Object.keys(queryParams).forEach((key) => {
        if (queryParams[key] === undefined) {
          delete queryParams[key];
        }
      });

      this.logger.log('Listing agencies with params:', queryParams);

      return await firstValueFrom(this.agencyService.listAgencies(queryParams));
    } catch (error) {
      this.logger.error('List agencies failed', error.stack);

      // ✅ Better error handling with specific error types
      if (error.code === 'GRPC_ERROR') {
        throw new HttpException(
          'Service temporarily unavailable',
          HttpStatus.SERVICE_UNAVAILABLE
        );
      }

      throw new HttpException(
        error.message || 'Failed to fetch agencies',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
