import { Observable } from 'rxjs';
import { AgencyBranch } from '../branch/branch.interface';
import { AgencyContact } from '../contact/agency-contact.interface';
import { Get } from '@nestjs/common';

// ======================= Entity =======================
export interface Agency {
  id: string;
  agencyName: string;
  agencyLogo: string;
  address: string;
  agencySize: string;
  about: string;
  primaryContactNumber: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  agencyRegistrationNumber: string;
  websiteUrl: string;
  email: string;
  status: string; // ✅ make sure this is included
  branchCount?: number; // ✅ new field added to support count from list API
  keyAccountManager?: string;
  disContinuationDate?: string;
  newStudentEnrollment?: number;
  applicationSubmitted?: number;
  transferredProcessed?: number;
  createdAt: string;
  updatedAt: string;
}

export interface SingleAgency {
  id: string;
  agencyName: string;
  agencyLogo: string;
  address: string;
  agencySize: string;
  about: string;
  primaryContactNumber: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  agencyRegistrationNumber: string;
  websiteUrl: string;
  email: string;
  status: string; // ✅ make sure this is included
  branches: AgencyBranch[]; // ✅ reused from sibling module
  contacts: AgencyContact[]; // ✅ reused if defined separately
  createdAt: string;
  updatedAt: string;
}

// ======================= Error =======================
export interface ValidationError {
  field: string;
  message: string;
  constraint: string;
}

export interface ErrorResponse {
  code: string;
  message: string;
  details: string;
  validationErrors?: ValidationError[];
}

// ======================= Create =======================
export interface CreateAgencyRequest {
  agencyName: string;
  agencyLogo: string;
  address: string;
  agencySize: string;
  about: string;
  primaryContactNumber: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  agencyRegistrationNumber: string;
  websiteUrl: string;
  email: string;
  // User creation fields
  ownerFirstName?: string;
  ownerLastName?: string;
  password?: string;
  userId: string;
  userRole: string;
}

export interface CreateAgencyResponse {
  status: number;
  message: string;
  data: Agency;
  error: ErrorResponse;
}

// ======================= Get =======================
export interface GetAgencyRequest {
  id: string;
}

export interface GetAgencyResponse {
  status: number;
  message: string;
  data: Agency;
  error: ErrorResponse;
}

export interface GetSingleAgencyResponse {
  status: number;
  message: string;
  data: SingleAgency;
  error: ErrorResponse;
}

// ======================= Update =======================
export interface UpdateAgencyRequest {
  id: string;
  agencyName?: string;
  agencyLogo?: string;
  address?: string;
  agencySize?: string;
  about?: string;
  primaryContactNumber?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
  agencyRegistrationNumber?: string;
  websiteUrl?: string;
  email?: string;
  userId?: string;
  userRole?: string;
}

export interface UpdateAgencyResponse {
  status: number;
  message: string;
  data: Agency;
  error: ErrorResponse;
}

// ======================= Delete =======================
export interface DeleteAgencyRequest {
  id: string;
}

export interface DeleteAgencyResponse {
  status: number;
  message: string;
  error: ErrorResponse;
}

// ======================= List =======================
export interface ListAgencyRequest {
  page?: number;
  pageSize?: number;
  search?: string;
  country?: string;
  status?: string;
  fromDate?: string;
  toDate?: string;
  sortBy?: string;
}

export interface ListAgencyResponse {
  status: number;
  message: string;
  data: Agency[]; // or a more extended interface if needed (e.g., with branchCount)
  total: number;
  page: number;
  pageSize: number;
  error: ErrorResponse | null;
}

// ======================= gRPC Service =======================
export interface AgencyGrpcService {
  createAgency(data: CreateAgencyRequest): Observable<CreateAgencyResponse>;
  getAgency(data: GetAgencyRequest): Observable<GetSingleAgencyResponse>;
  updateAgency(data: UpdateAgencyRequest): Observable<UpdateAgencyResponse>;
  deleteAgency(data: DeleteAgencyRequest): Observable<DeleteAgencyResponse>;
  listAgencies(data: ListAgencyRequest): Observable<ListAgencyResponse>;
}
