# Production Environment Configuration
# Copy this file to .env.production and fill in your actual values

# Database Configuration (Production PostgreSQL)
DB_HOST=applygoal-prod-do-user-24915658-0.g.db.ondigitalocean.com
DB_PORT=25060
DB_NAME=applyGoalPortal
DB_USER=postgres
DB_PASSWORD=postgres123

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# RabbitMQ Configuration
RABBITMQ_USER=rabbitmq_user
RABBITMQ_PASS=rabbitmq_pass

# S3/Cloudflare R2 Configuration (Production)
STORAGE_ENABLED=true
S3_ENDPOINT=https://0e3a85987d1b06514e83779c3527c246.r2.cloudflarestorage.com
S3_PUBLIC_ENDPOINT=https://0e3a85987d1b06514e83779c3527c246.r2.cloudflarestorage.com
S3_BUCKET=applygoal-files
S3_ACCESS_KEY=6e474b6e1aa7e62f5abb400211fee9a9
S3_SECRET_KEY=d28af234407cd1abd0fb26e07c5dbaf398f98e54da829dc7dc535f1746db8f1c
S3_REGION=auto
S3_FORCE_PATH_STYLE=true

# JWT Configuration
JWT_SECRET=applygoal-super-secret-jwt-key-2024

# Google OAuth Configuration (for Gmail API)
GMAIL_CLIENT_ID=your-gmail-client-id-here
GMAIL_CLIENT_SECRET=your-gmail-client-secret-here
GMAIL_REFRESH_TOKEN=your-gmail-refresh-token-here
GMAIL_SENDER=<EMAIL>

# Google OAuth Configuration (for authentication)
GOOGLE_CLIENT_ID=1008827733358-nok1v02dqkfgkh54pb0337fitvdktjf3.apps.googleusercontent.com

# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123

# NEXTAUTH Configuration
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=https://crm.applygoal.com

# Microservices Database Configuration (Production)
AUDIT_DB_HOST=applygoal-prod-do-user-24915658-0.g.db.ondigitalocean.com
AUDIT_DB_PORT=25060
AUDIT_DB_NAME=audit_db
AUDIT_DB_USER=audit_user
AUDIT_DB_PASSWORD=audit_pass

AUTH_DB_HOST=applygoal-prod-do-user-24915658-0.g.db.ondigitalocean.com
AUTH_DB_PORT=25060
AUTH_DB_NAME=auth_db
AUTH_DB_USER=auth_user
AUTH_DB_PASSWORD=auth_pass

STUDENTS_DB_HOST=applygoal-prod-do-user-24915658-0.g.db.ondigitalocean.com
STUDENTS_DB_PORT=25060
STUDENTS_DB_NAME=students_db
STUDENTS_DB_USER=students_user
STUDENTS_DB_PASSWORD=students_pass

UNIVERSITY_DB_HOST=applygoal-prod-do-user-24915658-0.g.db.ondigitalocean.com
UNIVERSITY_DB_PORT=25060
UNIVERSITY_DB_NAME=university_db
UNIVERSITY_DB_USER=university_user
UNIVERSITY_DB_PASSWORD=university_pass